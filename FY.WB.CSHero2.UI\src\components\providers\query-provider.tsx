"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // Prevent refetching immediately on the client
        staleTime: 60 * 1000, // 1 minute
        // Don't retry on server
        retry: (failureCount, error) => {
          // Don't retry during SSR
          if (typeof window === 'undefined') return false;
          // Retry up to 3 times on client
          return failureCount < 3;
        },
        // Prevent refetch on window focus during development
        refetchOnWindowFocus: false,
        // Prevent refetch on reconnect during development
        refetchOnReconnect: false,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
