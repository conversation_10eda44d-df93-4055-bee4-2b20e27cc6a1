using FY.WB.CSHero2.Infrastructure.Models;
using FY.WB.CSHero2.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Infrastructure.Services;

public interface IReportDataService
{
    Task<string> SaveReportDataAsync(Guid reportId, object reportData, string tenantId);
    Task<VersionedReportDataDocument?> GetReportDataAsync(Guid reportId, string tenantId);
    Task DeleteReportDataAsync(Guid reportId, string tenantId);
    Task<VersionedReportDataDocument> UpdateReportDataAsync(Guid reportId, object reportData, string tenantId);
}

public class ReportDataService : IReportDataService
{
    private readonly ICosmosDbService _cosmosDbService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<ReportDataService> _logger;

    public ReportDataService(
        ICosmosDbService cosmosDbService,
        ApplicationDbContext dbContext,
        ILogger<ReportDataService> logger)
    {
        _cosmosDbService = cosmosDbService;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<string> SaveReportDataAsync(Guid reportId, object reportData, string tenantId)
    {
        try
        {
            _logger.LogInformation("Saving report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            // Convert the frontend report data to our Cosmos DB document format
            var document = await ConvertToVersionedReportDataDocument(reportId, reportData, tenantId);
            
            // Save to Cosmos DB
            var savedDocument = await _cosmosDbService.UpsertItemAsync(document, tenantId);
            
            _logger.LogInformation("Successfully saved report data to Cosmos DB for ReportId: {ReportId}", reportId);
            
            return savedDocument.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task<VersionedReportDataDocument?> GetReportDataAsync(Guid reportId, string tenantId)
    {
        try
        {
            _logger.LogInformation("Getting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            var document = await _cosmosDbService.GetItemAsync<VersionedReportDataDocument>(reportId.ToString(), tenantId);
            
            if (document != null)
            {
                _logger.LogInformation("Successfully retrieved report data from Cosmos DB for ReportId: {ReportId}", reportId);
            }
            else
            {
                _logger.LogWarning("No report data found in Cosmos DB for ReportId: {ReportId}", reportId);
            }
            
            return document;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task DeleteReportDataAsync(Guid reportId, string tenantId)
    {
        try
        {
            _logger.LogInformation("Deleting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            await _cosmosDbService.DeleteItemAsync(reportId.ToString(), tenantId);
            
            _logger.LogInformation("Successfully deleted report data from Cosmos DB for ReportId: {ReportId}", reportId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    public async Task<VersionedReportDataDocument> UpdateReportDataAsync(Guid reportId, object reportData, string tenantId)
    {
        try
        {
            _logger.LogInformation("Updating report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);

            // Get existing document to preserve version and creation date
            var existingDocument = await GetReportDataAsync(reportId, tenantId);
            
            var document = await ConvertToVersionedReportDataDocument(reportId, reportData, tenantId);
            
            if (existingDocument != null)
            {
                document.Metadata.CreatedAt = existingDocument.Metadata.CreatedAt;
                document.VersionNumber = existingDocument.VersionNumber + 1;
            }
            
            document.Metadata.UpdatedAt = DateTime.UtcNow;
            
            var savedDocument = await _cosmosDbService.UpsertItemAsync(document, tenantId);
            
            _logger.LogInformation("Successfully updated report data in Cosmos DB for ReportId: {ReportId}", reportId);
            
            return savedDocument;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report data for ReportId: {ReportId}, TenantId: {TenantId}", reportId, tenantId);
            throw;
        }
    }

    private async Task<VersionedReportDataDocument> ConvertToVersionedReportDataDocument(Guid reportId, object reportData, string tenantId)
    {
        // Get report information from SQL database
        var report = await _dbContext.Reports
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(r => r.Id == reportId);

        var document = new VersionedReportDataDocument
        {
            Id = reportId.ToString(),
            ReportId = reportId,
            TenantId = tenantId,
            ReportName = report?.Name ?? "Unknown Report",
            ReportNumber = report?.ReportNumber ?? "",
            Category = report?.Category ?? "",
            Status = "Active",
            Author = "System", // TODO: Get from user context when available
            ClientId = report?.ClientId ?? Guid.Empty,
            ClientName = "", // Will be populated from client lookup if needed
            Metadata = new ReportDataMetadata
            {
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Version = "1.0"
            }
        };

        // Handle dynamic object conversion
        if (reportData is System.Text.Json.JsonElement jsonElement)
        {
            // Convert from System.Text.Json to our model
            ConvertFromJsonElement(document, jsonElement);
        }
        else if (reportData != null)
        {
            // Try to convert using reflection or serialization
            var json = System.Text.Json.JsonSerializer.Serialize(reportData);
            var jsonDoc = System.Text.Json.JsonDocument.Parse(json);
            ConvertFromJsonElement(document, jsonDoc.RootElement);
        }

        return document;
    }

    private void ConvertFromJsonElement(VersionedReportDataDocument document, System.Text.Json.JsonElement element)
    {
        if (element.TryGetProperty("reportName", out var reportName))
        {
            document.ReportName = reportName.GetString() ?? document.ReportName;
        }
        
        if (element.TryGetProperty("clientId", out var clientId) && clientId.TryGetGuid(out var clientGuid))
        {
            document.ClientId = clientGuid;
        }
        
        if (element.TryGetProperty("clientName", out var clientName))
        {
            document.ClientName = clientName.GetString() ?? "";
        }

        if (element.TryGetProperty("content", out var content) && 
            content.TryGetProperty("sections", out var sections))
        {
            document.Sections = ConvertSections(sections);
        }

        // Store the complete JSON data for frontend compatibility
        document.JsonData = element;
        
        // Extract component data if present
        if (element.TryGetProperty("componentData", out var componentData))
        {
            document.ComponentDataJson = componentData;
        }
    }

    private List<ReportDataSection> ConvertSections(System.Text.Json.JsonElement sectionsElement)
    {
        var sections = new List<ReportDataSection>();
        
        if (sectionsElement.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var sectionElement in sectionsElement.EnumerateArray())
            {
                var section = new ReportDataSection();
                
                if (sectionElement.TryGetProperty("id", out var id) && id.TryGetGuid(out var sectionGuid))
                {
                    section.Id = sectionGuid;
                }
                
                if (sectionElement.TryGetProperty("section-title", out var title))
                {
                    section.Title = title.GetString() ?? "";
                }
                else if (sectionElement.TryGetProperty("title", out var titleAlt))
                {
                    section.Title = titleAlt.GetString() ?? "";
                }
                
                if (sectionElement.TryGetProperty("type", out var type))
                {
                    section.SectionType = type.GetString() ?? "";
                }

                if (sectionElement.TryGetProperty("name", out var name))
                {
                    section.Name = name.GetString() ?? "";
                }

                if (sectionElement.TryGetProperty("description", out var description))
                {
                    section.Description = description.GetString() ?? "";
                }
                
                sections.Add(section);
            }
        }
        
        return sections;
    }
}
