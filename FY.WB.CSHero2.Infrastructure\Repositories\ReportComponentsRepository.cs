using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Models.MultiStorage;
using FY.WB.CSHero2.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace FY.WB.CSHero2.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for Azure Blob Storage operations (rendered components)
    /// </summary>
    public class ReportComponentsRepository : IReportComponentsRepository
    {
        private readonly BlobContainerClient _containerClient;
        private readonly BlobStorageOptions _options;
        private readonly ILogger<ReportComponentsRepository> _logger;

        public ReportComponentsRepository(IOptions<BlobStorageOptions> options, ILogger<ReportComponentsRepository> logger)
        {
            _options = options.Value;
            _logger = logger;
            
            var blobServiceClient = new BlobServiceClient(_options.ConnectionString);
            _containerClient = blobServiceClient.GetBlobContainerClient(_options.ContainerName);
        }

        #region Component Storage Operations

        public async Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<ReportComponent> components, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobId = GenerateComponentsBlobId(reportId, versionId);
                _logger.LogDebug("Saving components to blob {BlobId}", blobId);

                var componentsList = components.ToList();
                var metadata = new ComponentsMetadata
                {
                    ReportId = reportId.ToString(),
                    VersionId = versionId.ToString(),
                    GeneratedAt = DateTime.UtcNow,
                    Framework = "NextJS",
                    StyleFramework = "TailwindCSS",
                    Components = componentsList.Select(c => new ComponentMetadata
                    {
                        Id = c.Id,
                        Name = c.Name,
                        SectionId = c.SectionId,
                        FileName = $"{c.Name}.tsx",
                        Size = Encoding.UTF8.GetByteCount(c.Code),
                        Hash = ComputeHash(c.Code),
                        Imports = c.Imports,
                        Props = c.Props,
                        GeneratedAt = DateTime.UtcNow
                    }).ToList()
                };

                metadata.TotalSize = metadata.Components.Sum(c => c.Size);

                // Save metadata
                await SaveMetadataAsync(blobId, metadata, cancellationToken);

                // Save individual components
                foreach (var component in componentsList)
                {
                    await SaveIndividualComponentAsync(blobId, component, cancellationToken);
                }

                _logger.LogInformation("Successfully saved {Count} components to blob {BlobId}", componentsList.Count, blobId);
                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving components for report {ReportId}, version {VersionId}", reportId, versionId);
                throw;
            }
        }

        public async Task<ComponentsMetadata?> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting components metadata for blob {BlobId}", blobId);

                var metadataPath = GenerateMetadataPath(blobId);
                var blobClient = _containerClient.GetBlobClient(metadataPath);

                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogDebug("Components metadata not found for blob {BlobId}", blobId);
                    return null;
                }

                var response = await blobClient.DownloadContentAsync(cancellationToken);
                var json = response.Value.Content.ToString();
                var metadata = JsonSerializer.Deserialize<ComponentsMetadata>(json);

                _logger.LogDebug("Successfully retrieved components metadata for blob {BlobId}", blobId);
                return metadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting components metadata for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<IEnumerable<ReportComponent>> GetComponentsAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting components for blob {BlobId}", blobId);

                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                if (metadata == null)
                {
                    return Enumerable.Empty<ReportComponent>();
                }

                var components = new List<ReportComponent>();
                foreach (var componentMeta in metadata.Components)
                {
                    var component = await GetComponentAsync(blobId, componentMeta.Name, cancellationToken);
                    if (component != null)
                    {
                        components.Add(component);
                    }
                }

                _logger.LogDebug("Successfully retrieved {Count} components for blob {BlobId}", components.Count, blobId);
                return components;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting components for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<ReportComponent?> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting component {ComponentName} from blob {BlobId}", componentName, blobId);

                var componentPath = GenerateComponentPath(blobId, componentName);
                var blobClient = _containerClient.GetBlobClient(componentPath);

                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogDebug("Component {ComponentName} not found in blob {BlobId}", componentName, blobId);
                    return null;
                }

                var response = await blobClient.DownloadContentAsync(cancellationToken);
                var json = response.Value.Content.ToString();
                var component = JsonSerializer.Deserialize<ReportComponent>(json);

                _logger.LogDebug("Successfully retrieved component {ComponentName} from blob {BlobId}", componentName, blobId);
                return component;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting component {ComponentName} from blob {BlobId}", componentName, blobId);
                throw;
            }
        }

        public async Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting components for blob {BlobId}", blobId);

                var prefix = GenerateBlobPrefix(blobId);
                var blobs = _containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken);

                var deleteTasks = new List<Task>();
                await foreach (var blob in blobs)
                {
                    var blobClient = _containerClient.GetBlobClient(blob.Name);
                    deleteTasks.Add(blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken));
                }

                await Task.WhenAll(deleteTasks);

                _logger.LogInformation("Successfully deleted components for blob {BlobId}", blobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting components for blob {BlobId}", blobId);
                throw;
            }
        }

        #endregion

        #region Individual Component Operations

        public async Task<string> SaveComponentAsync(Guid reportId, Guid versionId, ReportComponent component, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobId = GenerateComponentsBlobId(reportId, versionId);
                _logger.LogDebug("Saving individual component {ComponentName} to blob {BlobId}", component.Name, blobId);

                await SaveIndividualComponentAsync(blobId, component, cancellationToken);

                _logger.LogInformation("Successfully saved component {ComponentName} to blob {BlobId}", component.Name, blobId);
                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving component {ComponentName} for report {ReportId}, version {VersionId}", component.Name, reportId, versionId);
                throw;
            }
        }

        public async Task<string> GetComponentCodeAsync(string blobId, string componentName, CancellationToken cancellationToken = default)
        {
            try
            {
                var component = await GetComponentAsync(blobId, componentName, cancellationToken);
                return component?.Code ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting component code for {ComponentName} from blob {BlobId}", componentName, blobId);
                throw;
            }
        }

        public async Task UpdateComponentAsync(string blobId, ReportComponent component, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating component {ComponentName} in blob {BlobId}", component.Name, blobId);

                await SaveIndividualComponentAsync(blobId, component, cancellationToken);

                // Update metadata
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                if (metadata != null)
                {
                    var componentMeta = metadata.Components.FirstOrDefault(c => c.Name == component.Name);
                    if (componentMeta != null)
                    {
                        componentMeta.Size = Encoding.UTF8.GetByteCount(component.Code);
                        componentMeta.Hash = ComputeHash(component.Code);
                        componentMeta.GeneratedAt = DateTime.UtcNow;
                        componentMeta.Imports = component.Imports;
                        componentMeta.Props = component.Props;

                        metadata.TotalSize = metadata.Components.Sum(c => c.Size);
                        await SaveMetadataAsync(blobId, metadata, cancellationToken);
                    }
                }

                _logger.LogInformation("Successfully updated component {ComponentName} in blob {BlobId}", component.Name, blobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating component {ComponentName} in blob {BlobId}", component.Name, blobId);
                throw;
            }
        }

        #endregion

        #region Utility Operations

        public async Task<bool> ComponentsExistAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadataPath = GenerateMetadataPath(blobId);
                var blobClient = _containerClient.GetBlobClient(metadataPath);
                return await blobClient.ExistsAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if components exist for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<long> GetComponentsSizeAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                return metadata?.TotalSize ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting components size for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<ComponentStorageResult> GetStorageResultAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                if (metadata == null)
                {
                    return new ComponentStorageResult
                    {
                        BlobId = blobId,
                        Success = false,
                        ErrorMessage = "Components not found"
                    };
                }

                return new ComponentStorageResult
                {
                    BlobId = blobId,
                    ComponentCount = metadata.Components.Count,
                    TotalSize = metadata.TotalSize,
                    Metadata = metadata,
                    Success = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting storage result for blob {BlobId}", blobId);
                return new ComponentStorageResult
                {
                    BlobId = blobId,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<IEnumerable<string>> ListComponentBlobsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Listing component blobs for report {ReportId}", reportId);

                var prefix = $"components/{reportId}/";
                var blobs = _containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken);

                var blobIds = new List<string>();
                await foreach (var blob in blobs)
                {
                    if (blob.Name.EndsWith("/metadata.json"))
                    {
                        var blobId = ExtractBlobIdFromPath(blob.Name);
                        if (!string.IsNullOrEmpty(blobId))
                        {
                            blobIds.Add(blobId);
                        }
                    }
                }

                _logger.LogDebug("Found {Count} component blobs for report {ReportId}", blobIds.Count, reportId);
                return blobIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing component blobs for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task DeleteAllComponentsForReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting all components for report {ReportId}", reportId);

                var prefix = $"components/{reportId}/";
                var blobs = _containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken);

                var deleteTasks = new List<Task>();
                await foreach (var blob in blobs)
                {
                    var blobClient = _containerClient.GetBlobClient(blob.Name);
                    deleteTasks.Add(blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken));
                }

                await Task.WhenAll(deleteTasks);

                _logger.LogInformation("Successfully deleted all components for report {ReportId}", reportId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting all components for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private string GenerateComponentsBlobId(Guid reportId, Guid versionId)
        {
            return $"components-{reportId}-{versionId}";
        }

        private string GenerateBlobPrefix(string blobId)
        {
            return $"components/{blobId}/";
        }

        private string GenerateMetadataPath(string blobId)
        {
            return $"{GenerateBlobPrefix(blobId)}metadata.json";
        }

        private string GenerateComponentPath(string blobId, string componentName)
        {
            return $"{GenerateBlobPrefix(blobId)}{componentName}.json";
        }

        private string ExtractBlobIdFromPath(string blobPath)
        {
            // Extract blob ID from path like "components/components-guid-guid/metadata.json"
            var parts = blobPath.Split('/');
            if (parts.Length >= 2)
            {
                return parts[1];
            }
            return string.Empty;
        }

        private async Task SaveMetadataAsync(string blobId, ComponentsMetadata metadata, CancellationToken cancellationToken)
        {
            var metadataPath = GenerateMetadataPath(blobId);
            var blobClient = _containerClient.GetBlobClient(metadataPath);

            var json = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(json));

            var uploadOptions = new BlobUploadOptions
            {
                HttpHeaders = new BlobHttpHeaders
                {
                    ContentType = "application/json"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["reportId"] = metadata.ReportId,
                    ["versionId"] = metadata.VersionId,
                    ["tenantId"] = metadata.TenantId,
                    ["generatedAt"] = metadata.GeneratedAt.ToString("O"),
                    ["componentCount"] = metadata.Components.Count.ToString(),
                    ["totalSize"] = metadata.TotalSize.ToString()
                }
            };

            await blobClient.UploadAsync(stream, uploadOptions, cancellationToken);
        }

        private async Task SaveIndividualComponentAsync(string blobId, ReportComponent component, CancellationToken cancellationToken)
        {
            var componentPath = GenerateComponentPath(blobId, component.Name);
            var blobClient = _containerClient.GetBlobClient(componentPath);

            var json = JsonSerializer.Serialize(component, new JsonSerializerOptions { WriteIndented = true });
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(json));

            var uploadOptions = new BlobUploadOptions
            {
                HttpHeaders = new BlobHttpHeaders
                {
                    ContentType = "application/json"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["componentId"] = component.Id,
                    ["componentName"] = component.Name,
                    ["sectionId"] = component.SectionId,
                    ["size"] = Encoding.UTF8.GetByteCount(component.Code).ToString(),
                    ["hash"] = ComputeHash(component.Code)
                }
            };

            await blobClient.UploadAsync(stream, uploadOptions, cancellationToken);
        }

        private string ComputeHash(string content)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(content);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }

        #endregion
    }
}
