﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FY.WB.CSHero2", "FY.WB.CSHero2.csproj", "{C6607AED-EE11-4E43-87FD-965A15C62B70}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FY.WB.CSHero2.Domain", "..\FY.WB.CSHero2.Domain\FY.WB.CSHero2.Domain.csproj", "{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FY.WB.CSHero2.Application", "..\FY.WB.CSHero2.Application\FY.WB.CSHero2.Application.csproj", "{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FY.WB.CSHero2.Infrastructure", "..\FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj", "{D0A70B28-DA99-46C9-9175-4A30EF0C5364}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|x64.Build.0 = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Debug|x86.Build.0 = Debug|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|x64.ActiveCfg = Release|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|x64.Build.0 = Release|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|x86.ActiveCfg = Release|Any CPU
		{C6607AED-EE11-4E43-87FD-965A15C62B70}.Release|x86.Build.0 = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|x64.Build.0 = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Debug|x86.Build.0 = Debug|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|x64.ActiveCfg = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|x64.Build.0 = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|x86.ActiveCfg = Release|Any CPU
		{C35FF4EB-CDFB-4575-91F5-9695C0D0CBD6}.Release|x86.Build.0 = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|x64.Build.0 = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Debug|x86.Build.0 = Debug|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|x64.ActiveCfg = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|x64.Build.0 = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|x86.ActiveCfg = Release|Any CPU
		{7F4EF577-7CBC-4576-89DE-6009F4E8E9D2}.Release|x86.Build.0 = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|x64.Build.0 = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Debug|x86.Build.0 = Debug|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|x64.ActiveCfg = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|x64.Build.0 = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|x86.ActiveCfg = Release|Any CPU
		{D0A70B28-DA99-46C9-9175-4A30EF0C5364}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {69E367E8-80D1-4AEE-B3EF-5494EDC87C01}
	EndGlobalSection
EndGlobal
