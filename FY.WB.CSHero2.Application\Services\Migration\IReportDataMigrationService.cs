using FY.WB.CSHero2.Application.Models.Migration;

namespace FY.WB.CSHero2.Application.Services.Migration
{
    /// <summary>
    /// Service interface for migrating report data from SQL to multi-storage architecture
    /// </summary>
    public interface IReportDataMigrationService
    {
        /// <summary>
        /// Migrates all reports from SQL JSON storage to multi-storage architecture
        /// </summary>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration result with statistics and status</returns>
        Task<MigrationResult> MigrateAllReportsAsync(
            MigrationOptions? options = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrates a specific report and all its versions
        /// </summary>
        /// <param name="reportId">Report ID to migrate</param>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration result for the specific report</returns>
        Task<MigrationResult> MigrateReportAsync(
            Guid reportId, 
            MigrationOptions? options = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrates a specific report version
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID to migrate</param>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration result for the specific version</returns>
        Task<MigrationResult> MigrateReportVersionAsync(
            Guid reportId, 
            Guid versionId, 
            MigrationOptions? options = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current migration status and progress
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Current migration status</returns>
        Task<MigrationStatus> GetMigrationStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets detailed migration statistics
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration statistics and metrics</returns>
        Task<MigrationStatistics> GetMigrationStatisticsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates that a report has been successfully migrated
        /// </summary>
        /// <param name="reportId">Report ID to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result with details</returns>
        Task<ValidationResult> ValidateMigrationAsync(
            Guid reportId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates that a specific report version has been successfully migrated
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result with details</returns>
        Task<ValidationResult> ValidateVersionMigrationAsync(
            Guid reportId, 
            Guid versionId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back migration for a specific report
        /// </summary>
        /// <param name="reportId">Report ID to rollback</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Rollback result</returns>
        Task<RollbackResult> RollbackReportMigrationAsync(
            Guid reportId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back migration for a specific report version
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID to rollback</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Rollback result</returns>
        Task<RollbackResult> RollbackVersionMigrationAsync(
            Guid reportId, 
            Guid versionId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs a dry run migration without making actual changes
        /// </summary>
        /// <param name="reportId">Optional report ID for specific report dry run</param>
        /// <param name="options">Migration options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dry run result with what would be migrated</returns>
        Task<DryRunResult> PerformDryRunAsync(
            Guid? reportId = null, 
            MigrationOptions? options = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets migration progress for a specific operation
        /// </summary>
        /// <param name="operationId">Migration operation ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration progress information</returns>
        Task<MigrationProgress> GetMigrationProgressAsync(
            Guid operationId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancels an ongoing migration operation
        /// </summary>
        /// <param name="operationId">Migration operation ID to cancel</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cancellation result</returns>
        Task<CancellationResult> CancelMigrationAsync(
            Guid operationId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a list of reports that need migration
        /// </summary>
        /// <param name="tenantId">Optional tenant ID filter</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of reports requiring migration</returns>
        Task<IEnumerable<ReportMigrationInfo>> GetReportsRequiringMigrationAsync(
            Guid? tenantId = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets migration history for a specific report
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Migration history entries</returns>
        Task<IEnumerable<MigrationHistoryEntry>> GetMigrationHistoryAsync(
            Guid reportId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cleans up failed migration artifacts
        /// </summary>
        /// <param name="reportId">Optional report ID for specific cleanup</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cleanup result</returns>
        Task<CleanupResult> CleanupFailedMigrationAsync(
            Guid? reportId = null, 
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Service interface for transforming data during migration
    /// </summary>
    public interface IDataTransformationService
    {
        /// <summary>
        /// Transforms SQL JSON data to Cosmos DB report data structure
        /// </summary>
        /// <param name="jsonData">SQL JSON data</param>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Transformed report data for Cosmos DB</returns>
        Task<ReportDataTransformResult> TransformToReportDataAsync(
            string jsonData, 
            Guid reportId, 
            Guid versionId, 
            Guid tenantId);

        /// <summary>
        /// Transforms component JSON data to blob storage components
        /// </summary>
        /// <param name="componentDataJson">Component JSON data</param>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID</param>
        /// <returns>Transformed component definitions</returns>
        Task<ComponentTransformResult> TransformToComponentsAsync(
            string componentDataJson, 
            Guid reportId, 
            Guid versionId);

        /// <summary>
        /// Validates JSON data structure before transformation
        /// </summary>
        /// <param name="jsonData">JSON data to validate</param>
        /// <param name="dataType">Type of data (report or component)</param>
        /// <returns>Validation result</returns>
        Task<DataValidationResult> ValidateJsonDataAsync(
            string jsonData, 
            JsonDataType dataType);

        /// <summary>
        /// Extracts sections from legacy JSON structure
        /// </summary>
        /// <param name="jsonData">Legacy JSON data</param>
        /// <returns>Extracted sections</returns>
        Task<IEnumerable<ReportSectionTransformResult>> ExtractSectionsAsync(string jsonData);

        /// <summary>
        /// Extracts fields from section data
        /// </summary>
        /// <param name="sectionData">Section data object</param>
        /// <param name="sectionId">Section ID</param>
        /// <returns>Extracted fields</returns>
        Task<IEnumerable<ReportFieldTransformResult>> ExtractFieldsAsync(
            object sectionData, 
            string sectionId);

        /// <summary>
        /// Infers field type from content
        /// </summary>
        /// <param name="content">Field content</param>
        /// <param name="fieldName">Field name for context</param>
        /// <returns>Inferred field type</returns>
        string InferFieldType(object content, string fieldName);

        /// <summary>
        /// Sanitizes and validates field content
        /// </summary>
        /// <param name="content">Raw field content</param>
        /// <param name="fieldType">Field type</param>
        /// <returns>Sanitized content</returns>
        string SanitizeFieldContent(object content, string fieldType);
    }

    /// <summary>
    /// Service interface for migration validation
    /// </summary>
    public interface IMigrationValidationService
    {
        /// <summary>
        /// Validates data integrity after migration
        /// </summary>
        /// <param name="reportId">Report ID to validate</param>
        /// <param name="versionId">Version ID to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateDataIntegrityAsync(
            Guid reportId, 
            Guid versionId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates cross-storage references
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Reference validation result</returns>
        Task<ReferenceValidationResult> ValidateCrossStorageReferencesAsync(
            Guid reportId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Compares original and migrated data
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Data comparison result</returns>
        Task<DataComparisonResult> CompareOriginalAndMigratedDataAsync(
            Guid reportId, 
            Guid versionId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates storage connectivity and permissions
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Storage validation result</returns>
        Task<StorageValidationResult> ValidateStorageConnectivityAsync(
            CancellationToken cancellationToken = default);
    }
}