﻿{
    "CosmosDb":  {
                     "RequestTimeoutSeconds":  30,
                     "DatabaseName":  "ReportRenderingEngine",
                     "MaxConnections":  50,
                     "ConnectionString":  "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=****************************************************************************************;",
                     "ContainerName":  "Reports",
                     "MaxRetryAttempts":  3
                 },
    "BlobStorage":  {
                        "ContainerName":  "report-components",
                        "EnableEncryption":  true,
                        "ConnectionString":  "DefaultEndpointsProtocol=https;EndpointSuffix=core.windows.net;AccountName=csheroblobstorage;AccountKey=****************************************************************************************;BlobEndpoint=https://csheroblobstorage.blob.core.windows.net/;FileEndpoint=https://csheroblobstorage.file.core.windows.net/;QueueEndpoint=https://csheroblobstorage.queue.core.windows.net/;TableEndpoint=https://csheroblobstorage.table.core.windows.net/",
                        "ReportDataContainer":  "report-data",
                        "DefaultContentType":  "application/json",
                        "MaxConcurrentOperations":  10,
                        "MaxRetryAttempts":  3,
                        "RequestTimeoutSeconds":  30
                    }
}
