using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// Repository for managing report metadata in SQL Database
    /// Implements CRUD operations for reports, versions, and styles
    /// </summary>
    public class ReportMetadataRepository : IReportMetadataRepository
    {
        private readonly IApplicationDbContext _context;
        private readonly ILogger<ReportMetadataRepository> _logger;

        public ReportMetadataRepository(
            IApplicationDbContext context,
            ILogger<ReportMetadataRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Report operations

        public async Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving report {ReportId}", reportId);

                var report = await _context.Reports
                    .IgnoreAutoIncludes()
                    .Include(r => r.Versions)
                    .Include(r => r.Sections)
                    .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

                if (report != null)
                {
                    _logger.LogDebug("Successfully retrieved report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Report {ReportId} not found", reportId);
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<Report> CreateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report {ReportId} - {ReportName}", report.Id, report.Name);

                _context.Reports.Add(report);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created report {ReportId} - {ReportName}", report.Id, report.Name);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report {ReportId} - {ReportName}", report.Id, report.Name);
                throw;
            }
        }

        public async Task<Report> UpdateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report {ReportId} - {ReportName}", report.Id, report.Name);

                _context.Reports.Update(report);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated report {ReportId} - {ReportName}", report.Id, report.Name);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report {ReportId} - {ReportName}", report.Id, report.Name);
                throw;
            }
        }

        public async Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report {ReportId}", reportId);

                var report = await _context.Reports.FindAsync(new object[] { reportId }, cancellationToken);
                if (report != null)
                {
                    _context.Reports.Remove(report);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Report {ReportId} not found for deletion", reportId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<IEnumerable<Report>> GetReportsAsync(Guid? clientId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving reports for client {ClientId}", clientId);

                var query = _context.Reports
                    .Include(r => r.Versions.Where(v => v.IsCurrent))
                    .AsQueryable();

                if (clientId.HasValue)
                {
                    query = query.Where(r => r.ClientId == clientId.Value);
                }

                var reports = await query.ToListAsync(cancellationToken);

                _logger.LogDebug("Successfully retrieved {ReportCount} reports for client {ClientId}", reports.Count, clientId);
                return reports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving reports for client {ClientId}", clientId);
                throw;
            }
        }

        #endregion

        #region Report Version operations

        public async Task<ReportVersion?> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving report version {VersionId}", versionId);

                var version = await _context.ReportVersions
                    .Include(v => v.Report)
                    .FirstOrDefaultAsync(v => v.Id == versionId, cancellationToken);

                if (version != null)
                {
                    _logger.LogDebug("Successfully retrieved report version {VersionId}", versionId);
                }
                else
                {
                    _logger.LogWarning("Report version {VersionId} not found", versionId);
                }

                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving report version {VersionId}", versionId);
                throw;
            }
        }

        public async Task<ReportVersion?> GetCurrentReportVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving current version for report {ReportId}", reportId);

                var version = await _context.ReportVersions
                    .FirstOrDefaultAsync(v => v.ReportId == reportId && v.IsCurrent, cancellationToken);

                if (version != null)
                {
                    _logger.LogDebug("Successfully retrieved current version {VersionId} for report {ReportId}", version.Id, reportId);
                }
                else
                {
                    _logger.LogWarning("No current version found for report {ReportId}", reportId);
                }

                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current version for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<ReportVersion> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report version {VersionId} for report {ReportId}", version.Id, version.ReportId);

                _context.ReportVersions.Add(version);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created report version {VersionId} for report {ReportId}", version.Id, version.ReportId);
                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report version {VersionId} for report {ReportId}", version.Id, version.ReportId);
                throw;
            }
        }

        public async Task<ReportVersion> UpdateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report version {VersionId}", version.Id);

                _context.ReportVersions.Update(version);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated report version {VersionId}", version.Id);
                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report version {VersionId}", version.Id);
                throw;
            }
        }

        public async Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report version {VersionId}", versionId);

                var version = await _context.ReportVersions.FindAsync(new object[] { versionId }, cancellationToken);
                if (version != null)
                {
                    _context.ReportVersions.Remove(version);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted report version {VersionId}", versionId);
                }
                else
                {
                    _logger.LogWarning("Report version {VersionId} not found for deletion", versionId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report version {VersionId}", versionId);
                throw;
            }
        }

        public async Task<IEnumerable<ReportVersion>> GetReportVersionsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving versions for report {ReportId}", reportId);

                var versions = await _context.ReportVersions
                    .Where(v => v.ReportId == reportId)
                    .OrderByDescending(v => v.VersionNumber)
                    .ToListAsync(cancellationToken);

                _logger.LogDebug("Successfully retrieved {VersionCount} versions for report {ReportId}", versions.Count, reportId);
                return versions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving versions for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Report Style operations

        public async Task<ReportStyle?> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving style for report {ReportId}", reportId);

                var style = await _context.ReportStyles
                    .FirstOrDefaultAsync(s => s.ReportId == reportId, cancellationToken);

                if (style != null)
                {
                    _logger.LogDebug("Successfully retrieved style for report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Style not found for report {ReportId}", reportId);
                }

                return style;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving style for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<ReportStyle> CreateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating style for report {ReportId}", style.ReportId);

                _context.ReportStyles.Add(style);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created style for report {ReportId}", style.ReportId);
                return style;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating style for report {ReportId}", style.ReportId);
                throw;
            }
        }

        public async Task<ReportStyle> UpdateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating style for report {ReportId}", style.ReportId);

                _context.ReportStyles.Update(style);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated style for report {ReportId}", style.ReportId);
                return style;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating style for report {ReportId}", style.ReportId);
                throw;
            }
        }

        public async Task DeleteReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting style for report {ReportId}", reportId);

                var style = await _context.ReportStyles
                    .FirstOrDefaultAsync(s => s.ReportId == reportId, cancellationToken);

                if (style != null)
                {
                    _context.ReportStyles.Remove(style);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted style for report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Style not found for report {ReportId} - nothing to delete", reportId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting style for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Bulk operations

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var changes = await _context.SaveChangesAsync(cancellationToken);
                _logger.LogDebug("Successfully saved {ChangeCount} changes to database", changes);
                return changes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving changes to database");
                throw;
            }
        }

        #endregion
    }
}