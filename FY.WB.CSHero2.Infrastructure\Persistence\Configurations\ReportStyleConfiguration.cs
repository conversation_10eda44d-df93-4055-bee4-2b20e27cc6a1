using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ReportStyleConfiguration : IEntityTypeConfiguration<ReportStyle>
    {
        public void Configure(EntityTypeBuilder<ReportStyle> builder)
        {
            builder.ToTable("ReportStyles");
            builder.HasKey(s => s.Id);
            
            builder.Property(s => s.ReportId)
                .IsRequired();
            
            builder.Property(s => s.Theme)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("modern");
            
            builder.Property(s => s.ColorScheme)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("blue");
            
            builder.Property(s => s.Typography)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("sans-serif");
            
            builder.Property(s => s.Spacing)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("normal");
            
            // JSON properties for style options
            builder.Property(s => s.LayoutOptionsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");
            
            builder.Property(s => s.TypographyOptionsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");
            
            builder.Property(s => s.StructureOptionsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");
            
            builder.Property(s => s.ContentOptionsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");
            
            builder.Property(s => s.VisualOptionsJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired()
                .HasDefaultValue("{}");
            
            // Indexes
            builder.HasIndex(s => s.ReportId)
                .IsUnique()
                .HasDatabaseName("IX_ReportStyles_ReportId");
            
            builder.HasIndex(s => s.Theme)
                .HasDatabaseName("IX_ReportStyles_Theme");
            
            builder.HasIndex(s => s.ColorScheme)
                .HasDatabaseName("IX_ReportStyles_ColorScheme");
            
            builder.HasIndex(s => s.CreationTime)
                .HasDatabaseName("IX_ReportStyles_CreationTime");
            
            // Relationships
            builder.HasOne(s => s.Report)
                .WithOne()
                .HasForeignKey<ReportStyle>(s => s.ReportId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
