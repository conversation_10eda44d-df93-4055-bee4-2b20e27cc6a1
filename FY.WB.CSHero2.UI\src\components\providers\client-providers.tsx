"use client";

import { useEffect, useState } from "react";
import { Toast } from "@/components/ui/toast";
import { ToastProvider } from "@/components/providers/toast-provider";
import { QueryProvider } from "@/components/providers/query-provider";
import { AuthProvider } from "@/components/providers/auth-provider";
import ErrorBoundary from "@/components/providers/error-boundary";

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
    </div>
  );
}

// Improved hydration-safe wrapper that doesn't cause mismatches
function HydrationSafeWrapper({ children }: { children: React.ReactNode }) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // During SSR and initial hydration, render a consistent loading state
  // After hydration completes, render the actual content
  return (
    <>
      {hasMounted ? children : <LoadingFallback />}
    </>
  );
}

export function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <QueryProvider>
        <HydrationSafeWrapper>
          <AuthProvider>
            <ToastProvider>
              {children}
              <Toast />
            </ToastProvider>
          </AuthProvider>
        </HydrationSafeWrapper>
      </QueryProvider>
    </ErrorBoundary>
  );
}
