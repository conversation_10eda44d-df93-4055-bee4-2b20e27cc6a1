# Report Rendering Engine Documentation

## Overview
The Report Rendering Engine is a sophisticated system designed to generate dynamic, data-driven reports using AI-powered rendering. It combines template management, data binding, version control, and multi-format export capabilities to create a comprehensive reporting solution.

## Core Concepts

### 1. Templates vs Reports

**Templates** are reusable report structures that define:
- The overall layout and sections
- Field types and validation rules  
- Styling and theming options
- Default values and rendering instructions

**Reports** are user-specific instances created from templates that contain:
- Bound data values
- Customized content
- Generated Next.js components
- Version history

### 2. Component-Based Architecture
The system generates Next.js React components rather than static HTML, enabling:
- Dynamic interactivity
- Real-time data updates
- Modern web application features
- Responsive design capabilities

## System Architecture

### Core Components
```
┌─────────────────────────────────────────────────────────────┐
│                      User Interface                          │
├─────────────────────────────────────────────────────────────┤
│                     Report Service                           │
├─────────────────────────────────────────────────────────────┤
│  Report Renderer  │  Database Service  │  Export Service    │
├─────────────────────────────────────────────────────────────┤
│  LLM Client  │  Versioning Service  │  XML Serializer      │
└─────────────────────────────────────────────────────────────┘
```

### Key Services
- **ReportRenderer**: Orchestrates the rendering process
- **DatabaseService**: Manages data persistence
- **LLMClient**: Interfaces with AI for component generation
- **VersioningService**: Tracks changes and enables rollbacks
- **ExportService**: Handles conversion to PDF, Word, PowerPoint

## Workflow

### 1. Template Selection and Cloning
```mermaid
graph LR
    A[Template Library] --> B[User Selects Template]
    B --> C[Clone to User Account]
    C --> D[Customize Template]
    D --> E[Create Report Instance]
```

When a user selects a template:
- The system clones the template structure to their account
- User can customize sections, fields, and styling
- A new report instance is created with the customized template

### 2. Report Rendering Process

#### Full Report Rendering
```csharp
// Step 1: Load report and template data
var report = await _database.GetReportAsync(reportId);
var template = await _database.GetTemplateAsync(report.TemplateId);

// Step 2: Build XML render request
var renderRequest = BuildRenderRequest(template, report, null);

// Step 3: Send to LLM for component generation
var xmlPrompt = XmlPromptSerializer.SerializeToXml(renderRequest);
var llmResult = await _llm.GenerateComponentAsync(xmlPrompt);

// Step 4: Save components and data
await SaveRenderResult(reportId, renderResult, isPartialUpdate: false);

// Step 5: Create version snapshot
await _versioning.CreateVersionAsync(reportId, renderResult);
```

#### Partial Section Rendering
When only a section needs updating:
```csharp
// Renders only the specified section
await _renderer.RenderSectionAsync(reportId, "financials-section");
```
This preserves all other components and only updates the targeted section.

### 3. Data Management
The system separates data from presentation:

```json
// Example persisted data structure
{
  "header.title": "Q4 2024 Financial Report",
  "header.date": "2024-12-31",
  "financials.revenue": 1250000,
  "financials.expenses": 980000,
  "charts.revenueChart": {
    "type": "line",
    "data": [...]
  }
}
```

Users can update this data without triggering a re-render:
```csharp
await reportService.UpdateReportDataAsync(reportId, new Dictionary<string, object>
{
    ["financials.revenue"] = 1300000
});
```

### 4. Component Generation
The LLM generates Next.js components like:

```tsx
// Generated component example
export const FinancialsSection = ({ data }) => {
  return (
    <section className="financials-section">
      <h2>Financial Overview</h2>
      <div className="metrics-grid">
        <MetricCard 
          title="Revenue" 
          value={data.revenue} 
          format="currency" 
        />
        <MetricCard 
          title="Expenses" 
          value={data.expenses} 
          format="currency" 
        />
      </div>
    </section>
  );
};
```

## Domain Model

### Enhanced Entity Structure
```csharp
// Template: Reusable template definition
public class Template : FullAuditedMultiTenantEntity<Guid>
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string Category { get; set; }
    public bool IsPublic { get; set; }
    public TemplateStructure Structure { get; set; }
    public List<TemplateSection> Sections { get; set; }
    public ReportStyle DefaultStyle { get; set; }
}

// Report: User instance of template
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    public Guid TemplateId { get; set; }
    public string Name { get; set; }
    public ReportStatus Status { get; set; }
    public Guid? CurrentVersionId { get; set; }
    public List<ReportVersion> Versions { get; set; }
}

// ReportVersion: Version history
public class ReportVersion : Entity<Guid>
{
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    public string Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<ComponentDefinition> Components { get; set; }
    public Dictionary<string, object> JsonData { get; set; }
}

// ComponentDefinition: Generated React components
public class ComponentDefinition : Entity<Guid>
{
    public Guid ReportVersionId { get; set; }
    public string SectionId { get; set; }
    public string ComponentCode { get; set; }
    public string TypeDefinitions { get; set; }
    public List<string> Imports { get; set; }
    public ComponentMetadata Metadata { get; set; }
}
```

### Field Types and Validation
```csharp
public enum FieldType
{
    Text,
    Number,
    Date,
    Currency,
    Percentage,
    Boolean,
    Image,
    Chart,
    Table,
    RichText
}

public class FieldValidation
{
    public bool IsRequired { get; set; }
    public string Pattern { get; set; }
    public object MinValue { get; set; }
    public object MaxValue { get; set; }
    public List<string> AllowedValues { get; set; }
}
```

## Service Interfaces

### Core Services
```csharp
public interface IReportRenderer
{
    Task<ComponentResult> RenderAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ComponentResult> RenderSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
}

public interface ITemplateService
{
    Task<List<Template>> GetPublicTemplatesAsync(CancellationToken cancellationToken = default);
    Task<Report> CloneTemplateAsync(Guid templateId, string reportName, CancellationToken cancellationToken = default);
    Task<Template> CreateTemplateAsync(CreateTemplateRequest request, CancellationToken cancellationToken = default);
}

public interface IReportService
{
    Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(Guid reportId, Dictionary<string, object> data, CancellationToken cancellationToken = default);
    Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);
}

public interface IVersioningService
{
    Task<ReportVersion> CreateVersionAsync(Guid reportId, ComponentResult components, string description = null, CancellationToken cancellationToken = default);
    Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<bool> RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);
}

public interface IExportService
{
    Task<byte[]> ExportToPdfAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);
    Task<byte[]> ExportToPowerPointAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);
    Task<byte[]> ExportToWordAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);
}
```

## API Endpoints

### Report Rendering
```csharp
[Route("api/report-rendering")]
public class ReportRenderingController : ControllerBase
{
    // Full report rendering
    [HttpPost("render/{reportId}")]
    public async Task<ActionResult<ComponentResult>> RenderReportAsync(Guid reportId)
    
    // Partial section rendering  
    [HttpPost("render/{reportId}/section/{sectionId}")]
    public async Task<ActionResult<ComponentResult>> RenderSectionAsync(Guid reportId, string sectionId)
    
    // Data updates without re-rendering
    [HttpPut("reports/{reportId}/data")]
    public async Task<ActionResult> UpdateReportDataAsync(Guid reportId, [FromBody] Dictionary<string, object> data)
    
    // Export functionality
    [HttpPost("reports/{reportId}/export/{format}")]
    public async Task<ActionResult<FileResult>> ExportReportAsync(Guid reportId, string format)
}
```

### Template Management
```csharp
[Route("api/templates")]
public class TemplateController : ControllerBase
{
    [HttpGet("public")]
    public async Task<ActionResult<List<Template>>> GetPublicTemplatesAsync()
    
    [HttpPost("{templateId}/clone")]
    public async Task<ActionResult<Report>> CloneTemplateAsync(Guid templateId, [FromBody] CloneTemplateRequest request)
    
    [HttpGet("user")]
    public async Task<ActionResult<List<Template>>> GetUserTemplatesAsync()
}
```

## Configuration

### LLM Configuration
```json
{
  "ReportRenderingEngine": {
    "LLM": {
      "Provider": "OpenAI",
      "Model": "gpt-4",
      "ApiKey": "your-api-key-here",
      "MaxTokens": 4000,
      "TimeoutSeconds": 30
    },
    "ComponentGeneration": {
      "Framework": "NextJS",
      "TypeScript": true,
      "StyleFramework": "TailwindCSS",
      "ComponentLibrary": "Custom"
    },
    "Export": {
      "PdfEngine": "PuppeteerSharp",
      "OfficeEngine": "OpenXML",
      "TempDirectory": "/tmp/exports"
    }
  }
}
```

This documentation provides a comprehensive overview of the improved Report Rendering Engine architecture, focusing on Next.js component generation, partial re-rendering, versioning, and export capabilities.
