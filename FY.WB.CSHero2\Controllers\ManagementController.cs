using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Controllers
{
    /// <summary>
    /// Controller for administrative and management operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Ensure only authorized users can access these endpoints
    public class ManagementController : ControllerBase
    {
        private readonly ILogger<ManagementController> _logger;

        public ManagementController(ILogger<ManagementController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets system status and health information
        /// </summary>
        /// <returns>System status information</returns>
        [HttpGet("status")]
        public async Task<IActionResult> GetSystemStatus()
        {
            try
            {
                _logger.LogInformation("System status requested");
                
                return Ok(new
                {
                    Success = true,
                    Message = "System is operational",
                    Timestamp = System.DateTime.UtcNow,
                    Status = "Healthy"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Error getting system status: {Message}", ex.Message);
                
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "An error occurred getting system status",
                    Error = ex.Message
                });
            }
        }
    }
}
