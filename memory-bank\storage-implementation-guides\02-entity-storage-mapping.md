<!-- Guide Purpose: Detailed mapping of which entities go where and why, with cross-reference patterns -->
<!-- Target Audience: Developers implementing data access layers and understanding entity relationships -->

# Entity Storage Mapping Guide

## Overview

This guide provides detailed mapping of entities across the three storage systems, explaining the rationale for placement decisions and how cross-references maintain data integrity and relationships.

## Storage Decision Matrix

| Data Type | SQL Server | Cosmos DB | Blob Storage | Rationale |
|-----------|------------|-----------|--------------|-----------|
| Report Metadata | ✅ Primary | ❌ | ❌ | Structured, frequently queried, relational |
| Report Sections Structure | ✅ Primary | ❌ | ❌ | Relational hierarchy, foreign keys |
| Report Section Fields | ✅ Primary | ❌ | ❌ | Structured data with validation rules |
| Report Data Content | ❌ | ✅ Primary | ❌ | Large JSON documents, flexible schema |
| Version Snapshots | ❌ | ✅ Primary | ❌ | Document versioning, immutable data |
| Component Code | ❌ | ❌ | ✅ Primary | Large text files, infrequent access |
| Style Documents | ❌ | ❌ | ✅ Primary | CSS/HTML templates, caching friendly |
| Binary Assets | ❌ | ❌ | ✅ Primary | Images, PDFs, large files |
| User/Tenant Data | ✅ Primary | ❌ | ❌ | Identity, authentication, authorization |
| Audit Trails | ✅ Primary | ❌ | ❌ | Compliance, structured queries |

## SQL Server Entity Mapping

### Core Business Entities

#### Report Entity
```csharp
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    // Core metadata - stays in SQL
    public string ReportNumber { get; set; }        // Business identifier
    public Guid? ClientId { get; set; }             // Foreign key relationship (nullable)
    public string ClientName { get; set; }          // Denormalized for performance
    public string Name { get; set; }                // Frequently searched
    public string Category { get; set; }            // Filtering/grouping
    public int SlideCount { get; set; }             // Calculated field
    public string Status { get; set; }              // Workflow state
    public string Author { get; set; }              // User reference
    
    // Cross-storage references - critical for data retrieval
    public string DataDocumentId { get; set; }      // → Cosmos DB document
    public string ComponentsBlobId { get; set; }    // → Blob Storage path
    public string DraftDataDocumentId { get; set; } // → Cosmos DB draft
    
    // Template relationships
    public Guid? TemplateId { get; set; }           // Template source
    public Guid? CurrentVersionId { get; set; }     // Active version
    
    // Navigation properties for EF Core
    public virtual Client Client { get; set; }
    public virtual Template Template { get; set; }
    public virtual ICollection<ReportVersion> Versions { get; set; }
    public virtual ICollection<ReportSection> Sections { get; set; }
}
```

**Why SQL Server?**
- Frequently queried for lists, searches, and filtering
- Strong relationships with Clients, Templates, Users
- Transactional integrity for status changes
- Audit trail requirements

#### ReportVersion Entity
```csharp
public class ReportVersion : AuditedEntity<Guid>
{
    // Version metadata - SQL for queries and relationships
    public Guid ReportId { get; set; }              // Parent report
    public int VersionNumber { get; set; }          // Sequential versioning
    public string Description { get; set; }         // Change description
    public bool IsCurrent { get; set; }             // Active version flag
    
    // Legacy JSON storage (being phased out)
    public string ComponentDataJson { get; set; }   // Deprecated
    public string JsonData { get; set; }            // Deprecated
    
    // Multi-storage references
    public string DataDocumentId { get; set; }      // → Cosmos DB
    public string ComponentsBlobId { get; set; }    // → Blob Storage
    public string StylesBlobId { get; set; }        // → Blob Storage
    
    // Size tracking for optimization
    public long ComponentDataSize { get; set; }
    public long JsonDataSize { get; set; }
    public long StylesSize { get; set; }
    
    // Storage strategy indicator
    public string StorageStrategy { get; set; }     // "Legacy", "MultiStorage"
    
    public virtual Report Report { get; set; }
}
```

**Why SQL Server?**
- Version history queries and comparisons
- Current version determination
- Size tracking and storage optimization
- Transactional version creation

#### ReportSection Entity
```csharp
public class ReportSection : AuditedEntity<Guid>
{
    // Structure definition - SQL for relational integrity
    public Guid ReportId { get; set; }              // Parent report
    public string Title { get; set; }               // Section name
    public string Type { get; set; }                // "text", "chart", "table"
    public int Order { get; set; }                  // Display sequence
    public string Content { get; set; }             // Basic content (optional)
    
    // Navigation properties
    public virtual Report Report { get; set; }
    public virtual ICollection<ReportSectionField> Fields { get; set; }
}
```

#### ReportSectionField Entity
```csharp
public class ReportSectionField : AuditedEntity<Guid>
{
    // Field definition - SQL for structure and validation
    public Guid ReportSectionId { get; set; }       // Parent section
    public string Name { get; set; }                // Field identifier
    public string Type { get; set; }                // Data type
    public string Label { get; set; }               // Display label
    public string Value { get; set; }               // Current value
    public string Content { get; set; }             // Rich content
    public int Order { get; set; }                  // Display order
    public bool IsRequired { get; set; }            // Validation rule
    public string Metadata { get; set; }            // Configuration JSON
    
    // Template relationships
    public Guid? TemplateSourceFieldId { get; set; }
    public bool IsModifiedFromTemplate { get; set; }
    
    public virtual ReportSection ReportSection { get; set; }
}
```

**Why SQL Server for Sections/Fields?**
- Structured hierarchy with foreign keys
- Validation rules and constraints
- Template inheritance tracking
- Frequent queries for report building

### Supporting Entities

#### ReportStorageMetadata Entity
```csharp
public class ReportStorageMetadata : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }              // Report reference
    public string DataStorageType { get; set; }     // "SQL", "CosmosDB", "Hybrid"
    
    // External storage references
    public string DataDocumentId { get; set; }      // Cosmos document ID
    public string DataBlobPath { get; set; }        // Blob storage path
    public string AssetBlobPath { get; set; }       // Assets path
    
    // Size and performance tracking
    public long SqlStorageSize { get; set; }
    public long CosmosStorageSize { get; set; }
    public long BlobStorageSize { get; set; }
    public long TotalStorageSize { get; set; }
    public int AccessCount { get; set; }
    public DateTime? LastSyncedAt { get; set; }
    
    public virtual Report Report { get; set; }
}
```

## Cosmos DB Document Mapping

### VersionedReportDataDocument Structure

```json
{
  "id": "rpt_550e8400e29b41d4a716************_v_c8bc5efcccbd42c0a7aa019767f8b83d",
  "tenantId": "941fdfc2-c519-48ec-96af-6f893aca18ad",
  "reportId": "550e8400-e29b41d4-a716-************",
  "versionId": "c8bc5efc-ccbd-42c0-a7aa-019767f8b83d",
  "versionNumber": 1,
  "isDraft": false,
  "reportName": "Quarterly Financial Analysis",
  "reportNumber": "CSR-2025-008",
  "category": "Financial",
  "status": "Completed",
  "author": "Financial Team",
  "clientId": "D4E5F667-7889-0123-4567-890123DEF678",
  "clientName": "BioTech Innovations",
  "sections": [
    {
      "id": "7ed32b50-d07d-425f-bd35-730c801ae9cd",
      "name": "Executive Summary",
      "title": "Executive Summary",
      "description": "",
      "sectionType": "text",
      "displayOrder": 0,
      "isRequired": false,
      "fields": [
        {
          "id": "c11c3838-3f94-4652-81f4-d1868017edb4",
          "name": "heading",
          "label": "heading",
          "fieldType": "string",
          "defaultValue": "Executive Summary",
          "isRequired": false,
          "displayOrder": 0,
          "validationRules": {},
          "options": []
        }
      ]
    }
  ],
  "componentDataJson": {
    "layout": "standard",
    "theme": "corporate"
  },
  "jsonData": {
    "enhanced": true,
    "dashboardMetrics": {
      "totalCustomers": {
        "current": 1200,
        "previousPeriod": 1000,
        "trend": [950, 1000, 1050, 1100, 1150, 1180, 1200]
      }
    }
  },
  "metadata": {
    "createdAt": "2024-03-07T08:30:00Z",
    "updatedAt": "2024-03-07T08:30:00Z",
    "sectionCount": 3,
    "fieldCount": 8,
    "version": "1",
    "tags": ["Financial", "Completed", "enhanced_with_mock_data"]
  }
}
```

**Why Cosmos DB?**
- Large, nested JSON documents (can exceed SQL Server limits)
- Flexible schema for different report types
- Version snapshots are immutable
- High-throughput read operations
- Global distribution capabilities
- Partition by TenantId for multi-tenancy

### Document ID Generation Pattern

```csharp
public static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
{
    var reportIdClean = reportId.ToString("N");  // Remove hyphens
    var versionIdClean = versionId.ToString("N");
    return $"rpt_{reportIdClean}_v_{versionIdClean}";
}

// Example: "rpt_550e8400e29b41d4a716************_v_c8bc5efcccbd42c0a7aa019767f8b83d"
```

### Partition Key Strategy

```csharp
// Partition by TenantId for optimal multi-tenant isolation
public class VersionedReportDataDocument
{
    [JsonPropertyName("tenantId")]
    public string TenantId { get; set; }  // Partition key
    
    [JsonPropertyName("id")]
    public string Id { get; set; }        // Document ID
}
```

## Blob Storage Mapping

### Hierarchical Organization

```
Container: reports
├── tenants/
│   ├── {tenantId}/
│   │   ├── reports/
│   │   │   ├── {reportId}/
│   │   │   │   ├── versions/
│   │   │   │   │   ├── v{versionNumber}/
│   │   │   │   │   │   ├── components.json
│   │   │   │   │   │   ├── styles.json
│   │   │   │   │   │   ├── assets/
│   │   │   │   │   │   │   ├── images/
│   │   │   │   │   │   │   ├── charts/
│   │   │   │   │   │   │   └── exports/
```

### Component Storage Structure

```json
// components.json
{
  "reportId": "550e8400-e29b41d4-a716-************",
  "versionId": "c8bc5efc-ccbd-42c0-a7aa-019767f8b83d",
  "versionNumber": 1,
  "components": [
    {
      "id": "component-guid-1",
      "name": "ExecutiveSummarySection",
      "sectionId": "section-guid-1",
      "fileName": "ExecutiveSummarySection.tsx",
      "componentCode": "import React from 'react';\n\nexport const ExecutiveSummarySection = ({ data }) => {\n  return (\n    <section className=\"executive-summary\">\n      <h2>{data.heading}</h2>\n      <div>{data.content}</div>\n    </section>\n  );\n};",
      "imports": ["React"],
      "props": ["heading", "content"],
      "metadata": {
        "framework": "NextJS",
        "typescript": true,
        "styleFramework": "TailwindCSS"
      }
    }
  ],
  "metadata": {
    "createdAt": "2024-03-07T08:30:00Z",
    "createdBy": "system",
    "totalComponents": 1,
    "totalSize": 2048
  }
}
```

### Style Storage Structure

```json
// styles.json
{
  "reportId": "550e8400-e29b41d4-a716-************",
  "versionId": "c8bc5efc-ccbd-42c0-a7aa-019767f8b83d",
  "styleDocumentId": "style-report-c8bc5efc",
  "htmlContent": "<div class=\"report-container\">...</div>",
  "cssStyles": ".report-container { max-width: 1200px; margin: 0 auto; }",
  "componentStyles": {
    "header": {
      "componentId": "header",
      "componentType": "header",
      "cssClasses": ["report-header", "text-white", "p-8"],
      "customCss": ".report-header { background: linear-gradient(...); }"
    }
  },
  "metadata": {
    "framework": "TailwindCSS",
    "theme": "corporate",
    "colorScheme": "light"
  }
}
```

**Why Blob Storage?**
- Large text files (component code, CSS)
- Infrequent access patterns
- Cost-effective for archival data
- CDN integration for performance
- Version-specific immutable storage

## Cross-Storage Reference Patterns

### 1. SQL → Cosmos DB References

```csharp
public class ReportService
{
    public async Task<CompleteReportDto> GetReportWithDataAsync(Guid reportId)
    {
        // 1. Get metadata from SQL
        var report = await _context.Reports
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .FirstOrDefaultAsync(r => r.Id == reportId);
            
        if (report?.CurrentVersion?.DataDocumentId == null)
            return MapMetadataOnly(report);
            
        // 2. Get document data from Cosmos
        var documentData = await _cosmosService.GetDocumentAsync<VersionedReportDataDocument>(
            report.CurrentVersion.DataDocumentId, 
            report.TenantId.ToString());
            
        // 3. Combine data
        return new CompleteReportDto
        {
            Metadata = _mapper.Map<ReportDto>(report),
            DocumentData = documentData,
            HasExternalData = true
        };
    }
}
```

### 2. SQL → Blob Storage References

```csharp
public async Task<ReportComponentsDto> GetReportComponentsAsync(Guid reportId)
{
    // 1. Get blob reference from SQL
    var version = await _context.ReportVersions
        .Where(v => v.ReportId == reportId && v.IsCurrent)
        .FirstOrDefaultAsync();
        
    if (string.IsNullOrEmpty(version?.ComponentsBlobId))
        return null;
        
    // 2. Get components from Blob Storage
    var components = await _blobService.GetComponentsAsync(version.ComponentsBlobId);
    
    return new ReportComponentsDto
    {
        ReportId = reportId,
        VersionId = version.Id,
        Components = components
    };
}
```

### 3. Cosmos DB → SQL References

```csharp
public async Task<ReportMetadataDto> GetMetadataFromDocumentAsync(string documentId)
{
    // 1. Get document from Cosmos
    var document = await _cosmosService.GetDocumentAsync<VersionedReportDataDocument>(documentId);
    
    // 2. Get full metadata from SQL using document references
    var report = await _context.Reports
        .Include(r => r.Client)
        .Include(r => r.Template)
        .FirstOrDefaultAsync(r => r.Id == document.ReportId);
        
    return _mapper.Map<ReportMetadataDto>(report);
}
```

## Data Transformation Patterns

### SQL Entities → Cosmos Document

```csharp
public VersionedReportDataDocument TransformToCosmosDocument(Report report, ReportVersion version)
{
    return new VersionedReportDataDocument
    {
        Id = GenerateVersionedDocumentId(report.Id, version.Id),
        TenantId = report.TenantId.ToString(),
        ReportId = report.Id,
        VersionId = version.Id,
        VersionNumber = version.VersionNumber,
        
        // Copy metadata for denormalization
        ReportName = report.Name,
        ReportNumber = report.ReportNumber,
        Category = report.Category,
        Status = report.Status,
        Author = report.Author,
        ClientId = report.ClientId,
        ClientName = report.ClientName,
        
        // Transform sections
        Sections = report.Sections?.Select(s => new ReportDataSection
        {
            Id = s.Id,
            Name = s.Title,
            Title = s.Title,
            SectionType = s.Type ?? "default",
            DisplayOrder = s.Order,
            Fields = s.Fields?.Select(f => new ReportDataField
            {
                Id = f.Id,
                Name = f.Name,
                Label = f.Label,
                FieldType = f.Type,
                DefaultValue = f.Content ?? string.Empty,
                DisplayOrder = f.Order,
                IsRequired = f.IsRequired
            }).ToList() ?? new List<ReportDataField>()
        }).ToList() ?? new List<ReportDataSection>(),
        
        // Metadata
        Metadata = new ReportDataMetadata
        {
            CreatedAt = version.CreationTime.ToUniversalTime(),
            UpdatedAt = (version.LastModificationTime ?? version.CreationTime).ToUniversalTime(),
            SectionCount = report.Sections?.Count ?? 0,
            FieldCount = report.Sections?.Sum(s => s.Fields?.Count ?? 0) ?? 0,
            Version = version.VersionNumber.ToString()
        }
    };
}
```

### Cosmos Document → SQL Updates

```csharp
public async Task SyncDocumentChangesToSqlAsync(VersionedReportDataDocument document)
{
    var report = await _context.Reports.FindAsync(document.ReportId);
    if (report == null) return;
    
    // Update denormalized fields if they've changed
    var hasChanges = false;
    
    if (report.Name != document.ReportName)
    {
        report.Name = document.ReportName;
        hasChanges = true;
    }
    
    if (report.Status != document.Status)
    {
        report.Status = document.Status;
        hasChanges = true;
    }
    
    if (hasChanges)
    {
        await _context.SaveChangesAsync();
    }
}
```

## Multi-Tenant Isolation Strategies

### SQL Server Isolation

```csharp
// Global query filter in ApplicationDbContext
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // Apply tenant filter to all multi-tenant entities
    modelBuilder.Entity<Report>().HasQueryFilter(r => r.TenantId == _currentTenant.Id);
    modelBuilder.Entity<ReportVersion>().HasQueryFilter(rv => rv.Report.TenantId == _currentTenant.Id);
    modelBuilder.Entity<ReportSection>().HasQueryFilter(rs => rs.Report.TenantId == _currentTenant.Id);
}
```

### Cosmos DB Isolation

```csharp
public async Task<T> GetDocumentAsync<T>(string documentId, string tenantId)
{
    // Partition key ensures tenant isolation
    var response = await _container.ReadItemAsync<T>(
        documentId, 
        new PartitionKey(tenantId));
        
    return response.Resource;
}
```

### Blob Storage Isolation

```csharp
public string GenerateTenantBlobPath(string tenantId, string reportId, string fileName)
{
    // Path-based tenant isolation
    return $"tenants/{tenantId}/reports/{reportId}/{fileName}";
}

public async Task<Stream> GetBlobAsync(string blobPath, string expectedTenantId)
{
    // Validate tenant access through path
    if (!blobPath.StartsWith($"tenants/{expectedTenantId}/"))
    {
        throw new UnauthorizedAccessException("Cross-tenant access denied");
    }
    
    return await _blobClient.OpenReadAsync(blobPath);
}
```

## Performance Optimization Patterns

### 1. Denormalization Strategy

Store frequently accessed data in multiple places:

```csharp
public class Report
{
    // Normalized reference (nullable)
    public Guid? ClientId { get; set; }
    
    // Denormalized for performance (avoid joins)
    public string ClientName { get; set; }
    
    // Calculated fields to avoid aggregation queries
    public int SlideCount { get; set; }
    public DateTime? LastModifiedAt { get; set; }
}
```

### 2. Lazy Loading Pattern

```csharp
public class ReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    
    // Lazy-loaded properties
    public Lazy<Task<VersionedReportDataDocument>> DocumentData { get; set; }
    public Lazy<Task<ComponentsMetadata>> Components { get; set; }
    
    public static ReportDto Create(Report report, ICosmosService cosmos, IBlobService blob)
    {
        return new ReportDto
        {
            Id = report.Id,
            Name = report.Name,
            DocumentData = new Lazy<Task<VersionedReportDataDocument>>(
                () => cosmos.GetDocumentAsync<VersionedReportDataDocument>(report.DataDocumentId)),
            Components = new Lazy<Task<ComponentsMetadata>>(
                () => blob.GetComponentsMetadataAsync(report.ComponentsBlobId))
        };
    }
}
```

### 3. Projection Patterns

Use specific DTOs for different access patterns:

```csharp
// List view - minimal data
public class ReportListItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public string ClientName { get; set; }
}

// Detail view - includes external data
public class ReportDetailDto : ReportListItemDto
{
    public VersionedReportDataDocument DocumentData { get; set; }
    public ComponentsMetadata Components { get; set; }
    public List<ReportVersionDto> Versions { get; set; }
}

// Edit view - includes structure
public class ReportEditDto : ReportDetailDto
{
    public List<ReportSectionDto> Sections { get; set; }
    public List<ReportSectionFieldDto> Fields { get; set; }
}
```

This mapping strategy ensures optimal data placement while maintaining consistency and performance across the multi-storage architecture.
