using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Domain.Interfaces
{
    /// <summary>
    /// Service interface for managing report versions and version control
    /// </summary>
    public interface IVersioningService
    {
        /// <summary>
        /// Creates a new version of a report with component data
        /// </summary>
        /// <param name="reportId">Report to create version for</param>
        /// <param name="components">Component result from rendering</param>
        /// <param name="description">Description of changes in this version</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created report version</returns>
        Task<ReportVersion> CreateVersionAsync(
            Guid reportId,
            ComponentResult components,
            string? description = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the version history for a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of report versions ordered by version number</returns>
        Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a specific version of a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="versionNumber">Version number to retrieve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Specific report version</returns>
        Task<ReportVersion> GetVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back a report to a previous version
        /// </summary>
        /// <param name="reportId">Report to rollback</param>
        /// <param name="versionNumber">Version number to rollback to</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if rollback was successful</returns>
        Task<bool> RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a specific version (cannot delete current version or version 1)
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="versionNumber">Version number to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current version of a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Current report version</returns>
        Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Compares two versions of a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="fromVersion">Source version number</param>
        /// <param name="toVersion">Target version number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Version comparison result</returns>
        Task<VersionComparison> CompareVersionsAsync(
            Guid reportId,
            int fromVersion,
            int toVersion,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets version statistics for a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Version statistics</returns>
        Task<VersionStatistics> GetVersionStatisticsAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a version from existing data (for migration purposes)
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="versionData">Version data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created version</returns>
        Task<ReportVersion> CreateVersionFromDataAsync(
            Guid reportId,
            CreateVersionRequest versionData,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cleans up old versions based on retention policy
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="maxVersionsToKeep">Maximum number of versions to keep</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of versions deleted</returns>
        Task<int> CleanupOldVersionsAsync(Guid reportId, int maxVersionsToKeep = 10, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Component result from rendering process
    /// </summary>
    public class ComponentResult
    {
        public bool Success { get; set; }
        public List<SectionComponent> Components { get; set; } = new List<SectionComponent>();
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public ComponentMetadata Metadata { get; set; } = new ComponentMetadata();
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Individual section component
    /// </summary>
    public class SectionComponent
    {
        public string SectionId { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public string ComponentCode { get; set; } = string.Empty;
        public string TypeDefinitions { get; set; } = string.Empty;
        public List<string> Imports { get; set; } = new List<string>();
        public Dictionary<string, object> Props { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Component metadata
    /// </summary>
    public class ComponentMetadata
    {
        public string Framework { get; set; } = "NextJS";
        public string StyleFramework { get; set; } = "TailwindCSS";
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public string GeneratedBy { get; set; } = string.Empty;
        public Dictionary<string, object> AdditionalMetadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Version comparison result
    /// </summary>
    public class VersionComparison
    {
        public ReportVersion FromVersion { get; set; } = null!;
        public ReportVersion ToVersion { get; set; } = null!;
        public List<string> DataChanges { get; set; } = new List<string>();
        public List<string> ComponentChanges { get; set; } = new List<string>();
        public bool HasChanges => DataChanges.Any() || ComponentChanges.Any();
    }

    /// <summary>
    /// Version statistics
    /// </summary>
    public class VersionStatistics
    {
        public int TotalVersions { get; set; }
        public int CurrentVersionNumber { get; set; }
        public DateTime FirstVersionCreated { get; set; }
        public DateTime LastVersionCreated { get; set; }
        public long TotalStorageSize { get; set; }
        public Dictionary<int, long> VersionSizes { get; set; } = new Dictionary<int, long>();
    }

    /// <summary>
    /// Request for creating a version from existing data
    /// </summary>
    public class CreateVersionRequest
    {
        public int VersionNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> JsonData { get; set; } = new Dictionary<string, object>();
        public List<SectionComponent> Components { get; set; } = new List<SectionComponent>();
        public bool IsCurrent { get; set; }
    }
}
