﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class MakeReportClientIdNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Make ClientId column nullable in Reports table
            migrationBuilder.AlterColumn<Guid?>(
                name: "ClientId",
                table: "Reports",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Make ClientId column non-nullable again in Reports table
            migrationBuilder.AlterColumn<Guid>(
                name: "ClientId",
                table: "Reports",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid?),
                oldType: "uniqueidentifier",
                oldNullable: true);
        }
    }
}
