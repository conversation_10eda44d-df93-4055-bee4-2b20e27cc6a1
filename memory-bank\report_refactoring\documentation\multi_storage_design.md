# Multi-Storage Report Structure Design

## Overview

This document outlines a comprehensive design for refactoring the report structure to use multiple storage mechanisms:

1. **SQL Database**: Store report metadata and style selections
   - Report basic information (name, description, creation date, etc.)
   - Report version history
   - Style selections and preferences

2. **Azure Cosmos DB**: Store report data (sections and fields) as JSON
   - Section structure and hierarchy
   - Field content and properties
   - Flexible metadata and attributes

3. **Azure Blob Storage**: Store rendered Next.js components with HTML and CSS
   - Component code (React/Next.js)
   - Component metadata
   - Assets and resources

This approach separates concerns and leverages the strengths of each storage technology:
- SQL for structured, relational data with strong consistency
- Blob Storage for large, unstructured content with high throughput
- Cosmos DB for semi-structured, document-oriented data with flexible schema

## New Domain Model

### Core Entities

#### Report (SQL)

```csharp
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    // Metadata
    public string ReportNumber { get; set; }
    public Guid ClientId { get; set; }
    public string ClientName { get; set; }
    public string Name { get; set; }
    public string Category { get; set; }
    public int SlideCount { get; set; }
    public string Status { get; set; }
    public string Author { get; set; }
    
    // References
    public Guid? TemplateId { get; set; }
    public Guid? CurrentVersionId { get; set; }
    public string ReportType { get; set; }
    
    // Storage references
    public string DataDocumentId { get; set; } // Reference to Cosmos DB document
    public string ComponentsBlobId { get; set; } // Reference to Blob Storage
    
    // Navigation properties
    public virtual Client Client { get; set; }
    public virtual Template Template { get; set; }
    public virtual ICollection<ReportVersion> Versions { get; set; }
}
```

#### ReportVersion (SQL)

```csharp
public class ReportVersion : AuditedEntity<Guid>
{
    // Version metadata
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    public string Description { get; set; }
    public bool IsCurrent { get; set; }
    
    // Storage references
    public string DataDocumentId { get; set; } // Reference to Cosmos DB document
    public string ComponentsBlobId { get; set; } // Reference to Blob Storage
    
    // Size tracking
    public long ComponentsSize { get; set; }
    public long DataSize { get; set; }
    
    // Navigation property
    public virtual Report Report { get; set; }
}
```

#### ReportStyle (SQL)

```csharp
public class ReportStyle : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    public string Theme { get; set; }
    public string ColorScheme { get; set; }
    public string Typography { get; set; }
    public string Spacing { get; set; }
    
    // JSON serialized style options
    public string LayoutOptionsJson { get; set; }
    public string TypographyOptionsJson { get; set; }
    public string StructureOptionsJson { get; set; }
    public string ContentOptionsJson { get; set; }
    public string VisualOptionsJson { get; set; }
    
    // Navigation property
    public virtual Report Report { get; set; }
}
```

### Data Models

#### ReportData (Cosmos DB)

```json
{
  "id": "report-data-guid",
  "reportId": "sql-report-guid",
  "versionId": "sql-version-guid",
  "versionNumber": 1,
  "sections": [
    {
      "id": "section-guid-1",
      "title": "Executive Summary",
      "type": "text",
      "order": 0,
      "fields": [
        {
          "id": "field-guid-1",
          "name": "heading",
          "type": "string",
          "content": "Executive Summary",
          "order": 0
        },
        {
          "id": "field-guid-2",
          "name": "body",
          "type": "string",
          "content": "This is the executive summary content.",
          "order": 1
        }
      ]
    },
    {
      "id": "section-guid-2",
      "title": "Financial Analysis",
      "type": "chart",
      "order": 1,
      "fields": [
        {
          "id": "field-guid-3",
          "name": "heading",
          "type": "string",
          "content": "Financial Analysis",
          "order": 0
        },
        {
          "id": "field-guid-4",
          "name": "chartType",
          "type": "string",
          "content": "bar",
          "order": 1
        },
        {
          "id": "field-guid-5",
          "name": "data",
          "type": "json",
          "content": "{\"labels\":[\"Q1\",\"Q2\",\"Q3\",\"Q4\"],\"values\":[100,150,200,250]}",
          "order": 2
        }
      ]
    }
  ],
  "metadata": {
    "createdAt": "2025-06-02T10:30:00Z",
    "createdBy": "user-guid",
    "lastModifiedAt": "2025-06-02T11:45:00Z",
    "lastModifiedBy": "user-guid"
  }
}
```

#### ReportComponents (Blob Storage)

The blob storage will contain:

1. **Component Definitions**: Next.js React components as TypeScript/JavaScript files
2. **Type Definitions**: TypeScript type definitions for the components
3. **Metadata**: JSON file with component metadata

Example structure in blob storage:
```
reports/
  {reportId}/
    {versionId}/
      components/
        ExecutiveSummarySection.tsx
        FinancialAnalysisSection.tsx
        ChartComponent.tsx
      types/
        index.d.ts
      metadata.json
```

Example component file:
```tsx
// ExecutiveSummarySection.tsx
import React from 'react';

interface ExecutiveSummarySectionProps {
  heading: string;
  body: string;
}

export const ExecutiveSummarySection: React.FC<ExecutiveSummarySectionProps> = ({ 
  heading, 
  body 
}) => {
  return (
    <section className="executive-summary">
      <h2 className="text-2xl font-bold mb-4">{heading}</h2>
      <div className="prose max-w-none">
        <p>{body}</p>
      </div>
    </section>
  );
};
```

Example metadata.json:
```json
{
  "reportId": "sql-report-guid",
  "versionId": "sql-version-guid",
  "versionNumber": 1,
  "components": [
    {
      "id": "component-guid-1",
      "name": "ExecutiveSummarySection",
      "sectionId": "section-guid-1",
      "fileName": "ExecutiveSummarySection.tsx",
      "imports": ["React"],
      "props": ["heading", "body"]
    },
    {
      "id": "component-guid-2",
      "name": "FinancialAnalysisSection",
      "sectionId": "section-guid-2",
      "fileName": "FinancialAnalysisSection.tsx",
      "imports": ["React", "ChartComponent"],
      "props": ["heading", "chartType", "data"]
    }
  ]
}
```

## Service Interfaces

### Storage Services

#### IReportMetadataRepository (SQL)

```csharp
public interface IReportMetadataRepository
{
    Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<ReportVersion> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
    Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<ReportStyle> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task SaveReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default);
}
```

#### IReportDataRepository (Cosmos DB)

```csharp
public interface IReportDataRepository
{
    Task<ReportData> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task DeleteReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    
    Task<ReportSection> GetSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
    Task UpdateSectionAsync(string documentId, ReportSection section, CancellationToken cancellationToken = default);
    
    Task<ReportSectionField> GetFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
    Task UpdateFieldAsync(string documentId, string sectionId, ReportSectionField field, CancellationToken cancellationToken = default);
}
```

#### IReportComponentsRepository (Blob Storage)

```csharp
public interface IReportComponentsRepository
{
    Task<ComponentsMetadata> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
    Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<ComponentDefinition> components, CancellationToken cancellationToken = default);
    Task<ComponentDefinition> GetComponentAsync(string blobId, string componentId, CancellationToken cancellationToken = default);
    Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    
    Task<byte[]> ExportComponentsAsZipAsync(string blobId, CancellationToken cancellationToken = default);
}
```

### Orchestration Services

#### IReportService

```csharp
public interface IReportService
{
    Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(CreateReportRequestDto request, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(UpdateReportRequestDto request, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<ReportVersionDto> GetReportVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);
    Task<ReportVersionDto> CreateReportVersionAsync(Guid reportId, string description, CancellationToken cancellationToken = default);
    Task<List<ReportVersionDto>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);
    
    Task UpdateReportStyleAsync(Guid reportId, ReportStyleDto style, CancellationToken cancellationToken = default);
}
```

#### IReportDataService

```csharp
public interface IReportDataService
{
    Task<ReportDataDto> GetReportDataAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(Guid reportId, ReportDataDto data, CancellationToken cancellationToken = default);
    
    Task<ReportSectionDto> GetSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    Task<string> AddSectionAsync(Guid reportId, ReportSectionDto section, CancellationToken cancellationToken = default);
    Task UpdateSectionAsync(Guid reportId, ReportSectionDto section, CancellationToken cancellationToken = default);
    Task DeleteSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    
    Task<ReportSectionFieldDto> GetFieldAsync(Guid reportId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
    Task<string> AddFieldAsync(Guid reportId, string sectionId, ReportSectionFieldDto field, CancellationToken cancellationToken = default);
    Task UpdateFieldAsync(Guid reportId, string sectionId, ReportSectionFieldDto field, CancellationToken cancellationToken = default);
    Task DeleteFieldAsync(Guid reportId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
}
```

#### IReportRenderingService

```csharp
public interface IReportRenderingService
{
    Task<RenderResultDto> RenderReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<RenderResultDto> RenderSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    
    Task<ComponentDefinitionDto> GetComponentAsync(Guid reportId, string componentId, CancellationToken cancellationToken = default);
    Task<List<ComponentDefinitionDto>> GetComponentsAsync(Guid reportId, CancellationToken cancellationToken = default);
    
    Task<byte[]> ExportReportAsync(Guid reportId, ExportFormat format, CancellationToken cancellationToken = default);
}
```

## Implementation Details

### SQL Implementation

The SQL implementation will use Entity Framework Core with the existing `ApplicationDbContext`. The entities will be configured using the standard EF Core configuration approach.

```csharp
public class ReportMetadataRepository : IReportMetadataRepository
{
    private readonly IApplicationDbContext _context;
    
    public ReportMetadataRepository(IApplicationDbContext context)
    {
        _context = context;
    }
    
    public async Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await _context.Reports
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
    }
    
    // Other methods implementation...
}
```

### Cosmos DB Implementation

The Cosmos DB implementation will use the Azure Cosmos DB SDK for .NET.

```csharp
public class ReportDataRepository : IReportDataRepository
{
    private readonly CosmosClient _cosmosClient;
    private readonly Container _container;
    
    public ReportDataRepository(CosmosClient cosmosClient, IConfiguration configuration)
    {
        _cosmosClient = cosmosClient;
        var databaseName = configuration["CosmosDb:DatabaseName"];
        var containerName = configuration["CosmosDb:ContainerName"];
        _container = _cosmosClient.GetContainer(databaseName, containerName);
    }
    
    public async Task<ReportData> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _container.ReadItemAsync<ReportData>(
                documentId, 
                new PartitionKey(documentId),
                cancellationToken: cancellationToken);
                
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return null;
        }
    }
    
    // Other methods implementation...
}
```

### Blob Storage Implementation

The Blob Storage implementation will use the Azure Storage SDK for .NET.

```csharp
public class ReportComponentsRepository : IReportComponentsRepository
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    
    public ReportComponentsRepository(BlobServiceClient blobServiceClient, IConfiguration configuration)
    {
        _blobServiceClient = blobServiceClient;
        _containerName = configuration["BlobStorage:ContainerName"];
    }
    
    public async Task<ComponentsMetadata> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        var blobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");
        
        if (!await blobClient.ExistsAsync(cancellationToken))
        {
            return null;
        }
        
        var response = await blobClient.DownloadAsync(cancellationToken);
        using var streamReader = new StreamReader(response.Value.Content);
        var json = await streamReader.ReadToEndAsync();
        
        return JsonSerializer.Deserialize<ComponentsMetadata>(json);
    }
    
    // Other methods implementation...
}
```

## Migration Strategy

### Phase 1: Infrastructure Setup
(Completed)
1. Set up Azure Cosmos DB account, database, and container
2. Set up Azure Blob Storage account and container
3. Add new entities to SQL database schema
4. Implement repository interfaces for each storage type
  // This will have to be verified that it is completed. Remove this comment when verified.

### Phase 2: Data Migration

1. This is the development environment on a new project. Do NOT create any new migrations to update storages and move data. 
2. ONLY update Data seed files, SeedData files, and existing storage migration that builds the database from scratch.

### Phase 3: Service Implementation

1. Implement the repository interfaces for each storage type
2. Implement the orchestration services
3. Update existing controllers to use the new services
4. Add new endpoints for section and field management

### Phase 4: UI Updates

1. Update frontend models to match the new structure
2. Update API service functions to use the new endpoints
3. Update UI components to handle the new data structure

## Benefits of the New Design

1. **Improved Performance**:
   - SQL for efficient querying of metadata
   - Blob Storage for high-throughput access to components
   - Cosmos DB for flexible querying of report data

2. **Better Scalability**:
   - Each storage type can scale independently
   - Cosmos DB can handle large volumes of report data
   - Blob Storage can handle large component files

3. **Flexible Schema Evolution**:
   - Cosmos DB allows for schema changes without migrations
   - New field types can be added without SQL schema changes

4. **Cost Optimization**:
   - SQL for small, frequently accessed data
   - Blob Storage for cost-effective storage of large files
   - Cosmos DB for optimized document storage

5. **Improved Maintainability**:
   - Clear separation of concerns
   - Each storage type optimized for its purpose
   - Simplified data access patterns

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Data consistency across storage types | Implement transactional patterns and compensating transactions |
| Increased complexity of multiple storage types | Clear documentation and abstraction through repository interfaces |
| Performance overhead of multiple storage calls | Implement caching and batch operations |
| Cost management | Monitor usage and implement cost optimization strategies |

## Conclusion

This multi-storage approach leverages the strengths of each storage technology to create a more scalable, flexible, and performant report system. By separating report metadata, component definitions, and report data into different storage types, we can optimize each for its specific use case while maintaining a cohesive system through well-defined interfaces and orchestration services.