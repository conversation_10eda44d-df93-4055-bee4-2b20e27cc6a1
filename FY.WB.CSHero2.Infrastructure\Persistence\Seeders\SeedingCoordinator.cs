using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface ISeedingCoordinator
    {
        Task SeedAllStoragesAsync(CancellationToken cancellationToken = default);
        Task<SeedingResult> GetSeedingStatusAsync(CancellationToken cancellationToken = default);
    }

    public class SeedingCoordinator : ISeedingCoordinator
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SeedingCoordinator> _logger;

        public SeedingCoordinator(IServiceProvider serviceProvider, ILogger<SeedingCoordinator> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task SeedAllStoragesAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting multi-storage seeding coordination...");
            var startTime = DateTime.UtcNow;

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                
                // Check service availability and get services
                var sqlSeeder = scope.ServiceProvider.GetService<ISqlSeeder>();
                var cosmosSeeder = scope.ServiceProvider.GetService<ICosmosSeeder>();
                var blobSeeder = scope.ServiceProvider.GetService<IBlobSeeder>();

                // Phase 1: SQL Server seeding (foundation data) - Required
                if (sqlSeeder != null)
                {
                    _logger.LogInformation("Phase 1: Starting SQL Server seeding...");
                    await sqlSeeder.SeedAsync(context, cancellationToken);
                    _logger.LogInformation("Phase 1: SQL Server seeding completed");
                }
                else
                {
                    _logger.LogError("ISqlSeeder service not registered. SQL seeding is required and cannot proceed.");
                    throw new InvalidOperationException("ISqlSeeder service not registered. Cannot proceed with seeding.");
                }

                // Phase 2: Cosmos DB seeding (document data linked to SQL) - Optional
                if (cosmosSeeder != null)
                {
                    try
                    {
                        _logger.LogInformation("Phase 2: Starting Cosmos DB seeding...");
                        await cosmosSeeder.SeedAsync(context, cancellationToken);
                        _logger.LogInformation("Phase 2: Cosmos DB seeding completed");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Phase 2: Cosmos DB seeding failed, continuing with remaining phases");
                    }
                }
                else
                {
                    _logger.LogInformation("Phase 2: Cosmos DB seeder not available, skipping Cosmos DB seeding");
                }

                // Phase 3: Blob Storage seeding (component data linked to SQL) - Optional
                if (blobSeeder != null)
                {
                    try
                    {
                        _logger.LogInformation("Phase 3: Starting Blob Storage seeding...");
                        await blobSeeder.SeedAsync(context, cancellationToken);
                        _logger.LogInformation("Phase 3: Blob Storage seeding completed");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Phase 3: Blob Storage seeding failed, continuing");
                    }
                }
                else
                {
                    _logger.LogInformation("Phase 3: Blob Storage seeder not available, skipping Blob Storage seeding");
                }

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("Multi-storage seeding coordination completed in {Duration:mm\\:ss}", duration);
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                _logger.LogError(ex, "Multi-storage seeding coordination failed after {Duration:mm\\:ss}", duration);
                throw;
            }
        }

        public async Task<SeedingResult> GetSeedingStatusAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Checking seeding status across all storage systems...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var sqlSeeder = scope.ServiceProvider.GetService<ISqlSeeder>();
                var cosmosSeeder = scope.ServiceProvider.GetService<ICosmosSeeder>();
                var blobSeeder = scope.ServiceProvider.GetService<IBlobSeeder>();

                var result = new SeedingResult
                {
                    CheckedAt = DateTime.UtcNow
                };

                // Check SQL Server status
                if (sqlSeeder != null)
                {
                    result.SqlStatus = await GetSqlSeedingStatusAsync(context, sqlSeeder, cancellationToken);
                }
                else
                {
                    result.SqlStatus = new StorageStatus
                    {
                        Status = SeedingStatusType.Error,
                        LastChecked = DateTime.UtcNow,
                        Details = "ISqlSeeder service not registered"
                    };
                }

                // Check Cosmos DB status
                if (cosmosSeeder != null)
                {
                    result.CosmosStatus = await GetCosmosSeedingStatusAsync(cosmosSeeder, cancellationToken);
                }
                else
                {
                    result.CosmosStatus = new StorageStatus
                    {
                        Status = SeedingStatusType.Empty,
                        LastChecked = DateTime.UtcNow,
                        Details = "ICosmosSeeder service not available"
                    };
                }

                // Check Blob Storage status
                if (blobSeeder != null)
                {
                    result.BlobStatus = await GetBlobSeedingStatusAsync(blobSeeder, cancellationToken);
                }
                else
                {
                    result.BlobStatus = new StorageStatus
                    {
                        Status = SeedingStatusType.Empty,
                        LastChecked = DateTime.UtcNow,
                        Details = "IBlobSeeder service not available"
                    };
                }

                // Overall status
                result.OverallStatus = DetermineOverallStatus(result.SqlStatus, result.CosmosStatus, result.BlobStatus);

                _logger.LogInformation("Seeding status check completed: SQL={SqlStatus}, Cosmos={CosmosStatus}, Blob={BlobStatus}, Overall={OverallStatus}",
                    result.SqlStatus.Status, result.CosmosStatus.Status, result.BlobStatus.Status, result.OverallStatus);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking seeding status");
                throw;
            }
        }

        private async Task<StorageStatus> GetSqlSeedingStatusAsync(ApplicationDbContext context, ISqlSeeder sqlSeeder, CancellationToken cancellationToken)
        {
            try
            {
                var isEmpty = sqlSeeder.IsTableEmpty<Domain.Entities.TenantProfile>(context);
                return new StorageStatus
                {
                    Status = isEmpty ? SeedingStatusType.Empty : SeedingStatusType.Seeded,
                    RecordCount = isEmpty ? 0 : await context.TenantProfiles.CountAsync(cancellationToken),
                    LastChecked = DateTime.UtcNow,
                    Details = isEmpty ? "No data found" : "Data exists"
                };
            }
            catch (Exception ex)
            {
                return new StorageStatus
                {
                    Status = SeedingStatusType.Error,
                    LastChecked = DateTime.UtcNow,
                    Details = ex.Message
                };
            }
        }

        private async Task<StorageStatus> GetCosmosSeedingStatusAsync(ICosmosSeeder cosmosSeeder, CancellationToken cancellationToken)
        {
            try
            {
                var existingDocuments = await cosmosSeeder.GetExistingDocumentIdsAsync(cancellationToken);
                return new StorageStatus
                {
                    Status = existingDocuments.Any() ? SeedingStatusType.Seeded : SeedingStatusType.Empty,
                    RecordCount = existingDocuments.Count,
                    LastChecked = DateTime.UtcNow,
                    Details = existingDocuments.Any() ? $"{existingDocuments.Count} documents found" : "No documents found"
                };
            }
            catch (Exception ex)
            {
                return new StorageStatus
                {
                    Status = SeedingStatusType.Error,
                    LastChecked = DateTime.UtcNow,
                    Details = ex.Message
                };
            }
        }

        private async Task<StorageStatus> GetBlobSeedingStatusAsync(IBlobSeeder blobSeeder, CancellationToken cancellationToken)
        {
            try
            {
                var existingBlobs = await blobSeeder.GetExistingBlobIdsAsync(cancellationToken);
                return new StorageStatus
                {
                    Status = existingBlobs.Any() ? SeedingStatusType.Seeded : SeedingStatusType.Empty,
                    RecordCount = existingBlobs.Count,
                    LastChecked = DateTime.UtcNow,
                    Details = existingBlobs.Any() ? $"{existingBlobs.Count} blobs found" : "No blobs found"
                };
            }
            catch (Exception ex)
            {
                return new StorageStatus
                {
                    Status = SeedingStatusType.Error,
                    LastChecked = DateTime.UtcNow,
                    Details = ex.Message
                };
            }
        }

        private static SeedingStatusType DetermineOverallStatus(StorageStatus sqlStatus, StorageStatus cosmosStatus, StorageStatus blobStatus)
        {
            // If any storage has errors, overall status is error
            if (sqlStatus.Status == SeedingStatusType.Error || 
                cosmosStatus.Status == SeedingStatusType.Error || 
                blobStatus.Status == SeedingStatusType.Error)
            {
                return SeedingStatusType.Error;
            }

            // If all storages are seeded, overall status is seeded
            if (sqlStatus.Status == SeedingStatusType.Seeded && 
                cosmosStatus.Status == SeedingStatusType.Seeded && 
                blobStatus.Status == SeedingStatusType.Seeded)
            {
                return SeedingStatusType.Seeded;
            }

            // If any storage is partially seeded, overall status is partial
            if (sqlStatus.Status == SeedingStatusType.Seeded || 
                cosmosStatus.Status == SeedingStatusType.Seeded || 
                blobStatus.Status == SeedingStatusType.Seeded)
            {
                return SeedingStatusType.Partial;
            }

            // If all storages are empty, overall status is empty
            return SeedingStatusType.Empty;
        }
    }

    public class SeedingResult
    {
        public DateTime CheckedAt { get; set; }
        public SeedingStatusType OverallStatus { get; set; }
        public StorageStatus SqlStatus { get; set; } = new();
        public StorageStatus CosmosStatus { get; set; } = new();
        public StorageStatus BlobStatus { get; set; } = new();
    }

    public class StorageStatus
    {
        public SeedingStatusType Status { get; set; }
        public int RecordCount { get; set; }
        public DateTime LastChecked { get; set; }
        public string Details { get; set; } = string.Empty;
    }

    public enum SeedingStatusType
    {
        Empty,
        Partial,
        Seeded,
        Error
    }
}
