using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Domain.Interfaces
{
    /// <summary>
    /// Service interface for managing templates in the Report Rendering Engine V2
    /// </summary>
    public interface ITemplateService
    {
        /// <summary>
        /// Gets all public templates, optionally filtered by category
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of public templates</returns>
        Task<List<Template>> GetPublicTemplatesAsync(string? category = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all templates owned by the current user
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of user's templates</returns>
        Task<List<Template>> GetUserTemplatesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a specific template by ID
        /// </summary>
        /// <param name="templateId">Template identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Template entity</returns>
        Task<Template> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Clones a template and creates a new report instance from it
        /// </summary>
        /// <param name="templateId">Template to clone</param>
        /// <param name="reportName">Name for the new report</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>New report instance created from template</returns>
        Task<Report> CloneTemplateAsync(Guid templateId, string reportName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new template
        /// </summary>
        /// <param name="request">Template creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created template</returns>
        Task<Template> CreateTemplateAsync(CreateTemplateRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing template
        /// </summary>
        /// <param name="templateId">Template to update</param>
        /// <param name="request">Update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Updated template</returns>
        Task<Template> UpdateTemplateAsync(Guid templateId, UpdateTemplateRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a template (only if user owns it)
        /// </summary>
        /// <param name="templateId">Template to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets templates by category with pagination
        /// </summary>
        /// <param name="category">Category to filter by</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paginated list of templates</returns>
        Task<(List<Template> Templates, int TotalCount)> GetTemplatesByCategoryAsync(
            string category, 
            int page = 1, 
            int pageSize = 20, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches templates by name or description
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching templates</returns>
        Task<List<Template>> SearchTemplatesAsync(string searchTerm, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the most popular templates based on usage count
        /// </summary>
        /// <param name="count">Number of templates to return</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of popular templates</returns>
        Task<List<Template>> GetPopularTemplatesAsync(int count = 10, CancellationToken cancellationToken = default);

        /// <summary>
        /// Increments the usage count for a template when it's cloned
        /// </summary>
        /// <param name="templateId">Template that was used</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task IncrementUsageCountAsync(Guid templateId, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Request model for creating a new template
    /// </summary>
    public class CreateTemplateRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public bool IsPublic { get; set; }
        public string StructureJson { get; set; } = string.Empty;
        public string DefaultStyleJson { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new List<string>();
        public int EstimatedCompletionTimeMinutes { get; set; } = 30;
    }

    /// <summary>
    /// Request model for updating an existing template
    /// </summary>
    public class UpdateTemplateRequest
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public bool? IsPublic { get; set; }
        public string? StructureJson { get; set; }
        public string? DefaultStyleJson { get; set; }
        public string? ThumbnailUrl { get; set; }
        public List<string>? Tags { get; set; }
        public int? EstimatedCompletionTimeMinutes { get; set; }
        public bool? IsActive { get; set; }
    }
}
