using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for managing React components in Azure Blob Storage
    /// </summary>
    public class ReportComponentsRepository : IReportComponentsRepository
    {
        private readonly BlobContainerClient _containerClient;
        private readonly ILogger<ReportComponentsRepository> _logger;
        private readonly BlobStorageOptions _options;

        public ReportComponentsRepository(BlobServiceClient blobServiceClient, IOptions<BlobStorageOptions> options, ILogger<ReportComponentsRepository> logger)
        {
            _options = options.Value;
            _logger = logger;
            _containerClient = blobServiceClient.GetBlobContainerClient(_options.ContainerName);
        }

        // Components Metadata Operations
        public async Task<ComponentsMetadata?> GetComponentsMetadataAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var blobPath = GenerateComponentsMetadataPath(tenantId, reportId, versionId);
            return await GetComponentsMetadataAsync(blobPath, cancellationToken);
        }

        public async Task<ComponentsMetadata?> GetComponentsMetadataAsync(string componentsBlobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting components metadata from blob {BlobPath}", componentsBlobId);
                
                var blobClient = _containerClient.GetBlobClient(componentsBlobId);
                
                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Components metadata blob {BlobPath} not found", componentsBlobId);
                    return null;
                }
                
                var response = await blobClient.DownloadContentAsync(cancellationToken);
                var json = response.Value.Content.ToString();
                var metadata = JsonSerializer.Deserialize<ComponentsMetadata>(json);
                
                _logger.LogInformation("Retrieved components metadata from blob {BlobPath}", componentsBlobId);
                return metadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting components metadata from blob {BlobPath}", componentsBlobId);
                throw;
            }
        }

        public async Task<string> CreateComponentsMetadataAsync(ComponentsMetadata metadata, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateComponentsMetadataPath(Guid.Parse(metadata.TenantId), Guid.Parse(metadata.ReportId), Guid.Parse(metadata.VersionId));
                _logger.LogDebug("Creating components metadata at blob {BlobPath}", blobPath);
                
                metadata.GeneratedAt = DateTime.UtcNow;
                var json = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
                
                var blobClient = _containerClient.GetBlobClient(blobPath);
                
                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = "application/json"
                    },
                    Metadata = new Dictionary<string, string>
                    {
                        ["tenantId"] = metadata.TenantId,
                        ["reportId"] = metadata.ReportId,
                        ["versionId"] = metadata.VersionId,
                        ["componentCount"] = metadata.Components.Count.ToString(),
                        ["uploadedAt"] = DateTime.UtcNow.ToString("O")
                    }
                };
                
                using var stream = new MemoryStream(Encoding.UTF8.GetBytes(json));
                await blobClient.UploadAsync(stream, uploadOptions, cancellationToken);
                
                _logger.LogInformation("Created components metadata at blob {BlobPath}", blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating components metadata");
                throw;
            }
        }

        public async Task<string> UpdateComponentsMetadataAsync(ComponentsMetadata metadata, CancellationToken cancellationToken = default)
        {
            // For blob storage, update is the same as create (overwrite)
            return await CreateComponentsMetadataAsync(metadata, cancellationToken);
        }

        public async Task DeleteComponentsAsync(string componentsBlobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting components at blob {BlobPath}", componentsBlobId);
                
                // Get metadata first to find all related blobs
                var metadata = await GetComponentsMetadataAsync(componentsBlobId, cancellationToken);
                if (metadata != null)
                {
                    // Delete individual component files
                    var tenantId = Guid.Parse(metadata.TenantId);
                    var reportId = Guid.Parse(metadata.ReportId);
                    var versionId = Guid.Parse(metadata.VersionId);
                    
                    foreach (var component in metadata.Components)
                    {
                        var componentPath = GenerateComponentPath(tenantId, reportId, versionId, component.SectionId);
                        var componentBlobClient = _containerClient.GetBlobClient(componentPath);
                        await componentBlobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                    }
                    
                    // Delete assets
                    var assetsPrefix = $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/assets/";
                    await foreach (var blobItem in _containerClient.GetBlobsAsync(prefix: assetsPrefix, cancellationToken: cancellationToken))
                    {
                        var assetBlobClient = _containerClient.GetBlobClient(blobItem.Name);
                        await assetBlobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                    }
                }
                
                // Delete metadata file
                var metadataBlobClient = _containerClient.GetBlobClient(componentsBlobId);
                await metadataBlobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                
                _logger.LogInformation("Deleted components at blob {BlobPath}", componentsBlobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting components at blob {BlobPath}", componentsBlobId);
                throw;
            }
        }

        // Individual Component Operations
        public async Task<ComponentDefinition?> GetComponentAsync(Guid reportId, Guid versionId, Guid tenantId, string sectionId, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateComponentPath(tenantId, reportId, versionId, sectionId);
                _logger.LogDebug("Getting component {SectionId} from blob {BlobPath}", sectionId, blobPath);
                
                var blobClient = _containerClient.GetBlobClient(blobPath);
                
                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Component blob {BlobPath} not found", blobPath);
                    return null;
                }
                
                var response = await blobClient.DownloadContentAsync(cancellationToken);
                var json = response.Value.Content.ToString();
                var component = JsonSerializer.Deserialize<ComponentDefinition>(json);
                
                _logger.LogInformation("Retrieved component {SectionId} from blob {BlobPath}", sectionId, blobPath);
                return component;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting component {SectionId}", sectionId);
                throw;
            }
        }

        public async Task<List<ComponentDefinition>> GetComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var metadata = await GetComponentsMetadataAsync(reportId, versionId, tenantId, cancellationToken);
            if (metadata == null)
            {
                _logger.LogWarning("No components metadata found for report {ReportId} version {VersionId}", reportId, versionId);
                return new List<ComponentDefinition>();
            }
            
            var components = new List<ComponentDefinition>();
            foreach (var componentInfo in metadata.Components)
            {
                var component = await GetComponentAsync(reportId, versionId, tenantId, componentInfo.SectionId, cancellationToken);
                if (component != null)
                {
                    components.Add(component);
                }
            }
            
            return components;
        }

        public async Task<string> SaveComponentAsync(Guid reportId, Guid versionId, Guid tenantId, ComponentDefinition component, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateComponentPath(tenantId, reportId, versionId, component.SectionId);
                _logger.LogDebug("Saving component {SectionId} to blob {BlobPath}", component.SectionId, blobPath);
                
                component.GeneratedAt = DateTime.UtcNow;
                var json = JsonSerializer.Serialize(component, new JsonSerializerOptions { WriteIndented = true });
                
                var blobClient = _containerClient.GetBlobClient(blobPath);
                
                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = "application/json"
                    },
                    Metadata = new Dictionary<string, string>
                    {
                        ["tenantId"] = tenantId.ToString(),
                        ["reportId"] = reportId.ToString(),
                        ["versionId"] = versionId.ToString(),
                        ["sectionId"] = component.SectionId,
                        ["uploadedAt"] = DateTime.UtcNow.ToString("O")
                    }
                };
                
                using var stream = new MemoryStream(Encoding.UTF8.GetBytes(json));
                await blobClient.UploadAsync(stream, uploadOptions, cancellationToken);
                
                _logger.LogInformation("Saved component {SectionId} to blob {BlobPath}", component.SectionId, blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving component {SectionId}", component.SectionId);
                throw;
            }
        }

        public async Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, List<ComponentDefinition> components, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Saving {Count} components for report {ReportId} version {VersionId}", components.Count, reportId, versionId);
            
            // Save individual components
            var componentTasks = components.Select(c => SaveComponentAsync(reportId, versionId, tenantId, c, cancellationToken));
            await Task.WhenAll(componentTasks);
            
            // Create and save metadata
            var metadata = new ComponentsMetadata
            {
                ReportId = reportId.ToString(),
                VersionId = versionId.ToString(),
                TenantId = tenantId.ToString(),
                GeneratedAt = DateTime.UtcNow,
                Components = components.Select(c => new ComponentInfo
                {
                    SectionId = c.SectionId,
                    Name = c.Name,
                    Hash = c.Hash,
                    GeneratedAt = c.GeneratedAt
                }).ToList()
            };
            
            var metadataPath = await CreateComponentsMetadataAsync(metadata, cancellationToken);
            _logger.LogInformation("Saved {Count} components and metadata for report {ReportId} version {VersionId}", components.Count, reportId, versionId);
            
            return metadataPath;
        }

        public async Task DeleteComponentAsync(Guid reportId, Guid versionId, Guid tenantId, string sectionId, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateComponentPath(tenantId, reportId, versionId, sectionId);
                _logger.LogDebug("Deleting component {SectionId} at blob {BlobPath}", sectionId, blobPath);
                
                var blobClient = _containerClient.GetBlobClient(blobPath);
                await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                
                _logger.LogInformation("Deleted component {SectionId}", sectionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting component {SectionId}", sectionId);
                throw;
            }
        }

        // Path Generation
        public string GenerateComponentsPath(Guid tenantId, Guid reportId, Guid versionId)
        {
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/components/";
        }

        public string GenerateComponentPath(Guid tenantId, Guid reportId, Guid versionId, string sectionId)
        {
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/components/{sectionId}.json";
        }

        public string GenerateAssetPath(Guid tenantId, Guid reportId, Guid versionId, string assetName)
        {
            var sanitizedAssetName = assetName.Replace(" ", "_").Replace("\\", "/");
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/assets/{sanitizedAssetName}";
        }

        private string GenerateComponentsMetadataPath(Guid tenantId, Guid reportId, Guid versionId)
        {
            return $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/components/metadata.json";
        }

        // Bulk Operations
        public async Task<Dictionary<Guid, List<ComponentDefinition>>> GetComponentsBulkAsync(List<(Guid reportId, Guid versionId, Guid tenantId)> requests, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting components for {Count} report versions", requests.Count);

            var results = new Dictionary<Guid, List<ComponentDefinition>>();
            var tasks = requests.Select(async request =>
            {
                var components = await GetComponentsAsync(request.reportId, request.versionId, request.tenantId, cancellationToken);
                return new { VersionId = request.versionId, Components = components };
            });

            var completedTasks = await Task.WhenAll(tasks);
            foreach (var result in completedTasks)
            {
                results[result.VersionId] = result.Components;
            }

            return results;
        }

        public async Task DeleteComponentsBulkAsync(List<string> componentsBlobIds, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Deleting {Count} component sets", componentsBlobIds.Count);

            var tasks = componentsBlobIds.Select(blobId => DeleteComponentsAsync(blobId, cancellationToken));
            await Task.WhenAll(tasks);
        }

        // Asset Operations
        public async Task<string> SaveAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, Stream assetStream, string contentType, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateAssetPath(tenantId, reportId, versionId, assetName);
                _logger.LogDebug("Saving asset {AssetName} to blob {BlobPath}", assetName, blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);

                var uploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = contentType
                    },
                    Metadata = new Dictionary<string, string>
                    {
                        ["tenantId"] = tenantId.ToString(),
                        ["reportId"] = reportId.ToString(),
                        ["versionId"] = versionId.ToString(),
                        ["assetName"] = assetName,
                        ["uploadedAt"] = DateTime.UtcNow.ToString("O")
                    }
                };

                await blobClient.UploadAsync(assetStream, uploadOptions, cancellationToken);

                _logger.LogInformation("Saved asset {AssetName} to blob {BlobPath}", assetName, blobPath);
                return blobPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving asset {AssetName}", assetName);
                throw;
            }
        }

        public async Task<Stream?> GetAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateAssetPath(tenantId, reportId, versionId, assetName);
                _logger.LogDebug("Getting asset {AssetName} from blob {BlobPath}", assetName, blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);

                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Asset blob {BlobPath} not found", blobPath);
                    return null;
                }

                var response = await blobClient.DownloadStreamingAsync(cancellationToken: cancellationToken);
                return response.Value.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting asset {AssetName}", assetName);
                throw;
            }
        }

        public async Task DeleteAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobPath = GenerateAssetPath(tenantId, reportId, versionId, assetName);
                _logger.LogDebug("Deleting asset {AssetName} at blob {BlobPath}", assetName, blobPath);

                var blobClient = _containerClient.GetBlobClient(blobPath);
                await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);

                _logger.LogInformation("Deleted asset {AssetName}", assetName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting asset {AssetName}", assetName);
                throw;
            }
        }

        public async Task<List<string>> ListAssetsAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var assetsPrefix = $"tenants/{tenantId}/reports/{reportId}/versions/{versionId}/assets/";
                _logger.LogDebug("Listing assets with prefix {Prefix}", assetsPrefix);

                var assets = new List<string>();
                await foreach (var blobItem in _containerClient.GetBlobsAsync(prefix: assetsPrefix, cancellationToken: cancellationToken))
                {
                    var assetName = blobItem.Name.Substring(assetsPrefix.Length);
                    assets.Add(assetName);
                }

                return assets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing assets for report {ReportId} version {VersionId}", reportId, versionId);
                throw;
            }
        }

        // Migration Operations
        public async Task<string> CreateFromLegacyComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, List<object> legacyComponents, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Creating components from {Count} legacy components", legacyComponents.Count);

            var components = new List<ComponentDefinition>();
            foreach (var legacyComponent in legacyComponents)
            {
                // Convert legacy component to new format
                var json = JsonSerializer.Serialize(legacyComponent);
                var legacyData = JsonSerializer.Deserialize<Dictionary<string, object>>(json);

                if (legacyData != null)
                {
                    var component = new ComponentDefinition
                    {
                        Id = legacyData.GetValueOrDefault("Id")?.ToString() ?? Guid.NewGuid().ToString(),
                        Name = legacyData.GetValueOrDefault("SectionName")?.ToString() ?? "Unknown",
                        SectionId = legacyData.GetValueOrDefault("SectionId")?.ToString() ?? "unknown",
                        ComponentCode = legacyData.GetValueOrDefault("ComponentCode")?.ToString() ?? "",
                        TypeDefinitions = legacyData.GetValueOrDefault("TypeDefinitions")?.ToString() ?? "",
                        Hash = legacyData.GetValueOrDefault("ComponentHash")?.ToString() ?? ""
                    };

                    // Parse imports and props if available
                    if (legacyData.ContainsKey("ImportsJson"))
                    {
                        var importsJson = legacyData["ImportsJson"]?.ToString();
                        if (!string.IsNullOrEmpty(importsJson))
                        {
                            component.Imports = JsonSerializer.Deserialize<List<string>>(importsJson) ?? new List<string>();
                        }
                    }

                    components.Add(component);
                }
            }

            return await SaveComponentsAsync(reportId, versionId, tenantId, components, cancellationToken);
        }

        public async Task<string> MigrateFromSqlAsync(Guid reportId, Guid versionId, Guid tenantId, string componentDataJson, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Migrating components from SQL for report {ReportId} version {VersionId}", reportId, versionId);

            var legacyComponents = JsonSerializer.Deserialize<List<object>>(componentDataJson) ?? new List<object>();
            return await CreateFromLegacyComponentsAsync(reportId, versionId, tenantId, legacyComponents, cancellationToken);
        }

        // Validation Operations
        public async Task<(bool isValid, List<string> errors)> ValidateComponentsAsync(List<ComponentDefinition> components, CancellationToken cancellationToken = default)
        {
            var errors = new List<string>();

            foreach (var component in components)
            {
                if (string.IsNullOrEmpty(component.SectionId))
                    errors.Add($"Component {component.Name} is missing SectionId");

                if (string.IsNullOrEmpty(component.ComponentCode))
                    errors.Add($"Component {component.SectionId} is missing ComponentCode");

                if (string.IsNullOrEmpty(component.Hash))
                    errors.Add($"Component {component.SectionId} is missing Hash");
            }

            // Check for duplicate section IDs
            var duplicates = components.GroupBy(c => c.SectionId).Where(g => g.Count() > 1).Select(g => g.Key);
            foreach (var duplicate in duplicates)
            {
                errors.Add($"Duplicate SectionId found: {duplicate}");
            }

            return (errors.Count == 0, errors);
        }

        public async Task<(long size, DateTime lastModified, int componentCount)> GetComponentsInfoAsync(string componentsBlobId, CancellationToken cancellationToken = default)
        {
            var metadata = await GetComponentsMetadataAsync(componentsBlobId, cancellationToken);
            if (metadata == null)
                throw new InvalidOperationException($"Components metadata {componentsBlobId} not found");

            var blobClient = _containerClient.GetBlobClient(componentsBlobId);
            var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);

            return (properties.Value.ContentLength, properties.Value.LastModified.DateTime, metadata.Components.Count);
        }

        // Versioning Operations
        public async Task<string> CopyComponentsAsync(string sourceComponentsBlobId, Guid newReportId, Guid newVersionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Copying components from {SourceBlobId} to new version", sourceComponentsBlobId);

            var sourceMetadata = await GetComponentsMetadataAsync(sourceComponentsBlobId, cancellationToken);
            if (sourceMetadata == null)
                throw new InvalidOperationException($"Source components metadata {sourceComponentsBlobId} not found");

            // Get all source components
            var sourceReportId = Guid.Parse(sourceMetadata.ReportId);
            var sourceVersionId = Guid.Parse(sourceMetadata.VersionId);
            var sourceTenantId = Guid.Parse(sourceMetadata.TenantId);

            var sourceComponents = await GetComponentsAsync(sourceReportId, sourceVersionId, sourceTenantId, cancellationToken);

            // Create new components with updated IDs
            var newComponents = sourceComponents.Select(c => new ComponentDefinition
            {
                Id = Guid.NewGuid().ToString(),
                Name = c.Name,
                SectionId = c.SectionId,
                ComponentCode = c.ComponentCode,
                TypeDefinitions = c.TypeDefinitions,
                Hash = c.Hash,
                Imports = c.Imports.ToList(),
                Props = c.Props.ToList()
            }).ToList();

            return await SaveComponentsAsync(newReportId, newVersionId, tenantId, newComponents, cancellationToken);
        }

        public async Task<List<string>> CompareComponentsAsync(string componentsBlobId1, string componentsBlobId2, CancellationToken cancellationToken = default)
        {
            var metadata1 = await GetComponentsMetadataAsync(componentsBlobId1, cancellationToken);
            var metadata2 = await GetComponentsMetadataAsync(componentsBlobId2, cancellationToken);

            var differences = new List<string>();

            if (metadata1 == null || metadata2 == null)
            {
                differences.Add("One or both component sets not found");
                return differences;
            }

            if (metadata1.Components.Count != metadata2.Components.Count)
                differences.Add($"Component count differs: {metadata1.Components.Count} vs {metadata2.Components.Count}");

            var components1Dict = metadata1.Components.ToDictionary(c => c.SectionId);
            var components2Dict = metadata2.Components.ToDictionary(c => c.SectionId);

            foreach (var sectionId in components1Dict.Keys.Union(components2Dict.Keys))
            {
                var comp1 = components1Dict.GetValueOrDefault(sectionId);
                var comp2 = components2Dict.GetValueOrDefault(sectionId);

                if (comp1 == null)
                    differences.Add($"Component '{sectionId}' was added");
                else if (comp2 == null)
                    differences.Add($"Component '{sectionId}' was removed");
                else if (comp1.Hash != comp2.Hash)
                    differences.Add($"Component '{sectionId}' was modified");
            }

            return differences;
        }
    }
}
