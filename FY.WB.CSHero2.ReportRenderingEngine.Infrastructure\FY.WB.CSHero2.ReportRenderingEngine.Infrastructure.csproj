<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.4" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.46" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.51.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Application\FY.WB.CSHero2.ReportRenderingEngine.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj" />
  </ItemGroup>

</Project>
