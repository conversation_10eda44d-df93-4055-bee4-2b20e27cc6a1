import { NextRequest, NextResponse } from 'next/server';

// Mark this route as dynamic since it uses request.url
export const dynamic = 'force-dynamic';
import { getSubscriptionByTypeAndBillingCycle } from '@/lib/api';

/**
 * GET /api/v1/subscriptions/by-type-and-cycle
 * Get a subscription by type and billing cycle
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const type = searchParams.get('type') as 'basic' | 'professional' | 'enterprise' | null;
    const billingCycle = searchParams.get('billingCycle') as 'monthly' | 'quarterly' | 'annual' | null;
    
    if (!type || !billingCycle) {
      return NextResponse.json(
        { error: 'Type and billingCycle parameters are required' },
        { status: 400 }
      );
    }
    
    const subscription = await getSubscriptionByTypeAndBillingCycle(type, billingCycle);
    
    if (!subscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(subscription);
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
}
