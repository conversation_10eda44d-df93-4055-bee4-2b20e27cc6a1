using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Blob Storage implementation of the report style service
    /// Stores HTML/CSS content in Azure Blob Storage following the multi-storage architecture
    /// </summary>
    public class BlobReportStyleService : IReportStyleService
    {
        private readonly BlobServiceClient _blobClient;
        private readonly ILogger<BlobReportStyleService> _logger;
        private readonly BlobStorageOptions _options;
        private const string DefaultContainerName = "report-components";

        public BlobReportStyleService(
            BlobServiceClient blobClient,
            IOptions<BlobStorageOptions> options,
            ILogger<BlobReportStyleService> logger)
        {
            _blobClient = blobClient ?? throw new ArgumentNullException(nameof(blobClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ReportStyleDocument?> GetStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobClient.GetBlobContainerClient(DefaultContainerName);
                var blobPath = GetStyleBlobPath(styleDocumentId, tenantId);

                var blobClient = containerClient.GetBlobClient(blobPath);

                if (!await blobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Style document {StyleDocumentId} not found for tenant {TenantId}", styleDocumentId, tenantId);
                    return null;
                }

                var response = await blobClient.DownloadContentAsync(cancellationToken);
                var jsonContent = response.Value.Content.ToString();

                var styleDocument = JsonSerializer.Deserialize<ReportStyleDocument>(jsonContent);

                _logger.LogDebug("Retrieved style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                return styleDocument;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                throw;
            }
        }

        public async Task<ReportStyleDocument?> GetReportStyleAsync(Guid reportVersionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateReportStyleId(reportVersionId);
            return await GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public async Task<ReportStyleDocument?> GetTemplateStyleAsync(Guid templateId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateTemplateStyleId(templateId);
            return await GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public async Task<string> SaveStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobClient.GetBlobContainerClient(DefaultContainerName);
                await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);

                var blobPath = GetStyleBlobPath(style.Id, style.TenantId);
                var blobClient = containerClient.GetBlobClient(blobPath);

                // Update timestamps
                style.LastModified = DateTime.UtcNow;
                if (style.CreatedAt == default)
                {
                    style.CreatedAt = DateTime.UtcNow;
                }

                var jsonContent = JsonSerializer.Serialize(style, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                var content = BinaryData.FromString(jsonContent);
                await blobClient.UploadAsync(content, overwrite: true, cancellationToken);

                // Set metadata
                var metadata = new Dictionary<string, string>
                {
                    ["tenantId"] = style.TenantId.ToString(),
                    ["styleType"] = style.StyleType,
                    ["version"] = style.Version.ToString(),
                    ["lastModified"] = style.LastModified.ToString("O")
                };

                await blobClient.SetMetadataAsync(metadata, cancellationToken: cancellationToken);

                _logger.LogInformation("Saved style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                return style.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                throw;
            }
        }

        public async Task<bool> UpdateStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            try
            {
                // Update the style document
                style.UpdateModified(style.LastModifiedBy);
                await SaveStyleAsync(style, cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating style document {StyleId} for tenant {TenantId}", style.Id, style.TenantId);
                return false;
            }
        }

        public async Task<bool> DeleteStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobClient.GetBlobContainerClient(DefaultContainerName);
                var blobPath = GetStyleBlobPath(styleDocumentId, tenantId);
                var blobClient = containerClient.GetBlobClient(blobPath);

                var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);

                if (response.Value)
                {
                    _logger.LogInformation("Deleted style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                }
                else
                {
                    _logger.LogWarning("Style document {StyleDocumentId} not found for deletion for tenant {TenantId}", styleDocumentId, tenantId);
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting style document {StyleDocumentId} for tenant {TenantId}", styleDocumentId, tenantId);
                return false;
            }
        }

        public async Task<List<ReportStyleDocument>> GetStylesByTenantAsync(Guid tenantId, string? styleType = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobClient.GetBlobContainerClient(DefaultContainerName);
                var styles = new List<ReportStyleDocument>();

                var tenantPrefix = $"tenants/{tenantId}/styles/";

                await foreach (var blobItem in containerClient.GetBlobsAsync(prefix: tenantPrefix, cancellationToken: cancellationToken))
                {
                    try
                    {
                        var blobClient = containerClient.GetBlobClient(blobItem.Name);
                        var response = await blobClient.DownloadContentAsync(cancellationToken);
                        var jsonContent = response.Value.Content.ToString();

                        var styleDocument = JsonSerializer.Deserialize<ReportStyleDocument>(jsonContent);

                        if (styleDocument != null &&
                            (string.IsNullOrEmpty(styleType) || styleDocument.StyleType == styleType))
                        {
                            styles.Add(styleDocument);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error deserializing style document {BlobName} for tenant {TenantId}", blobItem.Name, tenantId);
                    }
                }

                _logger.LogDebug("Retrieved {Count} style documents for tenant {TenantId} with type filter '{StyleType}'",
                    styles.Count, tenantId, styleType ?? "all");

                return styles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting styles for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<string> CopyStyleForReportVersionAsync(string sourceStyleId, Guid targetReportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            try
            {
                // Get the source style
                var sourceStyle = await GetStyleAsync(sourceStyleId, tenantId, cancellationToken);
                if (sourceStyle is null)
                {
                    throw new InvalidOperationException($"Source style {sourceStyleId} not found for tenant {tenantId}");
                }

                // Create a new style for the target report version
                var targetStyleId = ReportStyleDocument.CreateReportStyleId(targetReportVersionId);
                var targetStyle = new ReportStyleDocument
                {
                    Id = targetStyleId,
                    PartitionKey = tenantId.ToString(),
                    ReportVersionId = targetReportVersionId,
                    TenantId = tenantId,
                    StyleType = "report",
                    HtmlContent = sourceStyle.HtmlContent,
                    CssStyles = sourceStyle.CssStyles,
                    InlineStyles = new Dictionary<string, object>(sourceStyle.InlineStyles),
                    ComponentStyles = CopyComponentStyles(sourceStyle.ComponentStyles),
                    Metadata = CopyStyleMetadata(sourceStyle.Metadata),
                    CreatedAt = DateTime.UtcNow,
                    LastModified = DateTime.UtcNow,
                    CreatedBy = userId,
                    LastModifiedBy = userId,
                    Version = 1,
                    IsActive = true
                };

                await SaveStyleAsync(targetStyle, cancellationToken);

                _logger.LogInformation("Copied style from {SourceStyleId} to {TargetStyleId} for tenant {TenantId}",
                    sourceStyleId, targetStyleId, tenantId);

                return targetStyleId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying style from {SourceStyleId} to report version {TargetReportVersionId} for tenant {TenantId}",
                    sourceStyleId, targetReportVersionId, tenantId);
                throw;
            }
        }

        public async Task<string> CreateStyleFromTemplateAsync(Guid templateId, Guid reportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            try
            {
                // Get the template style
                var templateStyle = await GetTemplateStyleAsync(templateId, tenantId, cancellationToken);
                if (templateStyle is null)
                {
                    throw new InvalidOperationException($"Template style for template {templateId} not found for tenant {tenantId}");
                }

                // Create a new style for the report version based on the template
                var reportStyleId = ReportStyleDocument.CreateReportStyleId(reportVersionId);
                var reportStyle = new ReportStyleDocument
                {
                    Id = reportStyleId,
                    PartitionKey = tenantId.ToString(),
                    ReportVersionId = reportVersionId,
                    TenantId = tenantId,
                    StyleType = "report",
                    HtmlContent = templateStyle.HtmlContent,
                    CssStyles = templateStyle.CssStyles,
                    InlineStyles = new Dictionary<string, object>(templateStyle.InlineStyles),
                    ComponentStyles = CopyComponentStyles(templateStyle.ComponentStyles),
                    Metadata = CopyStyleMetadata(templateStyle.Metadata),
                    CreatedAt = DateTime.UtcNow,
                    LastModified = DateTime.UtcNow,
                    CreatedBy = userId,
                    LastModifiedBy = userId,
                    Version = 1,
                    IsActive = true
                };

                await SaveStyleAsync(reportStyle, cancellationToken);

                _logger.LogInformation("Created style {ReportStyleId} from template {TemplateId} for tenant {TenantId}",
                    reportStyleId, templateId, tenantId);

                return reportStyleId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating style from template {TemplateId} for report version {ReportVersionId} for tenant {TenantId}",
                    templateId, reportVersionId, tenantId);
                throw;
            }
        }

        public async Task<List<ReportStyleDocument>> SearchStylesAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default)
        {
            try
            {
                var allStyles = await GetStylesByTenantAsync(tenantId, cancellationToken: cancellationToken);

                var searchResults = allStyles.Where(style =>
                    style.Metadata.Tags.Any(tag => tag.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                    style.Metadata.Theme.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    style.Metadata.Framework.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    style.StyleType.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                _logger.LogDebug("Found {Count} style documents matching search term '{SearchTerm}' for tenant {TenantId}",
                    searchResults.Count, searchTerm, tenantId);

                return searchResults;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching styles for tenant {TenantId} with term '{SearchTerm}'", tenantId, searchTerm);
                throw;
            }
        }

        public async Task<List<ReportStyleDocument>> GetStylesByFrameworkAsync(Guid tenantId, string framework, CancellationToken cancellationToken = default)
        {
            try
            {
                var allStyles = await GetStylesByTenantAsync(tenantId, cancellationToken: cancellationToken);

                var frameworkStyles = allStyles.Where(style =>
                    style.Metadata.Framework.Equals(framework, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                _logger.LogDebug("Found {Count} style documents using framework '{Framework}' for tenant {TenantId}",
                    frameworkStyles.Count, framework, tenantId);

                return frameworkStyles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting styles by framework '{Framework}' for tenant {TenantId}", framework, tenantId);
                throw;
            }
        }

        public async Task<StyleValidationResult> ValidateStyleAsync(ReportStyleDocument style)
        {
            try
            {
                var errors = new List<string>();
                var warnings = new List<string>();

                // Basic validation
                if (string.IsNullOrEmpty(style.Id))
                    errors.Add("Style document ID is required");

                if (style.TenantId == Guid.Empty)
                    errors.Add("Tenant ID is required");

                if (string.IsNullOrEmpty(style.StyleType))
                    errors.Add("Style type is required");

                // Content validation
                if (string.IsNullOrEmpty(style.HtmlContent) && string.IsNullOrEmpty(style.CssStyles))
                    warnings.Add("Style document has no HTML or CSS content");

                // Framework validation
                if (string.IsNullOrEmpty(style.Metadata.Framework))
                    warnings.Add("No CSS framework specified");

                // Component validation
                if (style.ComponentStyles.Count == 0)
                    warnings.Add("No component styles defined");

                _logger.LogDebug("Validated style document {StyleId} - {ErrorCount} errors, {WarningCount} warnings",
                    style.Id, errors.Count, warnings.Count);

                return new StyleValidationResult
                {
                    IsValid = errors.Count == 0,
                    Errors = errors,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating style document {StyleId}", style.Id);
                return StyleValidationResult.Failure($"Validation error: {ex.Message}");
            }
        }

        // Helper method for additional functionality (not in interface)
        public async Task SaveRenderedComponentAsync(string tenantId, string reportId, string version, string componentName, string htmlContent, string cssContent)
        {
            var containerClient = _blobClient.GetBlobContainerClient(DefaultContainerName);
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.None);

            var componentBlobPath = $"tenants/{tenantId}/reports/{reportId}/versions/v{version}/components/{componentName}.html";
            var cssBlobPath = $"tenants/{tenantId}/reports/{reportId}/versions/v{version}/components/{componentName}.css";

            await containerClient.UploadBlobAsync(componentBlobPath, BinaryData.FromString(htmlContent));
            await containerClient.UploadBlobAsync(cssBlobPath, BinaryData.FromString(cssContent));

            _logger.LogInformation("Saved rendered component and CSS to Blob Storage for tenant {TenantId}, report {ReportId}, version {Version}, component {ComponentName}.",
                tenantId, reportId, version, componentName);
        }

        #region Private Helper Methods

        private static string GetStyleBlobPath(string styleDocumentId, Guid tenantId)
        {
            return $"tenants/{tenantId}/styles/{styleDocumentId}.json";
        }

        private static Dictionary<string, ComponentStyle> CopyComponentStyles(Dictionary<string, ComponentStyle> source)
        {
            var copy = new Dictionary<string, ComponentStyle>();
            foreach (var kvp in source)
            {
                copy[kvp.Key] = new ComponentStyle
                {
                    ComponentId = kvp.Value.ComponentId,
                    ComponentType = kvp.Value.ComponentType,
                    CssClasses = new List<string>(kvp.Value.CssClasses),
                    InlineStyles = new Dictionary<string, string>(kvp.Value.InlineStyles),
                    CustomCss = kvp.Value.CustomCss,
                    Layout = new ComponentLayout
                    {
                        Width = kvp.Value.Layout.Width,
                        Height = kvp.Value.Layout.Height,
                        Margin = kvp.Value.Layout.Margin,
                        Padding = kvp.Value.Layout.Padding,
                        Display = kvp.Value.Layout.Display,
                        Position = kvp.Value.Layout.Position
                    }
                };
            }
            return copy;
        }

        private static StyleMetadata CopyStyleMetadata(StyleMetadata source)
        {
            return new StyleMetadata
            {
                Framework = source.Framework,
                FrameworkVersion = source.FrameworkVersion,
                Theme = source.Theme,
                ColorScheme = source.ColorScheme,
                Tags = new List<string>(source.Tags),
                CustomProperties = new Dictionary<string, object>(source.CustomProperties),
                Breakpoints = new Dictionary<string, string>(source.Breakpoints)
            };
        }

        #endregion
    }
}
