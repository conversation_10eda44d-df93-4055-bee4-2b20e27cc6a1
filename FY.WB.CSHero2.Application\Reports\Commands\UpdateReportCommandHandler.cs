using FY.WB.CSHero2.Application.Common.Exceptions;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class UpdateReportCommandHandler : IRequestHandler<UpdateReportCommand>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<UpdateReportCommandHandler> _logger;

        public UpdateReportCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService,
            ILogger<UpdateReportCommandHandler> logger)
        {
            _context = context;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task Handle(UpdateReportCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating report with ID: {ReportId}", request.Dto.Id);
            
            // Load the report with its sections only (avoid loading fields that don't exist in the database)
            var entity = await _context.Reports
                .IgnoreAutoIncludes()
                .Include(r => r.Sections)
                .FirstOrDefaultAsync(r => r.Id == request.Dto.Id, cancellationToken);

            if (entity == null)
            {
                throw new NotFoundException(nameof(Report), request.Dto.Id);
            }

            if (!_currentUserService.TenantId.HasValue || entity.TenantId != _currentUserService.TenantId)
            {
                throw new ForbiddenAccessException("User is not authorized to update this report.");
            }

            // Update basic report properties
            entity.UpdateDetails(
                request.Dto.Name,
                request.Dto.Category,
                request.Dto.SlideCount,
                request.Dto.Status,
                request.Dto.Author
            );

            // Update sections and fields if provided
            if (request.Dto.Content != null && request.Dto.Content.Sections != null)
            {
                await UpdateReportSections(entity, request.Dto.Content.Sections, cancellationToken);
            }

            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully updated report {ReportId} with name '{ReportName}'", entity.Id, entity.Name);
        }

        private async Task UpdateReportSections(
            Report report,
            List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionDto> sectionDtos,
            CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating {SectionCount} sections for report {ReportId}", sectionDtos.Count, report.Id);
            
            // Get existing sections to compare
            var existingSections = report.Sections.ToList();
            var processedSectionIds = new HashSet<Guid>();
            
            // Process each section from the DTO
            for (int i = 0; i < sectionDtos.Count; i++)
            {
                var sectionDto = sectionDtos[i];
                var sectionTitle = !string.IsNullOrEmpty(sectionDto.Title) ? sectionDto.Title : sectionDto.SectionTitle;
                
                // Try to parse the section ID if provided
                Guid sectionId;
                bool isExistingSection = Guid.TryParse(sectionDto.Id, out sectionId) &&
                                        existingSections.Any(s => s.Id == sectionId);
                
                if (isExistingSection)
                {
                    // Update existing section
                    var existingSection = existingSections.First(s => s.Id == sectionId);
                    existingSection.UpdateDetails(
                        sectionTitle ?? $"Section {i + 1}",
                        sectionDto.Type ?? "text",
                        sectionDto.Order > 0 ? sectionDto.Order : i
                    );
                    
                    if (sectionDto.Content != null)
                    {
                        existingSection.Content = JsonSerializer.Serialize(sectionDto.Content);
                    }
                    
                    processedSectionIds.Add(existingSection.Id);
                    
                    // Skip updating fields as they don't exist in the database
                }
                else
                {
                    // Create new section with a pre-assigned ID
                    sectionId = Guid.NewGuid();
                    var sectionType = sectionDto.Type ?? "text";
                    var sectionOrder = sectionDto.Order > 0 ? sectionDto.Order : i;
                    
                    // Use constructor that accepts ID
                    var newSection = new ReportSection(
                        sectionId,
                        report.Id,
                        sectionTitle ?? $"Section {i + 1}",
                        sectionType,
                        sectionOrder)
                    {
                        Content = sectionDto.Content != null ? JsonSerializer.Serialize(sectionDto.Content) : string.Empty,
                        TenantId = _currentUserService.TenantId.Value
                    };
                    
                    _context.ReportSections.Add(newSection);
                    processedSectionIds.Add(sectionId);
                    
                    // Skip creating fields as they don't exist in the database
                }
            }
            
            // Remove sections that are no longer in the DTO
            var sectionsToRemove = existingSections.Where(s => !processedSectionIds.Contains(s.Id)).ToList();
            foreach (var sectionToRemove in sectionsToRemove)
            {
                _logger.LogInformation("Removing section {SectionId} from report {ReportId}", sectionToRemove.Id, report.Id);
                _context.ReportSections.Remove(sectionToRemove);
                // Fields will be cascade deleted by EF Core
            }
        }
        
        private async Task UpdateSectionFields(
            ReportSection section,
            List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionFieldDto> fieldDtos,
            CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating {FieldCount} fields for section {SectionId}", fieldDtos.Count, section.Id);
            
            // Get existing fields to compare
            var existingFields = section.Fields.ToList();
            var processedFieldIds = new HashSet<Guid>();
            
            // Process each field from the DTO
            for (int i = 0; i < fieldDtos.Count; i++)
            {
                var fieldDto = fieldDtos[i];
                
                // Try to parse the field ID if provided
                Guid fieldId;
                bool isExistingField = Guid.TryParse(fieldDto.Id, out fieldId) &&
                                      existingFields.Any(f => f.Id == fieldId);
                
                if (isExistingField)
                {
                    // Update existing field
                    var existingField = existingFields.First(f => f.Id == fieldId);
                    existingField.UpdateDetails(
                        fieldDto.Name ?? $"field_{i}",
                        fieldDto.Type ?? "text",
                        fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty,
                        fieldDto.Order > 0 ? fieldDto.Order : i
                    );
                    
                    if (fieldDto.Value != null)
                    {
                        existingField.Value = JsonSerializer.Serialize(fieldDto.Value);
                    }
                    
                    existingField.Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {i + 1}";
                    existingField.IsRequired = fieldDto.IsRequired;
                    
                    if (fieldDto.Metadata != null)
                    {
                        existingField.Metadata = JsonSerializer.Serialize(fieldDto.Metadata);
                    }
                    
                    processedFieldIds.Add(existingField.Id);
                }
                else
                {
                    // Create new field
                    fieldId = Guid.NewGuid();
                    
                    var field = new ReportSectionField(
                        fieldId,
                        section.Id,
                        fieldDto.Name ?? $"field_{i}",
                        fieldDto.Type ?? "text",
                        fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty,
                        fieldDto.Order > 0 ? fieldDto.Order : i)
                    {
                        Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {i + 1}",
                        Value = fieldDto.Value != null ? JsonSerializer.Serialize(fieldDto.Value) : string.Empty,
                        IsRequired = fieldDto.IsRequired,
                        Metadata = fieldDto.Metadata != null ? JsonSerializer.Serialize(fieldDto.Metadata) : string.Empty,
                        TenantId = _currentUserService.TenantId.Value
                    };
                    
                    _context.ReportSectionFields.Add(field);
                    processedFieldIds.Add(fieldId);
                }
            }
            
            // Remove fields that are no longer in the DTO
            var fieldsToRemove = existingFields.Where(f => !processedFieldIds.Contains(f.Id)).ToList();
            foreach (var fieldToRemove in fieldsToRemove)
            {
                _logger.LogInformation("Removing field {FieldId} from section {SectionId}", fieldToRemove.Id, section.Id);
                _context.ReportSectionFields.Remove(fieldToRemove);
            }
        }
        
        private Task CreateSectionFields(
            Guid sectionId,
            List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionFieldDto> fieldDtos,
            CancellationToken cancellationToken)
        {
            for (int i = 0; i < fieldDtos.Count; i++)
            {
                var fieldDto = fieldDtos[i];
                var fieldId = Guid.NewGuid();
                
                var field = new ReportSectionField(
                    fieldId,
                    sectionId,
                    fieldDto.Name ?? $"field_{i}",
                    fieldDto.Type ?? "text",
                    fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty,
                    fieldDto.Order > 0 ? fieldDto.Order : i)
                {
                    Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {i + 1}",
                    Value = fieldDto.Value != null ? JsonSerializer.Serialize(fieldDto.Value) : string.Empty,
                    IsRequired = fieldDto.IsRequired,
                    Metadata = fieldDto.Metadata != null ? JsonSerializer.Serialize(fieldDto.Metadata) : string.Empty,
                    TenantId = _currentUserService.TenantId.Value
                };
                
                _context.ReportSectionFields.Add(field);
            }
            
            _logger.LogInformation("Added {FieldCount} fields for section {SectionId}", fieldDtos.Count, sectionId);
            return Task.CompletedTask;
        }
    }
}
