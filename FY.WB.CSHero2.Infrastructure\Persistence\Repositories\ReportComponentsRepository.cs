using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Common.Interfaces;
using System.Text;
using System.Text.Json;
using System.IO.Compression;
using MultiStorageReportComponent = FY.WB.CSHero2.Application.Models.MultiStorage.ReportComponent;
using MultiStorageComponentsMetadata = FY.WB.CSHero2.Application.Models.MultiStorage.ComponentsMetadata;
using MultiStorageComponentStorageResult = FY.WB.CSHero2.Application.Models.MultiStorage.ComponentStorageResult;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// Repository for managing report components in Azure Blob Storage
    /// Implements CRUD operations for component files and metadata
    /// </summary>
    public class ReportComponentsRepository : IReportComponentsRepository
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly string _containerName;
        private readonly ILogger<ReportComponentsRepository> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public ReportComponentsRepository(
            BlobServiceClient blobServiceClient,
            IConfiguration configuration,
            ILogger<ReportComponentsRepository> logger)
        {
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _containerName = configuration["BlobStorage:ContainerName"] ?? "report-components";
            
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        #region Component storage operations

        public async Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<MultiStorageReportComponent> components, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Saving components for report {ReportId}, version {VersionId}", reportId, versionId);

                var blobId = $"reports/{reportId}/{versionId}";
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

                // Ensure container exists
                await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);

                var componentsList = components.ToList();
                long totalSize = 0;

                // Create metadata
                var metadata = new MultiStorageComponentsMetadata
                {
                    ReportId = reportId.ToString(),
                    VersionId = versionId.ToString(),
                    GeneratedAt = DateTime.UtcNow,
                    GeneratedBy = "system", // This should be replaced with actual user ID
                    Framework = "NextJS",
                    StyleFramework = "TailwindCSS",
                    Components = componentsList.Select(c => new FY.WB.CSHero2.Application.Models.MultiStorage.ComponentMetadata
                    {
                        Id = c.Id,
                        Name = c.Name,
                        SectionId = c.SectionId,
                        FileName = $"{c.Name}.tsx",
                        Size = Encoding.UTF8.GetByteCount(c.Code),
                        Hash = ComputeHash(c.Code),
                        Imports = c.Imports,
                        Props = c.Props,
                        GeneratedAt = DateTime.UtcNow
                    }).ToList()
                };

                // Calculate total size
                totalSize = metadata.Components.Sum(c => c.Size);
                metadata.TotalSize = totalSize;

                // Save metadata
                var metadataJson = JsonSerializer.Serialize(metadata, _jsonOptions);
                var metadataBlobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");
                
                using var metadataStream = new MemoryStream(Encoding.UTF8.GetBytes(metadataJson));
                await metadataBlobClient.UploadAsync(metadataStream, overwrite: true, cancellationToken: cancellationToken);

                // Save components
                foreach (var component in componentsList)
                {
                    var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                    using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                    
                    var blobHttpHeaders = new BlobHttpHeaders
                    {
                        ContentType = "text/typescript"
                    };
                    
                    await componentBlobClient.UploadAsync(
                        componentStream, 
                        new BlobUploadOptions 
                        { 
                            HttpHeaders = blobHttpHeaders,
                            Metadata = new Dictionary<string, string>
                            {
                                ["sectionId"] = component.SectionId,
                                ["componentId"] = component.Id,
                                ["generatedAt"] = DateTime.UtcNow.ToString("O")
                            }
                        }, 
                        cancellationToken: cancellationToken);
                }

                _logger.LogInformation("Successfully saved {ComponentCount} components for report {ReportId}, version {VersionId}. Total size: {TotalSize} bytes", 
                    componentsList.Count, reportId, versionId, totalSize);

                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving components for report {ReportId}, version {VersionId}", reportId, versionId);
                throw;
            }
        }

        public async Task<MultiStorageComponentsMetadata?> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving components metadata for blob {BlobId}", blobId);

                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var metadataBlobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");

                if (!await metadataBlobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Components metadata not found for blob {BlobId}", blobId);
                    return null;
                }

                var response = await metadataBlobClient.DownloadContentAsync(cancellationToken);
                var metadataJson = response.Value.Content.ToString();
                
                var metadata = JsonSerializer.Deserialize<MultiStorageComponentsMetadata>(metadataJson, _jsonOptions);
                
                _logger.LogDebug("Successfully retrieved components metadata for blob {BlobId}", blobId);
                return metadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving components metadata for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<IEnumerable<MultiStorageReportComponent>> GetComponentsAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving all components for blob {BlobId}", blobId);

                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                if (metadata == null)
                {
                    return Enumerable.Empty<MultiStorageReportComponent>();
                }

                var components = new List<MultiStorageReportComponent>();
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

                foreach (var componentMeta in metadata.Components)
                {
                    var component = await GetComponentAsync(blobId, componentMeta.Name, cancellationToken);
                    if (component != null)
                    {
                        components.Add(component);
                    }
                }

                _logger.LogDebug("Successfully retrieved {ComponentCount} components for blob {BlobId}", components.Count, blobId);
                return components;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving components for blob {BlobId}", blobId);
                throw;
            }
        }

        public async Task<MultiStorageReportComponent?> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving component {ComponentName} for blob {BlobId}", componentName, blobId);

                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{componentName}.tsx");

                if (!await componentBlobClient.ExistsAsync(cancellationToken))
                {
                    _logger.LogWarning("Component {ComponentName} not found for blob {BlobId}", componentName, blobId);
                    return null;
                }

                var response = await componentBlobClient.DownloadContentAsync(cancellationToken);
                var code = response.Value.Content.ToString();
                
                // Get metadata from blob properties
                var properties = await componentBlobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
                var metadata = properties.Value.Metadata;

                var component = new MultiStorageReportComponent
                {
                    Id = metadata.TryGetValue("componentId", out var componentId) ? componentId : Guid.NewGuid().ToString(),
                    Name = componentName,
                    SectionId = metadata.TryGetValue("sectionId", out var sectionId) ? sectionId : string.Empty,
                    Code = code,
                    Imports = ExtractImports(code),
                    Props = ExtractProps(code)
                };

                _logger.LogDebug("Successfully retrieved component {ComponentName} for blob {BlobId}", componentName, blobId);
                return component;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component {ComponentName} for blob {BlobId}", componentName, blobId);
                throw;
            }
        }

        public async Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting all components for blob {BlobId}", blobId);

                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                
                // List all blobs with the prefix
                var blobs = containerClient.GetBlobsAsync(prefix: $"{blobId}/", cancellationToken: cancellationToken);
                
                var deleteTasks = new List<Task>();
                await foreach (var blob in blobs)
                {
                    var blobClient = containerClient.GetBlobClient(blob.Name);
                    deleteTasks.Add(blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken));
                }

                await Task.WhenAll(deleteTasks);

                _logger.LogInformation("Successfully deleted all components for blob {BlobId}", blobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting components for blob {BlobId}", blobId);
                throw;
            }
        }

        #endregion

        #region Individual component operations

        public async Task<string> SaveComponentAsync(Guid reportId, Guid versionId, MultiStorageReportComponent component, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobId = $"reports/{reportId}/{versionId}";
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);

                // Ensure container exists
                await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);

                var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                
                var blobHttpHeaders = new BlobHttpHeaders
                {
                    ContentType = "text/typescript"
                };
                
                await componentBlobClient.UploadAsync(
                    componentStream, 
                    new BlobUploadOptions 
                    { 
                        HttpHeaders = blobHttpHeaders,
                        Metadata = new Dictionary<string, string>
                        {
                            ["sectionId"] = component.SectionId,
                            ["componentId"] = component.Id,
                            ["generatedAt"] = DateTime.UtcNow.ToString("O")
                        }
                    }, 
                    cancellationToken: cancellationToken);

                _logger.LogDebug("Successfully saved component {ComponentName} for report {ReportId}, version {VersionId}", 
                    component.Name, reportId, versionId);

                return $"{blobId}/components/{component.Name}.tsx";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving component {ComponentName} for report {ReportId}, version {VersionId}", 
                    component.Name, reportId, versionId);
                throw;
            }
        }

        public async Task<string> GetComponentCodeAsync(string blobId, string componentName, CancellationToken cancellationToken = default)
        {
            try
            {
                var component = await GetComponentAsync(blobId, componentName, cancellationToken);
                return component?.Code ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving component code for {ComponentName} in blob {BlobId}", componentName, blobId);
                throw;
            }
        }

        public async Task UpdateComponentAsync(string blobId, MultiStorageReportComponent component, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                
                using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                
                var blobHttpHeaders = new BlobHttpHeaders
                {
                    ContentType = "text/typescript"
                };
                
                await componentBlobClient.UploadAsync(
                    componentStream, 
                    new BlobUploadOptions 
                    { 
                        HttpHeaders = blobHttpHeaders,
                        Metadata = new Dictionary<string, string>
                        {
                            ["sectionId"] = component.SectionId,
                            ["componentId"] = component.Id,
                            ["lastModified"] = DateTime.UtcNow.ToString("O")
                        }
                    }, 
                    cancellationToken: cancellationToken);

                _logger.LogDebug("Successfully updated component {ComponentName} in blob {BlobId}", component.Name, blobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating component {ComponentName} in blob {BlobId}", component.Name, blobId);
                throw;
            }
        }

        #endregion

        #region Utility operations

        public async Task<bool> ComponentsExistAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                return metadata != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if components exist for blob {BlobId}", blobId);
                return false;
            }
        }

        public async Task<long> GetComponentsSizeAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                return metadata?.TotalSize ?? 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating components size for blob {BlobId}", blobId);
                return 0;
            }
        }

        public async Task<MultiStorageComponentStorageResult> GetStorageResultAsync(string blobId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await GetComponentsMetadataAsync(blobId, cancellationToken);
                
                return new MultiStorageComponentStorageResult
                {
                    BlobId = blobId,
                    ComponentCount = metadata?.Components.Count ?? 0,
                    TotalSize = metadata?.TotalSize ?? 0,
                    Metadata = metadata ?? new MultiStorageComponentsMetadata(),
                    Success = metadata != null,
                    ErrorMessage = metadata == null ? "Components metadata not found" : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting storage result for blob {BlobId}", blobId);
                return new MultiStorageComponentStorageResult
                {
                    BlobId = blobId,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region Bulk operations

        public async Task<IEnumerable<string>> ListComponentBlobsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
                var prefix = $"reports/{reportId}/";
                
                var blobIds = new List<string>();
                var blobs = containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken);
                
                await foreach (var blob in blobs)
                {
                    if (blob.Name.EndsWith("/metadata.json"))
                    {
                        var blobId = blob.Name.Replace("/metadata.json", "");
                        blobIds.Add(blobId);
                    }
                }

                return blobIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing component blobs for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task DeleteAllComponentsForReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                var blobIds = await ListComponentBlobsAsync(reportId, cancellationToken);
                
                var deleteTasks = blobIds.Select(blobId => DeleteComponentsAsync(blobId, cancellationToken));
                await Task.WhenAll(deleteTasks);

                _logger.LogInformation("Successfully deleted all components for report {ReportId}", reportId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting all components for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Private helper methods

        private static string ComputeHash(string content)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(content));
            return Convert.ToBase64String(hash);
        }

        private static List<string> ExtractImports(string code)
        {
            var imports = new List<string>();
            var lines = code.Split('\n');
            
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("import ") && trimmedLine.Contains(" from "))
                {
                    imports.Add(trimmedLine);
                }
            }
            
            return imports;
        }

        private static List<string> ExtractProps(string code)
        {
            var props = new List<string>();
            
            // Simple regex to extract interface/type definitions
            // This is a basic implementation - could be enhanced with proper TypeScript parsing
            var lines = code.Split('\n');
            bool inInterface = false;
            
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                if (trimmedLine.StartsWith("interface ") || trimmedLine.StartsWith("type "))
                {
                    inInterface = true;
                    props.Add(trimmedLine);
                }
                else if (inInterface && trimmedLine == "}")
                {
                    inInterface = false;
                    props.Add(trimmedLine);
                }
                else if (inInterface)
                {
                    props.Add(trimmedLine);
                }
            }
            
            return props;
        }

        #endregion
    }
}