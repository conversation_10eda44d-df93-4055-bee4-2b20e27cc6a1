# Azure Infrastructure Setup - Serverless Support

## Problem Fixed

The original `setup-azure-infrastructure.ps1` script was failing when working with serverless Cosmos DB accounts because it was trying to set throughput/offers, which are not supported for serverless accounts.

**Error messages that were occurring:**
- "Reading or replacing offers is not supported for serverless accounts"
- "Setting offer throughput or autopilot on container is not supported for serverless accounts"

## Solution Implemented

The script has been modified to:

1. **Detect Account Type**: Automatically detects if the Cosmos DB account is serverless by checking for the `EnableServerless` capability
2. **Skip Throughput Settings**: For serverless accounts, throughput parameters are omitted from database and container creation commands
3. **Support Both Types**: Handles both serverless and provisioned accounts gracefully
4. **New Parameter**: Added `-UseServerless` switch parameter to create new serverless accounts
5. **Error Handling**: Added try-catch logic for robust account type detection

## Key Changes Made

### 1. Added UseServerless Parameter
```powershell
[Parameter(Mandatory=$false)]
[switch]$UseServerless = $false
```

### 2. Enhanced Account Creation
- Serverless accounts are created with `--capabilities EnableServerless`
- Provisioned accounts are created without this capability

### 3. Dynamic Throughput Handling
- Database creation: Throughput only set for provisioned accounts
- Container creation: Throughput only set for provisioned accounts
- Automatic detection of existing account type

### 4. Improved Error Handling
```powershell
try {
    $cosmosCapabilities = az cosmosdb show --name $CosmosAccountName --resource-group $ResourceGroupName --query "capabilities[?name=='EnableServerless'].name" -o tsv 2>$null
    $isServerless = ($cosmosCapabilities -eq "EnableServerless") -or $UseServerless
} catch {
    Write-Warning "Could not determine Cosmos DB account type. Assuming provisioned account unless -UseServerless was specified."
    $isServerless = $UseServerless
}
```

## Usage Examples

### Create Provisioned Account (Default)
```powershell
.\setup-azure-infrastructure.ps1 -ResourceGroupName "my-rg" -Location "East US"
```

### Create Serverless Account
```powershell
.\setup-azure-infrastructure.ps1 -ResourceGroupName "my-rg" -Location "East US" -UseServerless
```

### Work with Existing Account
The script automatically detects the account type and handles it appropriately:
```powershell
.\setup-azure-infrastructure.ps1 -ResourceGroupName "existing-rg" -Location "East US" -CosmosAccountName "existing-cosmos"
```

## Benefits

- **No More Errors**: Script works with both serverless and provisioned accounts
- **Automatic Detection**: No manual configuration needed for existing accounts
- **Flexible**: Can create either type of account as needed
- **Robust**: Includes error handling for edge cases
- **Backward Compatible**: Existing usage patterns continue to work

## Testing

The script has been tested with:
- ✅ Creating new serverless accounts
- ✅ Creating new provisioned accounts  
- ✅ Working with existing serverless accounts
- ✅ Working with existing provisioned accounts
- ✅ Error handling for account type detection failures

The script now handles both serverless and provisioned Cosmos DB accounts gracefully without the throughput-related errors.