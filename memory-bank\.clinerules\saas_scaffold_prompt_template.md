# .clinerules - ASP.NET Core Multi-Tenant SaaS Standard
_Version: 1.1_
_Last Updated: 2025-05-11_

This file defines the architectural and coding standards for the project. It should be used by scaffolding tools (like Cline) and for code reviews.

---

## I. General Principles & Naming Conventions

* **ProjectNaming**: `PascalCase` (e.g., `YourProject.Api`, `YourProject.Application`)
* **FileNameCasing**: `PascalCase` (e.g., `MyService.cs`, `MyController.cs`)
* **NamespaceFormat**: `'{ProjectName}.{FolderStructure}'` (e.g., `YourProject.Application.Features.Invoices.Commands`)
* **UseModernCSharpFeatures**: `true` (C# 11+; e.g., records, init-only setters, file-scoped namespaces, raw string literals)
* **SOLIDPrinciples**: `Enforce` (Adherence to Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion)
* **XmlDocumentation**: `RequiredForAllPublicMembers` (Classes, methods, properties)

---

## II. Solution & Project Structure

*(Matches LLM Prompt Rule 1)*

* **ApiProject**:
    * NameSuffix: `.Api`
    * AllowedFolders: `['Controllers', 'Extensions', 'Middleware', 'Program.cs', 'OpenApi']`
    * ControllerNaming: `'{Feature}Controller.cs'`
    * ControllerLocation: `Controllers/` (Relative to Api project root)
* **ApplicationProject**:
    * NameSuffix: `.Application`
    * AllowedFolders: `['{Feature}', 'Behaviours', 'Common', 'Interfaces', 'Validators', 'DTOs']`
    * FeatureFolderStructure: `'{Feature}/(Commands|Queries|Handlers|Validators|DTOs)'`
    * CommandNaming: `'{Action}{Feature}Command.cs'` (e.g., `CreateInvoiceCommand.cs`)
    * QueryNaming: `'{Action}{Feature}Query.cs'` (e.g., `GetInvoiceByIdQuery.cs`, `ListInvoicesQuery.cs`)
    * HandlerNaming: `'{Action}{Feature}{CommandOrQuery}Handler.cs'` (e.g., `CreateInvoiceCommandHandler.cs`)
    * HandlerLocation: `SameFolderAsCommandOrQuery` (e.g., `Application/{Feature}/Commands/CreateInvoiceCommandHandler.cs`)
    * ValidatorNaming: `'{CommandName}Validator.cs'` (e.g., `CreateInvoiceCommandValidator.cs`)
    * ValidatorLocation: `SameFolderAsCommand` (e.g., `Application/{Feature}/Commands/CreateInvoiceCommandValidator.cs`)
    * DTONaming: `'{Feature}{Action}Dto.cs'` or `'{Feature}DetailsDto.cs'` (e.g., `InvoiceDetailsDto.cs`)
* **DomainProject**:
    * NameSuffix: `.Domain`
    * AllowedFolders: `['Entities', 'ValueObjects', 'Enums', 'Events', 'Exceptions', 'Interfaces', 'Constants']`
    * EntityNaming: `'{EntityName}.cs'` (e.g., `Invoice.cs`)
    * ValueObjectNaming: `'{ValueObjectName}.cs'`
* **InfrastructureProject**:
    * NameSuffix: `.Infrastructure`
    * AllowedFolders: `['Data', 'KeyVault', 'Services', 'Persistence', 'Migrations', 'Identity', 'Files', 'BackgroundJobs']`
    * DbContextName: `ApplicationDbContext.cs`
    * DbContextLocation: `Persistence/` (Changed from `Data/` for clearer separation)
    * KeyVaultBuilderName: `KeyVaultConnectionBuilder.cs`
    * KeyVaultBuilderLocation: `KeyVault/`
    * RepositoryNaming: `'{EntityName}Repository.cs'`
    * RepositoryImplementationNaming: `Ef{EntityName}Repository.cs` (Located in `Infrastructure/Persistence/Repositories/`)
* **SharedKernelProject**:
    * NameSuffix: `.SharedKernel`
    * Content: Truly shared abstractions, constants, core extension methods, utility classes not specific to any other layer. Avoid making it a dumping ground.

---

## III. CQRS Pattern

*(Matches LLM Prompt Rule 2)*

* **MediatorLibrary**: `MediatR`
* **CommandInterface**: `MediatR.IRequest<MediatR.Unit>` (Or custom result type like `Result<T>`)
* **QueryInterface**: `MediatR.IRequest<TResponse>`
* **HandlerInterfaceCommand**: `MediatR.IRequestHandler<{Command}, MediatR.Unit>` (Or custom `Result<T>`)
* **HandlerInterfaceQuery**: `MediatR.IRequestHandler<{Query}, {TResponse}>`
* **SeparateReadWriteModels**: `true` (DTOs for queries must differ from command models/entities if optimal for read)
* **NoSharedDbContextInQueries**: `true` (Queries should use a read-only DbContext or Dapper for performance if applicable. Default to read-only DbContext.)
* **MediatRPipelineBehaviours**:
    * Logging: `Required`
    * Validation: `RequiredForCommands`
    * TenantContextInjection: `Required`
    * PerformanceMonitoring: `Optional` (Can be added later if needed)
    * Caching: `Optional` (Implement specific caching strategies as needed, not a default behavior for all handlers)
    * UnitOfWork: `RequiredForCommands` (Wrap command handlers in a Unit of Work for transactional consistency)

---

## IV. Multi-Tenancy with Finbuckle

*(Matches LLM Prompt Rule 3)*

* **Library**: `Finbuckle.MultiTenant`
* **Strategy**: `SingleDatabaseWithDiscriminator`
* **DbSetMapping**: `modelBuilder.Entity<TEntity>().IsMultiTenant();` (Must be applied to all tenant-specific entities within `IEntityTypeConfiguration<T>` classes)
* **ProgramCsConfiguration**: `builder.Services.AddMultiTenant<AppTenantInfo>().WithHostStrategy()...WithEntityFrameworkCore<ApplicationDbContext>()` (Ensure essential setup including EF Core integration)
* **TenantInfoClass**: `Required` (Define `AppTenantInfo : ITenantInfo` in `Infrastructure` or a dedicated `MultiTenancy` project/folder)
* **TenantResolution**:
    * DefaultStrategy: `HostStrategy` (e.g., `tenant1.saas.com`)
* **TenantContextAccess**: Use `IMultiTenantContextAccessor<AppTenantInfo>`

---

## V. Secrets Management

*(Matches LLM Prompt Rule 4)*

* **Provider**: `AzureKeyVault`
* **AppSettingsKeyForUri**: `KeyVault:Uri` (Only this in `appsettings.json` for local development)
* **ConnectionRetrievalClass**: `KeyVaultConnectionBuilder.cs`
* **ConnectionCaching**: `RequiredAtStartup` (Cached by `KeyVaultConnectionBuilder`)
* **RetrievedSecrets**: `['DefaultConnection', 'ServiceApiKey', ...]` (List critical secrets managed by KeyVault)

---

## VI. Entity Framework Core

*(Matches LLM Prompt Rule 5)*

* **MappingStyle**: `FluentAPI` (Exclusively, located in `IEntityTypeConfiguration<T>` classes within `Infrastructure/Persistence/Configurations/`)
* **DataAnnotationUsage**: `[Key]` (Only `[Key]` allowed on entities for primary key. All other config via Fluent API.)
* **Migrations**:
    * Folder: `Infrastructure/Persistence/Migrations/`
    * Generation: Via `dotnet ef migrations add {MigrationName}`
    * Application: Via `_context.Database.MigrateAsync()` in `Program.cs` or a dedicated startup service.
* **DbContext**:
    * Registration: `builder.Services.AddDbContext<ApplicationDbContext>((serviceProvider, options) => { ... });` (In Program.cs or service extensions, allowing for tenant-specific connection string resolution if needed, though Finbuckle handles much of this).
    * Constructor: `public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IMultiTenantContextAccessor<AppTenantInfo> tenantAccessor)`
    * QuerySplittingBehavior: `QuerySplittingBehavior.SplitQuery` (Configure globally in `UseSqlServer` options for performance with multiple includes, unless specific queries benefit from single query.)
* **ConcurrencyControl**: `UseRowVersionOrTimestamp` (e.g., `modelBuilder.Entity<MyEntity>().Property<byte[]>("Version").IsRowVersion();`) For entities requiring optimistic concurrency.
* **LazyLoading**: `Prohibited` (Disable lazy loading proxies: `options.UseLazyLoadingProxies(false);`). Prefer explicit loading (`.Include()`, `.ThenInclude()`) or projections.
* **AsyncMethodsOnly**: `ForAllDatabaseOperations` (e.g., `SaveChangesAsync()`, `ToListAsync()`, `FirstOrDefaultAsync()`)
* **QueryPerformance**:
    * Use `AsNoTracking()` or `AsNoTrackingWithIdentityResolution()`: `Required` For all read-only queries.
    * Avoid `SELECT *` for complex projections: `Required`. Use `.Select()` to shape data into DTOs.
* **RepositoryPattern**:
    * InterfaceLocation: `DomainProject/Interfaces/` (e.g., `Domain/Interfaces/IInvoiceRepository.cs`)
    * ImplementationLocation: `InfrastructureProject/Persistence/Repositories/` (e.g., `Infrastructure/Persistence/Repositories/EfInvoiceRepository.cs`)
    * AsyncMethodsOnlyInRepository: `true`

---

## VII. Logging

* **Framework**: `Microsoft.Extensions.Logging` with `Serilog` as the provider.
* **ProviderConfiguration**: In `Program.cs` via `builder.Host.UseSerilog((context, services, configuration) => ...)`
* **StructuredLogging**: `Required` (Log events with properties, not just string messages)
* **LogOutput**:
    * Development: `Console` (Pretty format), `Debug`
    * Production: `AzureApplicationInsights` (or chosen cloud provider's logging service), structured file logs as a fallback.
* **DefaultLogLevel**: `Information`
* **SensitiveDataLogging**: `ProhibitOrMask` (Use Serilog destructuring policies for sensitive data)
* **CorrelationIds**: `RequiredInLogs` (Inject via middleware and include in all log messages using Serilog's `LogContext`)
* **RequestLoggingMiddleware**: `Required` (Use Serilog's `RequestLoggingMiddleware` to log request path, method, status code, duration, user agent, IP)

---

## VIII. Validation

*(Matches LLM Prompt Rule 6 - Cross-Cutting)*

* **Library**: `FluentValidation.AspNetCore` (for automatic ASP.NET Core integration)
* **ValidatorClassLocation**: Alongside `Command` or `Query` (e.g., `Application/{Feature}/Commands/CreateInvoiceCommandValidator.cs`)
* **ValidatorNaming**: `'{CommandOrQueryName}Validator.cs'`
* **AutomaticValidation**: Via MediatR Pipeline Behaviour (`ValidationBehaviour<TRequest, TResponse>`)
* **ValidationRules**:
    * `NotEmpty()`/`NotNull()`: For required fields.
    * `Length()`/`MaximumLength()`/`MinimumLength()`: For strings.
    * `EmailAddress()`: For email formats.
    * `IsInEnum()`: For enums.
    * `Custom()`: For complex business logic.
    * CascadeMode: `Stop` on first failure for most validators to avoid overwhelming users with messages.

---

## IX. API Design & Controllers

* **UseApiControllerAttribute**: `Required` for all API controllers.
* **ReturnTypes**: Standard `ActionResult<T>` or `IActionResult` (e.g., `Ok(data)`, `NotFound()`, `BadRequest(problemDetails)`, `CreatedAtActionResult()`).
* **ErrorHandling**: Use `ProblemDetails` for consistent error responses (RFC 7807). Configure via `AddProblemDetails()` in `Program.cs`.
* **Versioning**:
    * Method: `URL Path` (e.g., `/api/v1/invoices`)
    * Configuration: Via `Asp.Versioning.Mvc` and `Asp.Versioning.Mvc.ApiExplorer` NuGet packages.
* **SwaggerOpenAPI**:
    * Enabled: `true`
    * Configuration: `Swashbuckle.AspNetCore`
    * XmlCommentsForDocs: Enable in `.csproj` file and configure Swashbuckle to use them.
    * Documentation: All endpoints and DTOs must be documented with summaries and examples.
* **InputOutputModels**: Use `DTOs` (Data Transfer Objects) for request/response. Domain entities must not be directly exposed by APIs. DTOs should be `record` types where appropriate.

---

## X. Asynchronous Programming

*(Matches LLM Prompt Rule 6 - Cross-Cutting)*

* **AsyncSuffix**: `Required` for all truly asynchronous methods (e.g., `GetInvoiceAsync`).
* **ConfigureAwaitFalse**: Use `ConfigureAwait(false)` in library/shared kernel code. Not strictly necessary in ASP.NET Core controller/handler application code due to SynchronizationContext behavior, but harmless.
* **AvoidAsyncVoid**: `Prohibit` (Except for event handlers directly subscribed by UI frameworks, which is rare in backend APIs).
* **PassCancellationToken**: `Required` for all `async` methods that can support cancellation, especially those involving I/O or long-running computations. Propagate `CancellationToken` through the call stack.

---

## XI. Testing

*(Matches LLM Prompt Rule 7)*

* **Framework**: `NUnit`
* **TestProjectSuffix**:
    * Unit Tests: `.UnitTests` (e.g., `YourProject.Application.UnitTests`)
    * Integration Tests: `.IntegrationTests` (e.g., `YourProject.Api.IntegrationTests`)
* **TestClassNaming**: `'{ClassUnderTest}Tests.cs'` (e.g., `CreateInvoiceCommandHandlerTests.cs`)
* **TestMethodNaming**: `'{MethodUnderTest}_{Scenario}_{ExpectedBehavior}'` (e.g., `Handle_ValidCommand_ShouldReturnSuccess`)
* **HandlerTests (Unit Tests)**:
    * Location: In `.UnitTests` project, mirroring `Application` project structure.
    * DatabaseMocking: `Moq` for repository interfaces. Do not use InMemory providers for unit testing handlers if business logic is complex; mock dependencies instead. 
    * MockingLibrary: `Moq`
* **ControllerTests (Unit Tests)**:
    * FocusOn: Route invocation, parameter binding, response status code, `MediatR.ISender.Send` call verification. Mock MediatR.
* **IntegrationTests**:
    * Use `WebApplicationFactory<TEntryPoint>` where `TEntryPoint` is `Program.cs` or your API's entry point class.
    * DatabaseStrategy: Use Testcontainers for a real database (e.g., SQL Server in Docker) or `SQLite in-memory mode with schema sharing` for EF Core integration tests.
* **GenerateTestsForEachHandler**: `Required` (Aim for high coverage of command and query handlers).
* **AssertionLibrary**: `FluentAssertions`

---

## XII. Dependency Injection

* **ServiceRegistrationLocation**: `Program.cs` or dedicated extension methods in each project (e.g., `AddApplicationServices(this IServiceCollection services, IConfiguration configuration)` in `Application` project, `AddInfrastructureServices(...)` in `Infrastructure`).
* **Lifetimes**:
    * `Scoped`: Default for `DbContexts`, `Repositories`, `UnitOfWork`, tenant-specific services, MediatR handlers (often default for MediatR).
    * `Transient`: Validators, lightweight services without state.
    * `Singleton`: Configuration objects (`IOptions<T>`), `IMemoryCache`, `KeyVaultConnectionBuilder`, `ITenantResolver` implementations, `IHttpClientFactory` typed clients.
* **FavorConstructorInjection**: `true`
* **AvoidServiceLocatorPattern**: `true` (Do not inject `IServiceProvider` directly into classes to resolve dependencies, except in specific factory patterns or infrastructure code).

---

## XIII. Seeding Data

* **Method**:
    * See `DataSeeder_Documentation/README.md` for detailed seeding strategy.
    * `DataSeeder.cs` in `Infrastructure/Persistence/Seeders/`.
    * `SeedAsync(IServiceProvider serviceProvider)` method.
    * `if (app.Environment.IsDevelopment())` in `Program.cs`.
* **DataSources**: JSON files in `Tests/TestData/`.
* **Deserialization**: `System.Text.Json`.


---

## XIV. Error Handling & Resilience

* **UseDeveloperExceptionPage**: `InDevelopmentOnly` (`app.UseDeveloperExceptionPage();`)
* **UseExceptionHandlerMiddleware**: `InProduction` (`app.UseExceptionHandler("/error");` or custom middleware returning `ProblemDetails`).
* **ProblemDetails**: Configure global `ProblemDetails` responses via `builder.Services.AddProblemDetails()`. Map exceptions to `ProblemDetails` for consistent API error reporting.
* **TransientErrorHandling**: Use `Polly` for resilience policies (Retry, Circuit Breaker) when calling external HTTP services or, in some cases, database operations. Configure `HttpClient`s with Polly policies via `IHttpClientFactory`.

---

## XV. Code Style & Formatting

* **Indentation**: `Spaces`, Size `4`.
* **BraceStyle**: `Opening braces on the same line` for types, methods, and control structures (K&R variant).
    ```csharp
    public class MyClass
    {
        public void MyMethod()
        {
            if (condition)
            {
                // code
            }
        }
    }
    ```
* **ImplicitUsings**: `Enabled` (`<ImplicitUsings>enable</ImplicitUsings>` in `.csproj`). Manage global usings in `GlobalUsings.cs`.
* **FileScopedNamespaces**: `Preferred` for all new C# files (`namespace MyNamespace;`).
* **ExpressionBodiedMembers**: `Use` for simple, single-line accessors and methods.
* **`var` Usage**: `Use` when the type is obvious from the right-hand side of an assignment. Use explicit types when it improves readability or when the assigned type is not immediately clear (e.g., LINQ query results before projection).
* **StaticAnalysis**: `Enable Microsoft.CodeAnalysis.NetAnalyzers` and `StyleCop.Analyzers` with a defined ruleset (`.editorconfig`). Treat warnings as errors in CI builds.
* **`.editorconfig`**: `Required`. Provide a comprehensive `.editorconfig` file at the solution root to enforce formatting and code style rules across IDEs.

---

## XVI. Configuration

* **OptionsPattern**: `Required`. Use `IOptions<T>`, `IOptionsSnapshot<T>`, and `IOptionsMonitor<T>` for strongly-typed configuration.
* **ConfigurationBinding**: In `Program.cs` or service registration extensions (e.g., `builder.Services.Configure<MyConfigOptions>(builder.Configuration.GetSection("MyConfig"));`).
* **ValidateOptionsOnStartup**: `Required` for critical configurations. Implement `IValidateOptions<T>` (e.g., `services.AddOptions<MyConfigOptions>().ValidateDataAnnotations().ValidateOnStart();`) or custom validation.

---

## XVII. Background Tasks

* **Framework**: `IHostedService` / `BackgroundService` for long-running tasks.
* **JobScheduling**: For scheduled or recurring tasks, use `Quartz.NET` or `Hangfire`.
    * Chosen Scheduler: `Hangfire` (due to its dashboard and simpler setup for many common scenarios).
* **HangfireConfiguration**: Configure Hangfire server and dashboard in `Program.cs`. Use a persistent storage option for Hangfire (e.g., SQL Server).
* **MultiTenancy**: Ensure background jobs are tenant-aware if they operate on tenant-specific data. Pass tenant identifiers to jobs and resolve tenant context within the job execution.

---

## XVIII. Security

* **HTTPS**: `Enforce HTTPS` in production (`app.UseHttpsRedirection(); app.UseHsts();`).
* **Authentication**: `JWT Bearer Authentication` for APIs. ASP.NET Core Identity for user management if storing users within the application.
* **Authorization**: `Policy-based authorization` is preferred.
* **CORS**: Configure a strict CORS policy if the API is accessed from different origins (`builder.Services.AddCors(...)` and `app.UseCors(...)`).
* **InputValidation**: Already covered by FluentValidation.
* **SecretsManagement**: Already covered by Key Vault.
* **DataProtection**: Use ASP.NET Core Data Protection for encrypting sensitive information at rest if not handled by other means (e.g., database encryption).
* **SecurityHeaders**: Add common security headers like `X-Content-Type-Options`, `X-Frame-Options`, `Content-Security-Policy` (CSP), `Referrer-Policy` using middleware or custom configuration.
