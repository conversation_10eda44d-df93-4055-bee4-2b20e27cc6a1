<!-- Guide Purpose: High-level design patterns and principles for the multi-storage architecture -->
<!-- Target Audience: Developers implementing or maintaining the multi-storage report system -->

# Storage Architecture Overview

## Introduction

The CSHero2 reporting system implements a sophisticated multi-storage architecture that leverages the strengths of three different storage technologies to optimize performance, cost, and scalability. This guide provides a comprehensive overview of the architectural patterns, design principles, and coordination strategies.

## Storage System Responsibilities

### SQL Server (Primary Metadata Store)
**Purpose**: Structured, relational data with strong consistency and ACID properties

**Responsibilities**:
- Report metadata (name, status, author, creation dates)
- Entity relationships and referential integrity
- User and tenant management
- Report sections and fields structure
- Cross-storage references (DocumentId, BlobId fields)
- Audit trails and versioning metadata

**Optimal For**:
- Frequently queried metadata
- Complex relational queries
- Transactional operations
- Data requiring strong consistency

### Azure Cosmos DB (Document Store)
**Purpose**: Semi-structured, document-oriented data with flexible schema and global distribution

**Responsibilities**:
- Report data as JSON documents
- Section and field content
- Flexible metadata and attributes
- Multi-tenant partitioning
- Version-specific data snapshots

**Optimal For**:
- Large, nested JSON structures
- Flexible schema requirements
- High-throughput read/write operations
- Global distribution needs

### Azure Blob Storage (Large File Store)
**Purpose**: Unstructured content with high throughput and cost-effective storage

**Responsibilities**:
- Rendered Next.js components
- HTML templates and CSS styles
- Component metadata and assets
- Large binary files
- Export artifacts (PDF, Word, PowerPoint)

**Optimal For**:
- Large files and binary content
- Infrequently accessed data
- Cost-sensitive storage scenarios
- Content delivery and caching

## Multi-Storage Coordination Patterns

### 1. Reference Linking Pattern

SQL Server entities maintain foreign keys and path references to external storage systems:

```csharp
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    // Core metadata in SQL
    public string Name { get; set; }
    public string Status { get; set; }
    public Guid ClientId { get; set; }
    
    // Cross-storage references
    public string DataDocumentId { get; set; }    // → Cosmos DB
    public string ComponentsBlobId { get; set; }  // → Blob Storage
}

public class ReportVersion : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    
    // Version-specific storage references
    public string DataDocumentId { get; set; }    // → Cosmos DB
    public string ComponentsBlobId { get; set; }  // → Blob Storage
    public string StylesBlobId { get; set; }      // → Blob Storage
}
```

### 2. Data Flow Orchestration

```mermaid
graph TD
    A[Client Request] --> B[SQL Server]
    B --> C{Data Type?}
    C -->|Metadata| D[Return SQL Data]
    C -->|Document Data| E[Fetch from Cosmos DB]
    C -->|Components/Assets| F[Fetch from Blob Storage]
    E --> G[Combine Results]
    F --> G
    D --> G
    G --> H[Return to Client]
```

### 3. Write Coordination Pattern

For create operations, follow this sequence to maintain consistency:

```csharp
public async Task<Guid> CreateReportAsync(CreateReportRequest request)
{
    using var transaction = await _context.Database.BeginTransactionAsync();
    try
    {
        // 1. Create SQL entities first (generates IDs)
        var report = new Report { /* ... */ };
        var version = new ReportVersion { /* ... */ };
        
        _context.Reports.Add(report);
        _context.ReportVersions.Add(version);
        await _context.SaveChangesAsync();
        
        // 2. Create Cosmos document with SQL-generated IDs
        var documentId = GenerateDocumentId(report.Id, version.Id);
        var cosmosDoc = new VersionedReportDataDocument
        {
            Id = documentId,
            ReportId = report.Id,
            VersionId = version.Id,
            // ... other properties
        };
        await _cosmosService.UpsertItemAsync(cosmosDoc, report.TenantId.ToString());
        
        // 3. Create blob storage entries
        var blobId = await _blobService.SaveComponentsAsync(report.Id, version.Id, components);
        
        // 4. Update SQL with external references
        version.DataDocumentId = documentId;
        version.ComponentsBlobId = blobId;
        await _context.SaveChangesAsync();
        
        await transaction.CommitAsync();
        return report.Id;
    }
    catch
    {
        await transaction.RollbackAsync();
        // Cleanup external storage if needed
        throw;
    }
}
```

## Consistency Strategies

### 1. Eventual Consistency Model

The system implements eventual consistency across storage systems:

- **SQL Server**: Immediate consistency for metadata
- **Cross-Storage**: Eventually consistent with compensation patterns
- **Error Recovery**: Automated cleanup and retry mechanisms

### 2. Compensation Patterns

When cross-storage operations fail, implement compensation:

```csharp
public async Task<bool> DeleteReportAsync(Guid reportId)
{
    var report = await _context.Reports.FindAsync(reportId);
    var cleanupTasks = new List<Task>();
    
    try
    {
        // 1. Mark as deleted in SQL (soft delete)
        report.IsDeleted = true;
        await _context.SaveChangesAsync();
        
        // 2. Schedule cleanup of external storage
        if (!string.IsNullOrEmpty(report.DataDocumentId))
        {
            cleanupTasks.Add(_cosmosService.DeleteDocumentAsync(report.DataDocumentId));
        }
        
        if (!string.IsNullOrEmpty(report.ComponentsBlobId))
        {
            cleanupTasks.Add(_blobService.DeleteBlobAsync(report.ComponentsBlobId));
        }
        
        // 3. Execute cleanup (can be async/background)
        await Task.WhenAll(cleanupTasks);
        
        return true;
    }
    catch (Exception ex)
    {
        // Log for manual cleanup or retry
        _logger.LogError(ex, "Failed to cleanup external storage for report {ReportId}", reportId);
        return false; // SQL delete succeeded, external cleanup failed
    }
}
```

### 3. Health Monitoring

Implement health checks for all storage systems:

```csharp
public class StorageHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var results = await Task.WhenAll(
            CheckSqlHealthAsync(),
            CheckCosmosHealthAsync(),
            CheckBlobHealthAsync()
        );
        
        var unhealthy = results.Where(r => !r.IsHealthy).ToList();
        
        if (!unhealthy.Any())
            return HealthCheckResult.Healthy("All storage systems operational");
            
        return HealthCheckResult.Degraded($"Issues with: {string.Join(", ", unhealthy.Select(r => r.System))}");
    }
}
```

## Performance Optimization Strategies

### 1. Caching Layers

Implement multi-level caching:

```csharp
public class ReportService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    
    public async Task<ReportDto> GetReportAsync(Guid reportId)
    {
        // L1: Memory cache
        var cacheKey = $"report:{reportId}";
        if (_memoryCache.TryGetValue(cacheKey, out ReportDto cached))
            return cached;
        
        // L2: Distributed cache
        var distributedCached = await _distributedCache.GetStringAsync(cacheKey);
        if (distributedCached != null)
        {
            var report = JsonSerializer.Deserialize<ReportDto>(distributedCached);
            _memoryCache.Set(cacheKey, report, TimeSpan.FromMinutes(5));
            return report;
        }
        
        // L3: Storage systems
        var result = await LoadFromStorageAsync(reportId);
        
        // Cache the result
        await _distributedCache.SetStringAsync(cacheKey, JsonSerializer.Serialize(result), 
            new DistributedCacheEntryOptions { SlidingExpiration = TimeSpan.FromMinutes(15) });
        _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(5));
        
        return result;
    }
}
```

### 2. Parallel Data Loading

Load data from multiple storage systems concurrently:

```csharp
public async Task<CompleteReportDto> GetCompleteReportAsync(Guid reportId)
{
    // Start all operations concurrently
    var metadataTask = _context.Reports
        .Include(r => r.Versions)
        .Include(r => r.Sections)
        .FirstOrDefaultAsync(r => r.Id == reportId);
        
    var documentTask = GetCosmosDocumentAsync(reportId);
    var componentsTask = GetBlobComponentsAsync(reportId);
    
    // Wait for all to complete
    await Task.WhenAll(metadataTask, documentTask, componentsTask);
    
    // Combine results
    return new CompleteReportDto
    {
        Metadata = await metadataTask,
        DocumentData = await documentTask,
        Components = await componentsTask
    };
}
```

### 3. Batch Operations

Optimize bulk operations:

```csharp
public async Task<BulkOperationResult> CreateMultipleReportsAsync(List<CreateReportRequest> requests)
{
    // 1. Batch SQL operations
    var reports = requests.Select(r => new Report { /* ... */ }).ToList();
    _context.Reports.AddRange(reports);
    await _context.SaveChangesAsync();
    
    // 2. Batch Cosmos operations
    var cosmosOperations = reports.Select(r => new
    {
        Document = CreateCosmosDocument(r),
        PartitionKey = r.TenantId.ToString()
    });
    
    await _cosmosService.BulkUpsertAsync(cosmosOperations);
    
    // 3. Batch blob operations
    var blobOperations = reports.Select(r => CreateBlobData(r));
    await _blobService.BulkUploadAsync(blobOperations);
    
    return new BulkOperationResult { SuccessCount = reports.Count };
}
```

## Multi-Tenant Isolation

### 1. Tenant Partitioning Strategy

Each storage system implements tenant isolation:

**SQL Server**:
```csharp
// Global query filter in ApplicationDbContext
modelBuilder.Entity<Report>().HasQueryFilter(r => r.TenantId == _currentTenant.Id);
```

**Cosmos DB**:
```csharp
// Partition key strategy
public class VersionedReportDataDocument
{
    [JsonPropertyName("tenantId")]
    public string TenantId { get; set; } // Partition key
    
    [JsonPropertyName("id")]
    public string Id { get; set; }
}
```

**Blob Storage**:
```
Container: reports
Path: /tenants/{tenantId}/reports/{reportId}/versions/{versionId}/
```

### 2. Cross-Tenant Data Prevention

Implement validation at service boundaries:

```csharp
public async Task<ReportDto> GetReportAsync(Guid reportId)
{
    var report = await _context.Reports.FindAsync(reportId);
    
    // Validate tenant access
    if (report.TenantId != _currentTenant.Id && !_currentUser.IsAdmin)
    {
        throw new ForbiddenAccessException($"Access denied to report {reportId}");
    }
    
    return _mapper.Map<ReportDto>(report);
}
```

## Cost Optimization

### 1. Storage Tier Selection

Choose appropriate storage tiers based on access patterns:

- **SQL Server**: Premium for frequently accessed metadata
- **Cosmos DB**: Provisioned throughput for predictable workloads, serverless for variable
- **Blob Storage**: Hot tier for active data, Cool/Archive for historical data

### 2. Data Lifecycle Management

Implement automated data lifecycle policies:

```csharp
public class DataLifecycleService
{
    public async Task ArchiveOldVersionsAsync()
    {
        var oldVersions = await _context.ReportVersions
            .Where(v => v.CreationTime < DateTime.UtcNow.AddMonths(-6))
            .Where(v => !v.IsCurrent)
            .ToListAsync();
            
        foreach (var version in oldVersions)
        {
            // Move blob data to archive tier
            await _blobService.SetAccessTierAsync(version.ComponentsBlobId, AccessTier.Archive);
            
            // Optionally compress Cosmos documents
            await _cosmosService.CompressDocumentAsync(version.DataDocumentId);
        }
    }
}
```

## Error Handling and Recovery

### 1. Circuit Breaker Pattern

Implement circuit breakers for external storage:

```csharp
public class CosmosService
{
    private readonly CircuitBreaker _circuitBreaker;
    
    public async Task<T> GetDocumentAsync<T>(string id)
    {
        return await _circuitBreaker.ExecuteAsync(async () =>
        {
            return await _cosmosClient.ReadItemAsync<T>(id, new PartitionKey(id));
        });
    }
}
```

### 2. Retry Policies

Configure exponential backoff for transient failures:

```csharp
services.AddHttpClient<BlobService>()
    .AddPolicyHandler(GetRetryPolicy());

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                Console.WriteLine($"Retry {retryCount} after {timespan} seconds");
            });
}
```

## Monitoring and Observability

### 1. Distributed Tracing

Implement correlation across storage systems:

```csharp
public async Task<ReportDto> GetReportAsync(Guid reportId)
{
    using var activity = _activitySource.StartActivity("GetReport");
    activity?.SetTag("report.id", reportId.ToString());
    
    try
    {
        var metadata = await GetMetadataAsync(reportId);
        activity?.SetTag("storage.sql.success", true);
        
        var document = await GetDocumentAsync(metadata.DataDocumentId);
        activity?.SetTag("storage.cosmos.success", true);
        
        var components = await GetComponentsAsync(metadata.ComponentsBlobId);
        activity?.SetTag("storage.blob.success", true);
        
        return CombineData(metadata, document, components);
    }
    catch (Exception ex)
    {
        activity?.SetTag("error", true);
        activity?.SetTag("error.message", ex.Message);
        throw;
    }
}
```

### 2. Performance Metrics

Track key performance indicators:

```csharp
public class StorageMetrics
{
    private readonly IMetrics _metrics;
    
    public async Task<T> TrackStorageOperation<T>(string operation, string storageType, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await operation();
            
            _metrics.Measure.Counter.Increment("storage.operations.success", 
                new MetricTags("operation", operation, "storage", storageType));
            _metrics.Measure.Timer.Time("storage.operations.duration", stopwatch.Elapsed,
                new MetricTags("operation", operation, "storage", storageType));
                
            return result;
        }
        catch (Exception ex)
        {
            _metrics.Measure.Counter.Increment("storage.operations.failure",
                new MetricTags("operation", operation, "storage", storageType, "error", ex.GetType().Name));
            throw;
        }
    }
}
```

## Best Practices Summary

1. **Design for Failure**: Assume external storage systems will fail and design compensation patterns
2. **Optimize for Access Patterns**: Place frequently accessed data in SQL, large documents in Cosmos, files in Blob
3. **Implement Proper Caching**: Use multi-level caching to reduce storage system load
4. **Monitor Everything**: Track performance, errors, and costs across all storage systems
5. **Plan for Scale**: Design partitioning and sharding strategies from the beginning
6. **Security First**: Implement proper authentication, authorization, and encryption across all systems
7. **Cost Awareness**: Regularly review and optimize storage costs based on usage patterns

This architecture provides a robust foundation for scalable, performant, and cost-effective report storage while maintaining data consistency and multi-tenant isolation.
