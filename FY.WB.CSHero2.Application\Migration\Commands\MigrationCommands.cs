using MediatR;
using FY.WB.CSHero2.Application.Models.Migration;

namespace FY.WB.CSHero2.Application.Migration.Commands
{
    /// <summary>
    /// Command to migrate all reports from SQL to multi-storage architecture
    /// </summary>
    public class MigrateAllReportsCommand : IRequest<MigrationResult>
    {
        /// <summary>
        /// Migration options and configuration
        /// </summary>
        public MigrationOptions Options { get; set; } = new MigrationOptions();

        /// <summary>
        /// User ID who initiated the migration
        /// </summary>
        public Guid? InitiatedBy { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking
        /// </summary>
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Command to migrate a specific report and all its versions
    /// </summary>
    public class MigrateReportCommand : IRequest<MigrationResult>
    {
        /// <summary>
        /// Report ID to migrate
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Migration options and configuration
        /// </summary>
        public MigrationOptions Options { get; set; } = new MigrationOptions();

        /// <summary>
        /// User ID who initiated the migration
        /// </summary>
        public Guid? InitiatedBy { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking
        /// </summary>
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Command to migrate a specific report version
    /// </summary>
    public class MigrateReportVersionCommand : IRequest<MigrationResult>
    {
        /// <summary>
        /// Report ID
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Version ID to migrate
        /// </summary>
        public Guid VersionId { get; set; }

        /// <summary>
        /// Migration options and configuration
        /// </summary>
        public MigrationOptions Options { get; set; } = new MigrationOptions();

        /// <summary>
        /// User ID who initiated the migration
        /// </summary>
        public Guid? InitiatedBy { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking
        /// </summary>
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Command to validate migration for a specific report
    /// </summary>
    public class ValidateMigrationCommand : IRequest<ValidationResult>
    {
        /// <summary>
        /// Report ID to validate
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Optional version ID to validate specific version
        /// </summary>
        public Guid? VersionId { get; set; }

        /// <summary>
        /// Validation options
        /// </summary>
        public ValidationOptions Options { get; set; } = new ValidationOptions();

        /// <summary>
        /// User ID who initiated the validation
        /// </summary>
        public Guid? InitiatedBy { get; set; }
    }

    /// <summary>
    /// Command to rollback migration for a specific report
    /// </summary>
    public class RollbackMigrationCommand : IRequest<RollbackResult>
    {
        /// <summary>
        /// Report ID to rollback
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Optional version ID to rollback specific version
        /// </summary>
        public Guid? VersionId { get; set; }

        /// <summary>
        /// Rollback options
        /// </summary>
        public RollbackOptions Options { get; set; } = new RollbackOptions();

        /// <summary>
        /// User ID who initiated the rollback
        /// </summary>
        public Guid? InitiatedBy { get; set; }

        /// <summary>
        /// Reason for rollback
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Command to perform a dry run migration
    /// </summary>
    public class PerformDryRunCommand : IRequest<DryRunResult>
    {
        /// <summary>
        /// Optional report ID for specific report dry run
        /// </summary>
        public Guid? ReportId { get; set; }

        /// <summary>
        /// Migration options
        /// </summary>
        public MigrationOptions Options { get; set; } = new MigrationOptions();

        /// <summary>
        /// User ID who initiated the dry run
        /// </summary>
        public Guid? InitiatedBy { get; set; }
    }

    /// <summary>
    /// Command to cancel an ongoing migration operation
    /// </summary>
    public class CancelMigrationCommand : IRequest<CancellationResult>
    {
        /// <summary>
        /// Migration operation ID to cancel
        /// </summary>
        public Guid OperationId { get; set; }

        /// <summary>
        /// User ID who initiated the cancellation
        /// </summary>
        public Guid? InitiatedBy { get; set; }

        /// <summary>
        /// Reason for cancellation
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// Whether to force cancellation even if operation is in critical phase
        /// </summary>
        public bool Force { get; set; } = false;
    }

    /// <summary>
    /// Command to cleanup failed migration artifacts
    /// </summary>
    public class CleanupFailedMigrationCommand : IRequest<CleanupResult>
    {
        /// <summary>
        /// Optional report ID for specific cleanup
        /// </summary>
        public Guid? ReportId { get; set; }

        /// <summary>
        /// Optional operation ID for specific operation cleanup
        /// </summary>
        public Guid? OperationId { get; set; }

        /// <summary>
        /// Cleanup options
        /// </summary>
        public CleanupOptions Options { get; set; } = new CleanupOptions();

        /// <summary>
        /// User ID who initiated the cleanup
        /// </summary>
        public Guid? InitiatedBy { get; set; }
    }

    /// <summary>
    /// Command to validate storage connectivity
    /// </summary>
    public class ValidateStorageConnectivityCommand : IRequest<StorageValidationResult>
    {
        /// <summary>
        /// Whether to perform deep validation (slower but more thorough)
        /// </summary>
        public bool DeepValidation { get; set; } = false;

        /// <summary>
        /// Timeout for validation operations in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
    }

    // Supporting classes for command options

    /// <summary>
    /// Validation options for migration validation
    /// </summary>
    public class ValidationOptions
    {
        /// <summary>
        /// Whether to validate data integrity
        /// </summary>
        public bool ValidateDataIntegrity { get; set; } = true;

        /// <summary>
        /// Whether to validate cross-storage references
        /// </summary>
        public bool ValidateCrossReferences { get; set; } = true;

        /// <summary>
        /// Whether to validate storage connectivity
        /// </summary>
        public bool ValidateStorageConnectivity { get; set; } = true;

        /// <summary>
        /// Whether to validate schema compliance
        /// </summary>
        public bool ValidateSchema { get; set; } = true;

        /// <summary>
        /// Whether to validate performance characteristics
        /// </summary>
        public bool ValidatePerformance { get; set; } = false;

        /// <summary>
        /// Timeout for validation operations in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 60;
    }

    /// <summary>
    /// Rollback options for migration rollback
    /// </summary>
    public class RollbackOptions
    {
        /// <summary>
        /// Whether to delete migrated data from Cosmos DB
        /// </summary>
        public bool DeleteCosmosData { get; set; } = true;

        /// <summary>
        /// Whether to delete migrated components from Blob Storage
        /// </summary>
        public bool DeleteBlobData { get; set; } = true;

        /// <summary>
        /// Whether to restore original SQL data
        /// </summary>
        public bool RestoreSqlData { get; set; } = true;

        /// <summary>
        /// Whether to validate rollback completion
        /// </summary>
        public bool ValidateRollback { get; set; } = true;

        /// <summary>
        /// Whether to create backup before rollback
        /// </summary>
        public bool CreateBackup { get; set; } = true;

        /// <summary>
        /// Timeout for rollback operations in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 300;
    }

    /// <summary>
    /// Cleanup options for failed migration cleanup
    /// </summary>
    public class CleanupOptions
    {
        /// <summary>
        /// Whether to clean up orphaned Cosmos DB documents
        /// </summary>
        public bool CleanupCosmosOrphans { get; set; } = true;

        /// <summary>
        /// Whether to clean up orphaned Blob Storage data
        /// </summary>
        public bool CleanupBlobOrphans { get; set; } = true;

        /// <summary>
        /// Whether to clean up incomplete SQL references
        /// </summary>
        public bool CleanupSqlReferences { get; set; } = true;

        /// <summary>
        /// Whether to clean up migration logs and temporary data
        /// </summary>
        public bool CleanupTemporaryData { get; set; } = true;

        /// <summary>
        /// Maximum age of data to clean up in days
        /// </summary>
        public int MaxAgeInDays { get; set; } = 7;

        /// <summary>
        /// Whether to perform dry run cleanup (report what would be cleaned)
        /// </summary>
        public bool DryRun { get; set; } = false;
    }
}