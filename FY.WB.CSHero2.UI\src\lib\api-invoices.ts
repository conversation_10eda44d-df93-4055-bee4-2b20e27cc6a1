import { Invoice, InvoiceCreateInput, InvoiceUpdateInput } from '@/types/invoice';
import { NEXT_API_BASE_URL } from '@/lib/constants';

/**
 * Helper function to resolve the base URL for API calls
 * Ensures server-side fetch calls use an absolute URL
 */
function getResolvedNextApiBaseUrl(): string {
  let baseUrl = NEXT_API_BASE_URL;

  // Check if running on server-side and if the URL is relative
  if (typeof window === 'undefined' && baseUrl.startsWith('/')) {
    // For server-side calls to its own API, prepend the application's own full URL
    const internalAppUrl = process.env.INTERNAL_APP_URL || 'http://localhost:3000';
    baseUrl = `${internalAppUrl}${baseUrl}`;
  }
  
  return baseUrl;
}

/**
 * Helper function to create a fetch request with timeout
 * @param url The URL to fetch
 * @param options Fetch options
 * @param timeoutMs Timeout in milliseconds (default: 10000 ms)
 * @returns Promise with the fetch response
 */
async function fetchWithTimeout(url: string, options?: RequestInit, timeoutMs: number = 10000): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    return response;
  } finally {
    clearTimeout(timeoutId);
  }
}

/**
 * Get all invoices with optional filtering, sorting, and pagination
 */
export async function getInvoices(params?: {
  search?: string;
  type?: 'Invoice' | 'Credit' | 'Refund' | 'Payment';
  status?: 'paid' | 'pending' | 'overdue' | 'canceled';
  tenantId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params) {
    if (params.search) queryParams.append('q', params.search);
    if (params.type) queryParams.append('type', params.type);
    if (params.status) queryParams.append('status', params.status);
    if (params.tenantId) queryParams.append('tenantId', params.tenantId);
    if (params.sortBy) queryParams.append('_sort', params.sortBy);
    if (params.sortOrder) queryParams.append('_order', params.sortOrder);
    if (params.page) queryParams.append('_page', params.page.toString());
    if (params.limit) queryParams.append('_limit', params.limit.toString());
  }

  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/invoices?${queryParams}`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch invoices');
    }
    const data = await response.json();
    return {
      data,
      headers: response.headers
    };
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}

/**
 * Get a single invoice by ID
 */
export async function getInvoiceById(id: string): Promise<Invoice> {
  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/invoices/${id}`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch invoice');
    }
    return response.json();
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}

/**
 * Get all invoices for a specific tenant
 */
export async function getInvoicesByTenantId(tenantId: string): Promise<Invoice[]> {
  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/tenants/${tenantId}/invoices`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch tenant invoices');
    }
    return response.json();
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}

/**
 * Create a new invoice
 */
export async function createInvoice(data: InvoiceCreateInput): Promise<Invoice> {
  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/invoices`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create invoice');
    }
    return response.json();
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}

/**
 * Update an existing invoice
 */
export async function updateInvoice(id: string, data: InvoiceUpdateInput): Promise<Invoice> {
  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/invoices/${id}`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update invoice');
    }
    return response.json();
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}

/**
 * Delete an invoice
 */
export async function deleteInvoice(id: string): Promise<boolean> {
  const resolvedUrl = `${getResolvedNextApiBaseUrl()}/v1/invoices/${id}`;
  
  try {
    const response = await fetchWithTimeout(resolvedUrl, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        return false; // Invoice not found
      }
      throw new Error('Failed to delete invoice');
    }
    
    return true; // Successfully deleted
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timed out after 10 seconds');
    }
    throw error;
  }
}
