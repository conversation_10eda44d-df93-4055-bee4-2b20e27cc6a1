using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface IBlobSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default);
    }

    public class BlobSeeder : IBlobSeeder
    {
        private readonly ILogger<BlobSeeder> _logger;

        // Load blob seed data from JSON file
        private BlobSeedData? _blobSeedData;

        // Report style mappings extracted from CosmosDbSeeder for proper multi-storage alignment
        private readonly List<ReportStyleMapping> _reportStyleMappings = new()
        {
            new ReportStyleMapping
            {
                CompanyName = "TechCorp Solutions",
                Category = "Satisfaction Survey",
                Theme = "satisfaction-analysis",
                PrimaryColor = "#10b981",
                SecondaryColor = "#059669",
                ReportId = "CSR-2025-001"
            },
            new ReportStyleMapping
            {
                CompanyName = "Health Plus",
                Category = "Performance Metrics",
                Theme = "performance-dashboard",
                PrimaryColor = "#3b82f6",
                SecondaryColor = "#1e40af",
                ReportId = "CSR-2025-002"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechFusion Inc.",
                Category = "Action Plan",
                Theme = "action-planning",
                PrimaryColor = "#f59e0b",
                SecondaryColor = "#d97706",
                ReportId = "CSR-2025-003"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechCorp Solutions",
                Category = "Channel Analysis",
                Theme = "channel-effectiveness",
                PrimaryColor = "#8b5cf6",
                SecondaryColor = "#7c3aed",
                ReportId = "CSR-2025-004"
            },
            new ReportStyleMapping
            {
                CompanyName = "Health Plus",
                Category = "User Experience",
                Theme = "ux-analysis",
                PrimaryColor = "#ef4444",
                SecondaryColor = "#dc2626",
                ReportId = "CSR-2025-005"
            },
            new ReportStyleMapping
            {
                CompanyName = "TechFusion Inc.",
                Category = "Product Improvement",
                Theme = "product-analysis",
                PrimaryColor = "#06b6d4",
                SecondaryColor = "#0891b2",
                ReportId = "CSR-2025-006"
            }
        };

        public BlobSeeder(ILogger<BlobSeeder> logger)
        {
            _logger = logger;
        }

        private async Task<BlobSeedData?> LoadBlobSeedDataAsync()
        {
            try
            {
                // Load from the new consolidated seed data file
                var seedDataPath = Path.Combine("SeedData", "BlobSeedData.json");
                
                // Try multiple possible paths relative to the current working directory
                var possiblePaths = new[]
                {
                    seedDataPath,
                    Path.Combine("FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath),
                    Path.Combine("..", "FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath),
                    Path.Combine(Directory.GetCurrentDirectory(), "FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath)
                };

                string? foundPath = null;
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        foundPath = path;
                        break;
                    }
                }

                if (foundPath == null)
                {
                    _logger.LogWarning("Blob seed data file not found. Tried paths: {Paths}", string.Join(", ", possiblePaths));
                    return null;
                }

                var jsonContent = await File.ReadAllTextAsync(foundPath);
                var blobData = JsonSerializer.Deserialize<BlobSeedData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Successfully loaded Blob seed data from {FilePath}", foundPath);
                return blobData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Blob seed data");
                return null;
            }
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting Blob Storage seeding...");

            try
            {
                // Load blob seed data from the consolidated file
                _blobSeedData = await LoadBlobSeedDataAsync();
                if (_blobSeedData != null)
                {
                    _logger.LogInformation("Successfully loaded blob seed data with {StyleMappingCount} style mappings", 
                        _blobSeedData.StyleMappings?.Count ?? 0);
                }
                else
                {
                    _logger.LogWarning("Could not load blob seed data, proceeding with hardcoded mappings");
                }

                // Validate that active tenants exist before proceeding
                var activeTenants = await context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(tp => tp.TenantId != null)
                    .Select(tp => tp.TenantId.ToString().ToUpperInvariant())
                    .ToListAsync(cancellationToken);

                if (!activeTenants.Any())
                {
                    _logger.LogError("No tenants found in TenantProfiles table - cannot proceed with Blob seeding");
                    return;
                }
                _logger.LogInformation("Found {TenantCount} tenants for Blob seeding: {TenantList}", 
                    activeTenants.Count, string.Join(", ", activeTenants));

                // Get all report versions for blob creation
                var reportVersions = await context.ReportVersions
                    .IgnoreQueryFilters()
                    .Include(rv => rv.Report)
                    .ToListAsync(cancellationToken);

                var createdCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var version in reportVersions)
                {
                    try
                    {
                        // Validate TenantId before creating blobs
                        if (!version.Report?.TenantId.HasValue == true || version.Report.TenantId.Value == Guid.Empty)
                        {
                            _logger.LogWarning("Skipping report version {VersionId} - invalid TenantId", version.Id);
                            errorCount++;
                            continue;
                        }

                        var tenantIdString = version.Report.TenantId.Value.ToString().ToUpperInvariant();
                        if (!activeTenants.Contains(tenantIdString))
                        {
                            _logger.LogWarning("Skipping report version {VersionId} - TenantId {TenantId} not found in active tenants. Available tenants: {ActiveTenants}", 
                                version.Id, tenantIdString, string.Join(", ", activeTenants));
                            errorCount++;
                            continue;
                        }

                        // Create components blob for this version
                        var componentsBlobId = await CreateComponentsBlobAsync(context, version, cancellationToken);
                        if (!string.IsNullOrEmpty(componentsBlobId))
                        {
                            version.ComponentsBlobId = componentsBlobId;
                            createdCount++;
                        }

                        // Create styles blob for this version (extracted from CosmosDbSeeder)
                        var stylesBlobId = await CreateEnhancedStylesBlobAsync(context, version, cancellationToken);
                        if (!string.IsNullOrEmpty(stylesBlobId))
                        {
                            version.StylesBlobId = stylesBlobId;
                            version.StylesSize = CalculateStylesSize(version);
                            createdCount++;
                        }

                        // Update storage strategy to indicate blob storage is now available
                        version.StorageStrategy = "MultiStorage";
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error seeding blobs for report version {VersionId}", version.Id);
                        errorCount++;
                    }
                }

                // Save all changes
                await context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Blob Storage seeding completed: {Created} blobs created, {Skipped} skipped, {Errors} errors",
                    createdCount, skippedCount, errorCount);
                    
                if (errorCount > 0)
                {
                    _logger.LogWarning("Blob Storage seeding completed with {ErrorCount} errors. Check logs for details.", errorCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Blob Storage seeding");
                throw;
            }
        }

        public async Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // For now, simulate blob ID retrieval by checking the database for existing blob references
                // In a real implementation, this would query the actual blob storage service
                _logger.LogDebug("Simulating blob ID retrieval from database references");
                
                var existingBlobIds = new List<string>();
                
                // This is a simulation - in real implementation, you would:
                // 1. Connect to Azure Blob Storage
                // 2. List blobs in the container
                // 3. Filter by naming pattern (tenants/{tenant-id}/reports/{report-id}/versions/v{version}/*)
                // 4. Return the blob IDs
                
                await Task.CompletedTask; // Placeholder for async blob storage operations
                
                _logger.LogDebug("Retrieved {Count} existing blob IDs", existingBlobIds.Count);
                return existingBlobIds;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting existing blob IDs, returning empty list");
                return new List<string>();
            }
        }

        private async Task<string> CreateComponentsBlobAsync(
            ApplicationDbContext context,
            ReportVersion version,
            CancellationToken cancellationToken)
        {
            try
            {
                // Create components blob structure according to handoff document
                // Storage hierarchy: /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/components.json

                var tenantId = version.Report?.TenantId?.ToString() ?? "default";
                var reportId = version.ReportId;
                var versionNumber = version.VersionNumber;

                var componentsBlob = new ComponentsBlob
                {
                    ReportId = reportId,
                    VersionId = version.Id,
                    VersionNumber = versionNumber,
                    TenantId = tenantId,
                    ComponentDataJson = version.ComponentDataJson,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system"
                };

                // Generate blob ID following the hierarchy pattern
                var blobId = $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/components.json";

                // TODO: When blob storage service is available, save the blob
                // For now, just simulate the blob creation
                _logger.LogDebug("Created components blob {BlobId} for version {VersionId}", blobId, version.Id);

                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating components blob for version {VersionId}", version.Id);
                return string.Empty;
            }
        }

        private async Task<string> CreateStylesBlobAsync(
            ApplicationDbContext context,
            ReportVersion version,
            CancellationToken cancellationToken)
        {
            try
            {
                // Create styles blob structure according to handoff document
                // Storage hierarchy: /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/styles.json

                var tenantId = version.Report?.TenantId?.ToString() ?? "default";
                var reportId = version.ReportId;
                var versionNumber = version.VersionNumber;

                var stylesBlob = new StylesBlob
                {
                    ReportId = reportId,
                    VersionId = version.Id,
                    VersionNumber = versionNumber,
                    TenantId = tenantId,
                    StyleDocumentId = version.StyleDocumentId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system"
                };

                // Generate blob ID following the hierarchy pattern
                var blobId = $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/styles.json";

                // TODO: When blob storage service is available, save the blob
                // For now, just simulate the blob creation
                _logger.LogDebug("Created styles blob {BlobId} for version {VersionId}", blobId, version.Id);

                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating styles blob for version {VersionId}", version.Id);
                return string.Empty;
            }
        }

        private async Task<string> CreateEnhancedStylesBlobAsync(
            ApplicationDbContext context,
            ReportVersion version,
            CancellationToken cancellationToken)
        {
            try
            {
                // Validate input parameters
                if (version?.Report == null)
                {
                    _logger.LogWarning("Cannot create enhanced styles blob - version or report is null");
                    return string.Empty;
                }

                // Create enhanced styles blob with full HTML, CSS, and component styles
                // Storage hierarchy: /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/styles.json

                var tenantId = version.Report?.TenantId?.ToString() ?? "default";
                var reportId = version.ReportId;
                var versionNumber = version.VersionNumber;

                // Validate required data
                if (reportId == Guid.Empty)
                {
                    _logger.LogWarning("Cannot create enhanced styles blob - invalid reportId for version {VersionId}", version.Id);
                    return string.Empty;
                }

                // Find matching style mapping based on report category
                var report = version.Report;
                var styleMapping = FindMatchingStyleMapping(report);

                // Generate and validate style content
                var htmlContent = GenerateReportHtml(styleMapping, report);
                var cssStyles = GenerateReportCss(styleMapping);
                var componentStyles = GenerateComponentStyles(styleMapping);

                // Validate generated content
                if (string.IsNullOrEmpty(htmlContent) || string.IsNullOrEmpty(cssStyles))
                {
                    _logger.LogWarning("Generated style content is invalid for version {VersionId}", version.Id);
                    return string.Empty;
                }

                var enhancedStylesBlob = new EnhancedStylesBlob
                {
                    ReportId = reportId,
                    VersionId = version.Id,
                    VersionNumber = versionNumber,
                    TenantId = tenantId,
                    StyleDocumentId = version.StyleDocumentId,
                    HtmlContent = htmlContent,
                    CssStyles = cssStyles,
                    ComponentStyles = componentStyles,
                    Metadata = new StyleMetadata
                    {
                        Framework = "TailwindCSS",
                        FrameworkVersion = "3.0",
                        Theme = styleMapping?.Theme ?? "default",
                        ColorScheme = "light",
                        Tags = new List<string> { report?.Category?.ToLower().Replace(" ", "-") ?? "default", "report" },
                        CustomProperties = new Dictionary<string, object>
                        {
                            ["primaryColor"] = styleMapping?.PrimaryColor ?? "#3b82f6",
                            ["secondaryColor"] = styleMapping?.SecondaryColor ?? "#1e40af",
                            ["tenantId"] = tenantId
                        }
                    },
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system"
                };

                // Validate blob data before saving
                var validationResult = ValidateStylesBlobData(enhancedStylesBlob);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Enhanced styles blob validation failed for version {VersionId}: {Errors}", 
                        version.Id, string.Join("; ", validationResult.Errors));
                    return string.Empty;
                }

                // Create ReportStyleDocument-compatible data structure for future BlobReportStyleService usage
                var styleDocumentData = new ReportStyleDocumentData
                {
                    Id = $"style-report-{version.Id}",
                    PartitionKey = tenantId,
                    ReportVersionId = version.Id,
                    TenantId = Guid.Parse(tenantId),
                    StyleType = "report",
                    HtmlContent = enhancedStylesBlob.HtmlContent,
                    CssStyles = enhancedStylesBlob.CssStyles,
                    InlineStyles = new Dictionary<string, object>(),
                    ComponentStyles = enhancedStylesBlob.ComponentStyles.ToDictionary(
                        kvp => kvp.Key,
                        kvp => new ComponentStyleData
                        {
                            ComponentId = kvp.Value.ComponentId,
                            ComponentType = kvp.Value.ComponentType,
                            CssClasses = kvp.Value.CssClasses,
                            InlineStyles = kvp.Value.InlineStyles,
                            CustomCss = kvp.Value.CustomCss,
                            Layout = new ComponentLayoutData
                            {
                                Width = kvp.Value.Layout.Width,
                                Height = kvp.Value.Layout.Height,
                                Margin = kvp.Value.Layout.Margin,
                                Padding = kvp.Value.Layout.Padding,
                                Display = kvp.Value.Layout.Display,
                                Position = kvp.Value.Layout.Position
                            }
                        }),
                    Metadata = new StyleMetadataData
                    {
                        Framework = enhancedStylesBlob.Metadata.Framework,
                        FrameworkVersion = enhancedStylesBlob.Metadata.FrameworkVersion,
                        Theme = enhancedStylesBlob.Metadata.Theme,
                        ColorScheme = enhancedStylesBlob.Metadata.ColorScheme,
                        Tags = enhancedStylesBlob.Metadata.Tags,
                        CustomProperties = enhancedStylesBlob.Metadata.CustomProperties
                    },
                    CreatedAt = DateTime.UtcNow,
                    LastModified = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    LastModifiedBy = Guid.Empty,
                    Version = 1,
                    IsActive = true
                };

                // Generate blob ID following the BlobReportStyleService pattern
                var blobId = $"tenants/{tenantId}/styles/{styleDocumentData.Id}.json";

                // Simulate blob storage operation with ReportStyleDocument-compatible data
                await SimulateStyleDocumentStorageAsync(styleDocumentData, blobId, cancellationToken);

                _logger.LogInformation("Created style document data {StyleDocumentId} for version {VersionId} (architecture-compliant format)",
                    styleDocumentData.Id, version.Id);

                return styleDocumentData.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating enhanced styles blob for version {VersionId}", version.Id);
                return string.Empty;
            }
        }

        private StylesBlobValidationResult ValidateStylesBlobData(EnhancedStylesBlob blob)
        {
            var result = new StylesBlobValidationResult();

            try
            {
                // Validate required fields
                if (blob.ReportId == Guid.Empty)
                    result.AddError("ReportId is empty");

                if (blob.VersionId == Guid.Empty)
                    result.AddError("VersionId is empty");

                if (string.IsNullOrEmpty(blob.TenantId))
                    result.AddError("TenantId is null or empty");

                if (string.IsNullOrEmpty(blob.HtmlContent))
                    result.AddError("HtmlContent is null or empty");

                if (string.IsNullOrEmpty(blob.CssStyles))
                    result.AddError("CssStyles is null or empty");

                // Validate HTML content structure
                if (!string.IsNullOrEmpty(blob.HtmlContent))
                {
                    if (!blob.HtmlContent.Contains("report-container"))
                        result.AddError("HtmlContent missing required report-container class");

                    if (!blob.HtmlContent.Contains("report-header"))
                        result.AddError("HtmlContent missing required report-header class");
                }

                // Validate CSS content structure
                if (!string.IsNullOrEmpty(blob.CssStyles))
                {
                    if (!blob.CssStyles.Contains(".report-container"))
                        result.AddError("CssStyles missing required .report-container selector");

                    if (!blob.CssStyles.Contains(".report-header"))
                        result.AddError("CssStyles missing required .report-header selector");
                }

                // Validate component styles
                if (blob.ComponentStyles != null)
                {
                    var requiredComponents = new[] { "header", "metrics", "chart", "insights", "footer" };
                    foreach (var component in requiredComponents)
                    {
                        if (!blob.ComponentStyles.ContainsKey(component))
                            result.AddError($"Missing required component style: {component}");
                    }
                }

                // Validate metadata
                if (blob.Metadata == null)
                {
                    result.AddError("Metadata is null");
                }
                else
                {
                    if (string.IsNullOrEmpty(blob.Metadata.Framework))
                        result.AddError("Metadata.Framework is null or empty");

                    if (string.IsNullOrEmpty(blob.Metadata.Theme))
                        result.AddError("Metadata.Theme is null or empty");
                }

                // Check content size (reasonable limits for blob storage)
                var totalSize = (blob.HtmlContent?.Length ?? 0) + (blob.CssStyles?.Length ?? 0);
                if (totalSize > 500000) // 500KB limit
                {
                    result.AddError($"Total content size ({totalSize} chars) exceeds reasonable limit");
                }

            }
            catch (Exception ex)
            {
                result.AddError($"Validation process failed: {ex.Message}");
            }

            return result;
        }

        private async Task SimulateBlobStorageOperationAsync(EnhancedStylesBlob blob, string blobId, CancellationToken cancellationToken)
        {
            try
            {
                // Simulate the time it would take to upload to blob storage
                await Task.Delay(50, cancellationToken);

                // In a real implementation, this would:
                // 1. Serialize the blob data to JSON
                // 2. Upload to Azure Blob Storage using BlobServiceClient
                // 3. Set appropriate metadata and content type
                // 4. Handle retry logic for transient failures
                // 5. Return the blob URL or reference

                var serializedBlob = JsonSerializer.Serialize(blob, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                _logger.LogDebug("Simulated blob storage operation for {BlobId}, size: {Size} bytes", 
                    blobId, serializedBlob.Length);

                // Simulate potential blob storage errors for testing
                if (blob.TenantId == "error-test")
                {
                    throw new InvalidOperationException("Simulated blob storage error");
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Blob storage operation cancelled for {BlobId}", blobId);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Simulated blob storage operation failed for {BlobId}", blobId);
                throw;
            }
        }

        private async Task SimulateStyleDocumentStorageAsync(ReportStyleDocumentData data, string blobId, CancellationToken cancellationToken)
        {
            // Simulate ReportStyleDocument storage in blob storage (architecture-compliant format)
            var jsonData = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            var dataSize = Encoding.UTF8.GetByteCount(jsonData);

            _logger.LogDebug("Simulating style document storage for {BlobId} ({DataSize} bytes) - BlobReportStyleService compatible format", blobId, dataSize);

            // Simulate network delay
            await Task.Delay(Random.Shared.Next(10, 50), cancellationToken);

            _logger.LogDebug("Style document storage simulation completed for {BlobId}", blobId);
        }

        // Helper class for blob validation
        public class StylesBlobValidationResult
        {
            public List<string> Errors { get; } = new();
            public bool IsValid => !Errors.Any();
            
            public void AddError(string error)
            {
                Errors.Add(error);
            }
        }

        private ReportStyleMapping? FindMatchingStyleMapping(Report? report)
        {
            if (report == null) return null;

            // Prioritize loaded seed data over hardcoded mappings
            var mappingsToSearch = _blobSeedData?.StyleMappings?.Any() == true 
                ? _blobSeedData.StyleMappings 
                : _reportStyleMappings;

            // Try to find exact category match first
            var exactMatch = mappingsToSearch.FirstOrDefault(m => 
                string.Equals(m.Category, report.Category, StringComparison.OrdinalIgnoreCase));
            
            if (exactMatch != null) return exactMatch;

            // Fallback to first mapping if no exact match
            return mappingsToSearch.FirstOrDefault();
        }

        private string GenerateReportHtml(ReportStyleMapping? mapping, Report? report)
        {
            var category = mapping?.Category ?? report?.Category ?? "Report";
            var clientName = report?.ClientName ?? "Client";
            var primaryColor = mapping?.PrimaryColor ?? "#3b82f6";

            return $@"
<div class='report-container bg-white shadow-xl rounded-lg overflow-hidden'>
    <div class='report-header text-white p-8' style='background: linear-gradient(135deg, {primaryColor} 0%, {primaryColor}dd 100%)'>
        <div class='flex items-center justify-between'>
            <div>
                <h1 class='text-4xl font-bold mb-2'>{category} Report</h1>
                <p class='text-lg opacity-90'>Client: {clientName}</p>
                <p class='text-sm opacity-75 mt-1'>Generated on {{{{date}}}}</p>
            </div>
            <div class='report-logo'>
                <div class='w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center'>
                    <span class='text-2xl font-bold'>{{{{logo}}}}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class='report-content p-8'>
        <div class='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8'>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 1</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric1}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric1Description}}}}</p>
            </div>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 2</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric2}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric2Description}}}}</p>
            </div>
            <div class='metric-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6'>
                <h3 class='text-lg font-semibold text-gray-700 mb-2'>Key Metric 3</h3>
                <div class='text-3xl font-bold' style='color: {primaryColor}'>{{{{metric3}}}}</div>
                <p class='text-sm text-gray-500 mt-1'>{{{{metric3Description}}}}</p>
            </div>
        </div>
        
        <div class='grid grid-cols-1 lg:grid-cols-2 gap-8'>
            <div class='chart-section'>
                <h2 class='text-2xl font-bold text-gray-800 mb-4'>Trend Analysis</h2>
                <div class='chart-container bg-gray-50 rounded-lg p-6 min-h-80'>
                    <div class='chart-placeholder flex items-center justify-center h-full text-gray-400'>
                        {{{{trendChart}}}}
                    </div>
                </div>
            </div>
            <div class='insights-section'>
                <h2 class='text-2xl font-bold text-gray-800 mb-4'>Key Insights</h2>
                <div class='insights-content space-y-4'>
                    <div class='insight-item p-4 bg-blue-50 rounded-lg border-l-4' style='border-color: {primaryColor}'>
                        <h4 class='font-semibold text-gray-800'>{{{{insight1Title}}}}</h4>
                        <p class='text-gray-600 mt-1'>{{{{insight1Content}}}}</p>
                    </div>
                    <div class='insight-item p-4 bg-blue-50 rounded-lg border-l-4' style='border-color: {primaryColor}'>
                        <h4 class='font-semibold text-gray-800'>{{{{insight2Title}}}}</h4>
                        <p class='text-gray-600 mt-1'>{{{{insight2Content}}}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class='report-footer bg-gray-50 p-6 text-center'>
        <p class='text-sm text-gray-500'>
            This report was generated by CSHero Report Engine • 
            <span class='font-medium'>Confidential & Proprietary</span>
        </p>
    </div>
</div>";
        }

        private string GenerateReportCss(ReportStyleMapping? mapping)
        {
            var theme = mapping?.Theme ?? "default";
            var primaryColor = mapping?.PrimaryColor ?? "#3b82f6";
            var secondaryColor = mapping?.SecondaryColor ?? "#1e40af";

            return $@"
.report-container {{
    @apply max-w-6xl mx-auto;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
}}

.report-header {{
    background: linear-gradient(135deg, {primaryColor} 0%, {secondaryColor} 100%);
    position: relative;
}}

.report-header::before {{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns=""http://www.w3.org/2000/svg"" viewBox=""0 0 100 100""><defs><pattern id=""grain"" width=""100"" height=""100"" patternUnits=""userSpaceOnUse""><circle cx=""50"" cy=""50"" r=""1"" fill=""white"" opacity=""0.1""/></pattern></defs><rect width=""100"" height=""100"" fill=""url(%23grain)""/></svg>');
    opacity: 0.3;
}}

.metric-card {{
    @apply transition-all duration-300 hover:shadow-lg;
    border: 1px solid rgba(0,0,0,0.05);
}}

.metric-card:hover {{
    transform: translateY(-2px);
}}

.chart-container {{
    @apply border-2 border-dashed border-gray-200;
    background: linear-gradient(45deg, #f9fafb 25%, transparent 25%), 
                linear-gradient(-45deg, #f9fafb 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f9fafb 75%), 
                linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}}

.insight-item {{
    @apply transition-all duration-200 hover:shadow-md;
}}

.insight-item:hover {{
    transform: translateX(4px);
}}

/* Theme-specific styles for {theme} */
.theme-{theme.Replace("-", "_")} {{
    --primary-color: {primaryColor};
    --secondary-color: {secondaryColor};
}}

/* Responsive design */
@media (max-width: 1024px) {{
    .report-container {{
        @apply mx-4;
    }}
    
    .report-header {{
        @apply p-6;
    }}
    
    .report-content {{
        @apply p-6;
    }}
}}

@media (max-width: 768px) {{
    .report-header h1 {{
        @apply text-2xl;
    }}
    
    .metric-card {{
        @apply text-center;
    }}
    
    .chart-container {{
        @apply min-h-64;
    }}
}}

/* Print styles */
@media print {{
    .report-container {{
        @apply shadow-none max-w-none mx-0;
    }}
    
    .report-header {{
        background: {primaryColor} !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }}
}}";
        }

        private Dictionary<string, ComponentStyle> GenerateComponentStyles(ReportStyleMapping? mapping)
        {
            var primaryColor = mapping?.PrimaryColor ?? "#3b82f6";

            return new Dictionary<string, ComponentStyle>
            {
                ["header"] = new ComponentStyle
                {
                    ComponentId = "header",
                    ComponentType = "header",
                    CssClasses = new List<string> { "report-header", "text-white", "p-8" },
                    InlineStyles = new Dictionary<string, string>
                    {
                        ["background"] = $"linear-gradient(135deg, {primaryColor} 0%, {primaryColor}dd 100%)"
                    },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Padding = "2rem",
                        Display = "block"
                    }
                },
                ["metrics"] = new ComponentStyle
                {
                    ComponentId = "metrics",
                    ComponentType = "metrics-grid",
                    CssClasses = new List<string> { "grid", "grid-cols-1", "lg:grid-cols-3", "gap-8", "mb-8" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Display = "grid"
                    }
                },
                ["chart"] = new ComponentStyle
                {
                    ComponentId = "chart",
                    ComponentType = "chart",
                    CssClasses = new List<string> { "chart-container", "bg-gray-50", "rounded-lg", "p-6", "min-h-80" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "320px",
                        Padding = "1.5rem",
                        Display = "flex"
                    }
                },
                ["insights"] = new ComponentStyle
                {
                    ComponentId = "insights",
                    ComponentType = "insights",
                    CssClasses = new List<string> { "insights-content", "space-y-4" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Display = "block"
                    }
                },
                ["footer"] = new ComponentStyle
                {
                    ComponentId = "footer",
                    ComponentType = "footer",
                    CssClasses = new List<string> { "report-footer", "bg-gray-50", "p-6", "text-center" },
                    Layout = new ComponentLayout
                    {
                        Width = "100%",
                        Height = "auto",
                        Padding = "1.5rem",
                        Display = "block"
                    }
                }
            };
        }

        private long CalculateStylesSize(ReportVersion version)
        {
            // Calculate approximate size of styles data
            var stylesData = new
            {
                StyleDocumentId = version.StyleDocumentId,
                VersionId = version.Id,
                CreatedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(stylesData);
            return Encoding.UTF8.GetByteCount(json);
        }

        // Blob data models
        public class ComponentsBlob
        {
            public Guid ReportId { get; set; }
            public Guid VersionId { get; set; }
            public int VersionNumber { get; set; }
            public string TenantId { get; set; } = string.Empty;
            public string ComponentDataJson { get; set; } = string.Empty;
            public DateTime CreatedAt { get; set; }
            public string CreatedBy { get; set; } = string.Empty;
        }

        public class StylesBlob
        {
            public Guid ReportId { get; set; }
            public Guid VersionId { get; set; }
            public int VersionNumber { get; set; }
            public string TenantId { get; set; } = string.Empty;
            public string? StyleDocumentId { get; set; }
            public DateTime CreatedAt { get; set; }
            public string CreatedBy { get; set; } = string.Empty;
        }

        // Enhanced styles blob with full HTML, CSS, and component styles (extracted from CosmosDbSeeder)
        public class EnhancedStylesBlob
        {
            public Guid ReportId { get; set; }
            public Guid VersionId { get; set; }
            public int VersionNumber { get; set; }
            public string TenantId { get; set; } = string.Empty;
            public string? StyleDocumentId { get; set; }
            public string HtmlContent { get; set; } = string.Empty;
            public string CssStyles { get; set; } = string.Empty;
            public Dictionary<string, ComponentStyle> ComponentStyles { get; set; } = new();
            public StyleMetadata Metadata { get; set; } = new();
            public DateTime CreatedAt { get; set; }
            public string CreatedBy { get; set; } = string.Empty;
        }

        // Style metadata (extracted from CosmosDbSeeder)
        public class StyleMetadata
        {
            public string Framework { get; set; } = string.Empty;
            public string FrameworkVersion { get; set; } = string.Empty;
            public string Theme { get; set; } = string.Empty;
            public string ColorScheme { get; set; } = string.Empty;
            public List<string> Tags { get; set; } = new();
            public Dictionary<string, object> CustomProperties { get; set; } = new();
        }

        // Component style configuration (extracted from CosmosDbSeeder)
        public class ComponentStyle
        {
            public string ComponentId { get; set; } = string.Empty;
            public string ComponentType { get; set; } = string.Empty;
            public List<string> CssClasses { get; set; } = new();
            public Dictionary<string, string> InlineStyles { get; set; } = new();
            public string CustomCss { get; set; } = string.Empty;
            public ComponentLayout Layout { get; set; } = new();
        }

        // Component layout configuration (extracted from CosmosDbSeeder)
        public class ComponentLayout
        {
            public string Width { get; set; } = string.Empty;
            public string Height { get; set; } = string.Empty;
            public string Margin { get; set; } = string.Empty;
            public string Padding { get; set; } = string.Empty;
            public string Display { get; set; } = string.Empty;
            public string Position { get; set; } = string.Empty;
        }

        // Report style mapping configuration (extracted from CosmosDbSeeder)
        public class ReportStyleMapping
        {
            public string CompanyName { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public string Theme { get; set; } = string.Empty;
            public string PrimaryColor { get; set; } = string.Empty;
            public string SecondaryColor { get; set; } = string.Empty;
            public string ReportId { get; set; } = string.Empty;
        }

        // Blob seed data model for loading from JSON
        public class BlobSeedData
        {
            public List<ReportStyleMapping> StyleMappings { get; set; } = new();
            public Dictionary<string, object> HtmlTemplates { get; set; } = new();
            public Dictionary<string, object> CssFramework { get; set; } = new();
            public Dictionary<string, object> ComponentDefinitions { get; set; } = new();
            public Dictionary<string, object> ThemeConfigurations { get; set; } = new();
            public Dictionary<string, string> ResponsiveBreakpoints { get; set; } = new();
            public Dictionary<string, object> PrintStyles { get; set; } = new();
        }

        // Data classes for ReportStyleDocument-compatible seeding (avoiding circular dependencies)
        public class ReportStyleDocumentData
        {
            public string Id { get; set; } = string.Empty;
            public string PartitionKey { get; set; } = string.Empty;
            public Guid? ReportVersionId { get; set; }
            public Guid TenantId { get; set; }
            public string StyleType { get; set; } = string.Empty;
            public string HtmlContent { get; set; } = string.Empty;
            public string CssStyles { get; set; } = string.Empty;
            public Dictionary<string, object> InlineStyles { get; set; } = new();
            public Dictionary<string, ComponentStyleData> ComponentStyles { get; set; } = new();
            public StyleMetadataData Metadata { get; set; } = new();
            public DateTime CreatedAt { get; set; }
            public DateTime LastModified { get; set; }
            public Guid CreatedBy { get; set; }
            public Guid LastModifiedBy { get; set; }
            public int Version { get; set; }
            public bool IsActive { get; set; }
        }

        public class ComponentStyleData
        {
            public string ComponentId { get; set; } = string.Empty;
            public string ComponentType { get; set; } = string.Empty;
            public List<string> CssClasses { get; set; } = new();
            public Dictionary<string, string> InlineStyles { get; set; } = new();
            public string CustomCss { get; set; } = string.Empty;
            public ComponentLayoutData Layout { get; set; } = new();
        }

        public class ComponentLayoutData
        {
            public string Width { get; set; } = string.Empty;
            public string Height { get; set; } = string.Empty;
            public string Margin { get; set; } = string.Empty;
            public string Padding { get; set; } = string.Empty;
            public string Display { get; set; } = string.Empty;
            public string Position { get; set; } = string.Empty;
        }

        public class StyleMetadataData
        {
            public string Framework { get; set; } = string.Empty;
            public string FrameworkVersion { get; set; } = string.Empty;
            public string Theme { get; set; } = string.Empty;
            public string ColorScheme { get; set; } = string.Empty;
            public List<string> Tags { get; set; } = new();
            public Dictionary<string, object> CustomProperties { get; set; } = new();
        }
    }
}
