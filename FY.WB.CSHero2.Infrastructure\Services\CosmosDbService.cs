using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using FY.WB.CSHero2.Infrastructure.Configuration;
using System.Text.Json;

namespace FY.WB.CSHero2.Infrastructure.Services;

public interface ICosmosDbService
{
    Task<T?> GetItemAsync<T>(string id, string partitionKey) where T : class;
    Task<T> UpsertItemAsync<T>(T item, string partitionKey) where T : class;
    Task DeleteItemAsync(string id, string partitionKey);
    Task<IEnumerable<T>> GetItemsAsync<T>(string queryString) where T : class;
}

public class CosmosDbService : ICosmosDbService, IDisposable
{
    private readonly Container _container;
    private readonly CosmosClient _cosmosClient;

    public CosmosDbService(IOptions<CosmosDbOptions> options)
    {
        var cosmosOptions = options.Value;
        
        // Validate configuration
        if (string.IsNullOrEmpty(cosmosOptions.ConnectionString))
        {
            throw new InvalidOperationException("CosmosDb:ConnectionString is not configured or is empty");
        }
        
        if (string.IsNullOrEmpty(cosmosOptions.DatabaseName))
        {
            throw new InvalidOperationException("CosmosDb:DatabaseName is not configured or is empty");
        }
        
        if (string.IsNullOrEmpty(cosmosOptions.ContainerName))
        {
            throw new InvalidOperationException("CosmosDb:ContainerName is not configured or is empty");
        }
        
        _cosmosClient = new CosmosClient(cosmosOptions.ConnectionString, new CosmosClientOptions
        {
            SerializerOptions = new CosmosSerializationOptions
            {
                // Removed PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                // to preserve exact JSON property names like "TenantId" for partition key matching
            },
            MaxRetryAttemptsOnRateLimitedRequests = 3,
            MaxRetryWaitTimeOnRateLimitedRequests = TimeSpan.FromSeconds(30)
        });
        _container = _cosmosClient.GetContainer(cosmosOptions.DatabaseName, cosmosOptions.ContainerName);
    }

    public async Task<T?> GetItemAsync<T>(string id, string partitionKey) where T : class
    {
        try
        {
            var response = await _container.ReadItemAsync<T>(id, new PartitionKey(partitionKey));
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return null;
        }
    }

    public async Task<T> UpsertItemAsync<T>(T item, string partitionKey) where T : class
    {
        var response = await _container.UpsertItemAsync(item, new PartitionKey(partitionKey));
        return response.Resource;
    }

    public async Task DeleteItemAsync(string id, string partitionKey)
    {
        await _container.DeleteItemAsync<object>(id, new PartitionKey(partitionKey));
    }

    public async Task<IEnumerable<T>> GetItemsAsync<T>(string queryString) where T : class
    {
        var query = _container.GetItemQueryIterator<T>(new QueryDefinition(queryString));
        var results = new List<T>();

        while (query.HasMoreResults)
        {
            var response = await query.ReadNextAsync();
            results.AddRange(response.ToList());
        }

        return results;
    }

    public void Dispose()
    {
        _cosmosClient?.Dispose();
    }
}
