"use client";

import { useState, useEffect } from "react";
import { ArrowLeft, Search, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { StyleTemplate } from "../../types";

interface StyleTemplatePanelProps {
  selectedTemplate: string;
  onTemplateSelect: (templateId: string) => void;
}

// Extended StyleTemplate to include category and multiple page previews
interface EnhancedStyleTemplate extends StyleTemplate {
  category: string;
  description?: string;
  pages: {
    coverPage: string;
    contentPage: string;
    chartsPage: string;
  };
}

export function StyleTemplatePanel({ selectedTemplate, onTemplateSelect }: StyleTemplatePanelProps) {
  const [previewingTemplate, setPreviewingTemplate] = useState<EnhancedStyleTemplate | null>(null);
  const [allStyleTemplates, setAllStyleTemplates] = useState<EnhancedStyleTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<EnhancedStyleTemplate[]>([]);
  const [displayedTemplates, setDisplayedTemplates] = useState<EnhancedStyleTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showMore, setShowMore] = useState<boolean>(false);
  const [activePage, setActivePage] = useState<'cover' | 'content' | 'charts'>('cover');

  // Fetch style templates from the server
  useEffect(() => {
    const fetchStyleTemplates = async () => {
      try {
        setLoading(true);
        // Create style templates based on the files in mock-server/storage/style-templates
        // Extended with additional variations to reach 9+ templates
        const templates: EnhancedStyleTemplate[] = [
          {
            id: "minimal-light",
            name: "Minimal Light",
            category: "Clean",
            description: "Simple, clean design with light background",
            thumbnailUrl: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/minimal/minimal-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg"
            }
          },
          {
            id: "minimal-dark",
            name: "Minimal Dark",
            category: "Clean",
            description: "Simple, clean design with dark accents",
            thumbnailUrl: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/minimal/minimal-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg"
            }
          },
          {
            id: "minimal-accent",
            name: "Minimal Accent",
            category: "Clean",
            description: "Simple design with colorful accents",
            thumbnailUrl: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/minimal/minimal-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg"
            }
          },
          {
            id: "modern-blue",
            name: "Modern Blue",
            category: "Contemporary",
            description: "Contemporary design with blue color scheme",
            thumbnailUrl: "/assets/style-templates/modern/modern-style-template.jpg",
            templateUrl: "/assets/style-templates/modern/innocloud-revised-report.html",
            pages: {
              coverPage: "/assets/style-templates/modern/modern-style-template.jpg",
              contentPage: "/assets/style-templates/modern/modern-style-template.jpg",
              chartsPage: "/assets/style-templates/modern/modern-style-template.jpg"
            }
          },
          {
            id: "modern-teal",
            name: "Modern Teal",
            category: "Contemporary",
            description: "Contemporary design with teal accents",
            thumbnailUrl: "/assets/style-templates/modern/modern-style-template.jpg",
            templateUrl: "/assets/style-templates/modern/innocloud-revised-report.html",
            pages: {
              coverPage: "/assets/style-templates/modern/modern-style-template.jpg",
              contentPage: "/assets/style-templates/modern/modern-style-template.jpg",
              chartsPage: "/assets/style-templates/modern/modern-style-template.jpg"
            }
          },
          {
            id: "modern-gradient",
            name: "Modern Gradient",
            category: "Contemporary",
            description: "Contemporary design with gradient elements",
            thumbnailUrl: "/assets/style-templates/modern/modern-style-template.jpg",
            templateUrl: "/assets/style-templates/modern/innocloud-revised-report.html",
            pages: {
              coverPage: "/assets/style-templates/modern/modern-style-template.jpg",
              contentPage: "/assets/style-templates/modern/modern-style-template.jpg",
              chartsPage: "/assets/style-templates/modern/modern-style-template.jpg"
            }
          },
          {
            id: "professional-classic",
            name: "Professional Classic",
            category: "Business",
            description: "Traditional professional business layout",
            thumbnailUrl: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/professional/professional-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg"
            }
          },
          {
            id: "professional-modern",
            name: "Professional Modern",
            category: "Business",
            description: "Modern take on professional business reports",
            thumbnailUrl: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/professional/professional-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg"
            }
          },
          {
            id: "professional-bold",
            name: "Professional Bold",
            category: "Business",
            description: "Bold, impactful professional design",
            thumbnailUrl: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/professional/professional-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg"
            }
          },
          {
            id: "creative-colorful",
            name: "Creative Colorful",
            category: "Creative",
            description: "Vibrant, colorful creative layout",
            thumbnailUrl: "/assets/style-templates/modern/modern-style-template.jpg",
            templateUrl: "/assets/style-templates/modern/innocloud-revised-report.html",
            pages: {
              coverPage: "/assets/style-templates/modern/modern-style-template.jpg",
              contentPage: "/assets/style-templates/modern/modern-style-template.jpg",
              chartsPage: "/assets/style-templates/modern/modern-style-template.jpg"
            }
          },
          {
            id: "data-focused",
            name: "Data Focused",
            category: "Analytics",
            description: "Optimized for data visualization",
            thumbnailUrl: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/professional/professional-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/professional/professional-style-thumbnail.jpg"
            }
          },
          {
            id: "executive-summary",
            name: "Executive Summary",
            category: "Business",
            description: "Designed for executive-level reports",
            thumbnailUrl: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
            templateUrl: "/assets/style-templates/minimal/minimal-style-template.html",
            pages: {
              coverPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              contentPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg",
              chartsPage: "/assets/style-templates/minimal/minimal-style-thumbnail.jpg"
            }
          }
        ];
        
        setAllStyleTemplates(templates);
        setFilteredTemplates(templates);
        setDisplayedTemplates(templates.slice(0, 9));
        setLoading(false);
      } catch (err) {
        console.error('Error fetching style templates:', err);
        setError('Failed to load style templates');
        setLoading(false);
      }
    };

    fetchStyleTemplates();
  }, []);

  // Filter templates based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTemplates(allStyleTemplates);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = allStyleTemplates.filter(template => 
        template.name.toLowerCase().includes(query) || 
        template.category.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query))
      );
      setFilteredTemplates(filtered);
    }
    
    // Reset to showing first 9 templates whenever filter changes
    setShowMore(false);
  }, [searchQuery, allStyleTemplates]);

  // Update displayed templates when filteredTemplates changes or showMore toggles
  useEffect(() => {
    if (showMore) {
      setDisplayedTemplates(filteredTemplates);
    } else {
      setDisplayedTemplates(filteredTemplates.slice(0, 9));
    }
  }, [filteredTemplates, showMore]);

  const handleTemplateClick = (template: EnhancedStyleTemplate) => {
    setPreviewingTemplate(template);
    setActivePage('cover'); // Reset to cover page when previewing a new template
  };

  const handleSelectTemplate = (templateId: string) => {
    onTemplateSelect(templateId);
    setPreviewingTemplate(null);
  };

  const handleBackToList = () => {
    setPreviewingTemplate(null);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleShowMore = () => {
    setShowMore(true);
  };

  const getActivePageImage = () => {
    if (!previewingTemplate) return '';
    
    switch (activePage) {
      case 'cover':
        return previewingTemplate.pages.coverPage;
      case 'content':
        return previewingTemplate.pages.contentPage;
      case 'charts':
        return previewingTemplate.pages.chartsPage;
      default:
        return previewingTemplate.pages.coverPage;
    }
  };

  // If we're previewing a template, show the preview with multi-page navigation
  if (previewingTemplate) {
    return (
      <div className="space-y-4">
        <div className="border rounded-lg p-4">
          <div className="flex items-center mb-4">
            <button 
              onClick={handleBackToList}
              className="mr-2 p-1 rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h3 className="font-medium">{previewingTemplate.name} Style Template</h3>
          </div>
          
          {/* Template preview with page navigation */}
          <div className="relative w-full border rounded-lg overflow-hidden mb-4">
            <div className="flex justify-center items-center bg-gray-100 py-2 border-b">
              <button 
                className={`px-4 py-1 mx-1 rounded-md ${activePage === 'cover' ? 'bg-primary text-white' : 'hover:bg-gray-200'}`}
                onClick={() => setActivePage('cover')}
              >
                Cover
              </button>
              <button 
                className={`px-4 py-1 mx-1 rounded-md ${activePage === 'content' ? 'bg-primary text-white' : 'hover:bg-gray-200'}`}
                onClick={() => setActivePage('content')}
              >
                Content
              </button>
              <button 
                className={`px-4 py-1 mx-1 rounded-md ${activePage === 'charts' ? 'bg-primary text-white' : 'hover:bg-gray-200'}`}
                onClick={() => setActivePage('charts')}
              >
                Charts
              </button>
            </div>
            
            <div className="h-[300px] flex items-center justify-center">
              <Image
                src={getActivePageImage()}
                alt={`${previewingTemplate.name} ${activePage} preview`}
                width={400}
                height={300}
                className="object-contain max-h-full max-w-full"
              />
            </div>
            
            {/* Pagination arrows */}
            <div className="absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between px-2 pointer-events-none">
              <button 
                className="bg-white rounded-full p-2 shadow hover:bg-gray-100 pointer-events-auto"
                onClick={() => {
                  if (activePage === 'content') setActivePage('cover');
                  else if (activePage === 'charts') setActivePage('content');
                }}
                disabled={activePage === 'cover'}
              >
                <ChevronLeft className={`h-5 w-5 ${activePage === 'cover' ? 'text-gray-300' : 'text-gray-700'}`} />
              </button>
              <button 
                className="bg-white rounded-full p-2 shadow hover:bg-gray-100 pointer-events-auto"
                onClick={() => {
                  if (activePage === 'cover') setActivePage('content');
                  else if (activePage === 'content') setActivePage('charts');
                }}
                disabled={activePage === 'charts'}
              >
                <ChevronRight className={`h-5 w-5 ${activePage === 'charts' ? 'text-gray-300' : 'text-gray-700'}`} />
              </button>
            </div>
          </div>
          
          {/* Template description */}
          {previewingTemplate.description && (
            <p className="text-sm text-gray-600 mb-4 italic">
              {previewingTemplate.description}
            </p>
          )}
          
          <div className="flex justify-end space-x-2">
            <button
              className="px-4 py-2 border rounded-md hover:bg-gray-50"
              onClick={handleBackToList}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70"
              onClick={() => handleSelectTemplate(previewingTemplate.id)}
            >
              Select Template
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Otherwise, show the grid of templates with search
  return (
    <div className="space-y-4">
      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-4">Style Templates</h3>
        
        {/* Search bar */}
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search templates..."
            className="pl-10 w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          {loading ? (
            <div className="col-span-3 p-4 text-center text-gray-500 border-2 border-dashed rounded-md">
              <p>Loading style templates...</p>
            </div>
          ) : error ? (
            <div className="col-span-3 p-4 text-center text-red-500 border-2 border-dashed rounded-md">
              <p>{error}</p>
            </div>
          ) : displayedTemplates.length === 0 ? (
            <div className="col-span-3 p-4 text-center text-gray-500 border-2 border-dashed rounded-md">
              <p>No matching templates found</p>
            </div>
          ) : (
            displayedTemplates.map((template) => (
              <div
                key={template.id}
                className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                  selectedTemplate === template.id ? "border-primary bg-blue-50" : ""
                }`}
                onClick={() => handleTemplateClick(template)}
              >
                <div className="aspect-w-16 aspect-h-9 mb-2 overflow-hidden rounded border">
                  <Image 
                    src={template.thumbnailUrl} 
                    alt={template.name}
                    width={150}
                    height={100}
                    className="object-cover w-full h-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-sm">{template.name}</h4>
                    <p className="text-xs text-gray-500">{template.category}</p>
                  </div>
                  {selectedTemplate === template.id && (
                    <div className="bg-primary text-white text-xs px-2 py-1 rounded">Selected</div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
        
        {/* Show more button */}
        {!loading && !error && filteredTemplates.length > 9 && !showMore && (
          <div className="mt-4 text-center">
            <button
              className="px-4 py-2 border rounded-md hover:bg-gray-50 text-primary"
              onClick={handleShowMore}
            >
              Show More
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
