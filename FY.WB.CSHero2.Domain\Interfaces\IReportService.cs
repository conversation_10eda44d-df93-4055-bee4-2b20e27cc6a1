using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Domain.Interfaces
{
    /// <summary>
    /// Service interface for managing report instances in the Report Rendering Engine V2
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Gets a specific report by ID
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Report entity</returns>
        Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all reports owned by the current user
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of user's reports</returns>
        Task<List<Report>> GetUserReportsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new report instance
        /// </summary>
        /// <param name="request">Report creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Created report</returns>
        Task<Report> CreateReportAsync(CreateReportRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates the data for a report without triggering a re-render
        /// </summary>
        /// <param name="reportId">Report to update</param>
        /// <param name="data">New data values</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task UpdateReportDataAsync(Guid reportId, Dictionary<string, object> data, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current version of a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Current report version</returns>
        Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a report and all its versions
        /// </summary>
        /// <param name="reportId">Report to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets reports by client with pagination
        /// </summary>
        /// <param name="clientId">Client identifier</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paginated list of reports</returns>
        Task<(List<Report> Reports, int TotalCount)> GetReportsByClientAsync(
            Guid clientId, 
            int page = 1, 
            int pageSize = 20, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets reports created from a specific template
        /// </summary>
        /// <param name="templateId">Template identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of reports created from template</returns>
        Task<List<Report>> GetReportsByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates basic report information (name, status, etc.)
        /// </summary>
        /// <param name="reportId">Report to update</param>
        /// <param name="request">Update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Updated report</returns>
        Task<Report> UpdateReportAsync(Guid reportId, UpdateReportRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report data as a dictionary for the current version
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Report data dictionary</returns>
        Task<Dictionary<string, object>> GetReportDataAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a report exists and user has access to it
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if report exists and user has access</returns>
        Task<bool> ReportExistsAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report statistics for the current user
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Report statistics</returns>
        Task<ReportStatistics> GetReportStatisticsAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Request model for creating a new report
    /// </summary>
    public class CreateReportRequest
    {
        public string Name { get; set; } = string.Empty;
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public Guid? TemplateId { get; set; }
        public Dictionary<string, object> InitialData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Request model for updating a report
    /// </summary>
    public class UpdateReportRequest
    {
        public string? Name { get; set; }
        public string? Status { get; set; }
        public string? Category { get; set; }
        public string? Author { get; set; }
    }

    /// <summary>
    /// Report statistics model
    /// </summary>
    public class ReportStatistics
    {
        public int TotalReports { get; set; }
        public int TemplateBasedReports { get; set; }
        public int CustomReports { get; set; }
        public int ReportsThisMonth { get; set; }
        public Dictionary<string, int> ReportsByStatus { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> ReportsByCategory { get; set; } = new Dictionary<string, int>();
    }
}
