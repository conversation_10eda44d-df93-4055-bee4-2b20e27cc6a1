using FY.WB.CSHero2.Application.Models.MultiStorage;

namespace FY.WB.CSHero2.Application.Models.Migration
{
    /// <summary>
    /// Result of validation operations
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Whether the validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation score (0-100)
        /// </summary>
        public double ValidationScore { get; set; }

        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<ValidationWarning> Warnings { get; set; } = new List<ValidationWarning>();

        /// <summary>
        /// Detailed validation results
        /// </summary>
        public ValidationDetails Details { get; set; } = new ValidationDetails();

        /// <summary>
        /// When the validation was performed
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Duration of validation
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Validation error information
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// Error code for categorization
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Field or component that failed validation
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Expected value or format
        /// </summary>
        public string Expected { get; set; } = string.Empty;

        /// <summary>
        /// Actual value found
        /// </summary>
        public string Actual { get; set; } = string.Empty;

        /// <summary>
        /// Error severity
        /// </summary>
        public ErrorSeverity Severity { get; set; } = ErrorSeverity.Error;
    }

    /// <summary>
    /// Validation warning information
    /// </summary>
    public class ValidationWarning
    {
        /// <summary>
        /// Warning code for categorization
        /// </summary>
        public string WarningCode { get; set; } = string.Empty;

        /// <summary>
        /// Warning message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Field or component that generated the warning
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Recommended action
        /// </summary>
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Detailed validation information
    /// </summary>
    public class ValidationDetails
    {
        /// <summary>
        /// Data integrity validation passed
        /// </summary>
        public bool DataIntegrityValid { get; set; }

        /// <summary>
        /// Cross-storage references validation passed
        /// </summary>
        public bool CrossReferencesValid { get; set; }

        /// <summary>
        /// Schema compliance validation passed
        /// </summary>
        public bool SchemaValid { get; set; }

        /// <summary>
        /// Storage connectivity validation passed
        /// </summary>
        public bool StorageConnectivityValid { get; set; }

        /// <summary>
        /// Performance validation passed
        /// </summary>
        public bool PerformanceValid { get; set; }

        /// <summary>
        /// Number of records validated
        /// </summary>
        public int RecordsValidated { get; set; }

        /// <summary>
        /// Number of validation checks performed
        /// </summary>
        public int ChecksPerformed { get; set; }

        /// <summary>
        /// Number of checks that passed
        /// </summary>
        public int ChecksPassed { get; set; }
    }

    /// <summary>
    /// Result of rollback operations
    /// </summary>
    public class RollbackResult
    {
        /// <summary>
        /// Whether the rollback was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of reports rolled back
        /// </summary>
        public int ReportsRolledBack { get; set; }

        /// <summary>
        /// Number of Cosmos DB documents deleted
        /// </summary>
        public int CosmosDocumentsDeleted { get; set; }

        /// <summary>
        /// Number of blob storage items deleted
        /// </summary>
        public int BlobItemsDeleted { get; set; }

        /// <summary>
        /// Number of SQL references restored
        /// </summary>
        public int SqlReferencesRestored { get; set; }

        /// <summary>
        /// Rollback duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Rollback errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Rollback warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// When the rollback was performed
        /// </summary>
        public DateTime RolledBackAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of dry run operations
    /// </summary>
    public class DryRunResult
    {
        /// <summary>
        /// Whether the dry run completed successfully
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of reports that would be migrated
        /// </summary>
        public int ReportsToMigrate { get; set; }

        /// <summary>
        /// Number of versions that would be migrated
        /// </summary>
        public int VersionsToMigrate { get; set; }

        /// <summary>
        /// Estimated total data size to migrate
        /// </summary>
        public long EstimatedDataSize { get; set; }

        /// <summary>
        /// Estimated migration duration
        /// </summary>
        public TimeSpan EstimatedDuration { get; set; }

        /// <summary>
        /// Potential issues identified
        /// </summary>
        public List<string> PotentialIssues { get; set; } = new List<string>();

        /// <summary>
        /// Recommendations for migration
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// Reports that would be migrated
        /// </summary>
        public List<ReportMigrationInfo> ReportsToMigrateList { get; set; } = new List<ReportMigrationInfo>();

        /// <summary>
        /// When the dry run was performed
        /// </summary>
        public DateTime PerformedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of cancellation operations
    /// </summary>
    public class CancellationResult
    {
        /// <summary>
        /// Whether the cancellation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Whether the operation was cancelled gracefully
        /// </summary>
        public bool GracefulCancellation { get; set; }

        /// <summary>
        /// Number of reports that were partially migrated
        /// </summary>
        public int PartiallyMigratedReports { get; set; }

        /// <summary>
        /// Cleanup actions required
        /// </summary>
        public List<string> CleanupRequired { get; set; } = new List<string>();

        /// <summary>
        /// Cancellation message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the cancellation occurred
        /// </summary>
        public DateTime CancelledAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of cleanup operations
    /// </summary>
    public class CleanupResult
    {
        /// <summary>
        /// Whether the cleanup was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of items cleaned up
        /// </summary>
        public int ItemsCleanedUp { get; set; }

        /// <summary>
        /// Number of orphaned Cosmos DB documents removed
        /// </summary>
        public int OrphanedCosmosDocuments { get; set; }

        /// <summary>
        /// Number of orphaned blob storage items removed
        /// </summary>
        public int OrphanedBlobItems { get; set; }

        /// <summary>
        /// Number of incomplete SQL references fixed
        /// </summary>
        public int IncompleteSqlReferences { get; set; }

        /// <summary>
        /// Amount of temporary data cleaned up in bytes
        /// </summary>
        public long TemporaryDataCleaned { get; set; }

        /// <summary>
        /// Cleanup duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Cleanup errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// When the cleanup was performed
        /// </summary>
        public DateTime CleanedUpAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of reference validation operations
    /// </summary>
    public class ReferenceValidationResult
    {
        /// <summary>
        /// Whether all references are valid
        /// </summary>
        public bool AllReferencesValid { get; set; }

        /// <summary>
        /// Number of references checked
        /// </summary>
        public int ReferencesChecked { get; set; }

        /// <summary>
        /// Number of valid references
        /// </summary>
        public int ValidReferences { get; set; }

        /// <summary>
        /// Number of invalid references
        /// </summary>
        public int InvalidReferences { get; set; }

        /// <summary>
        /// List of invalid reference details
        /// </summary>
        public List<InvalidReferenceDetail> InvalidReferenceDetails { get; set; } = new List<InvalidReferenceDetail>();

        /// <summary>
        /// Validation duration
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Details about an invalid reference
    /// </summary>
    public class InvalidReferenceDetail
    {
        /// <summary>
        /// Report ID with invalid reference
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Reference type (CosmosDocument, BlobStorage)
        /// </summary>
        public string ReferenceType { get; set; } = string.Empty;

        /// <summary>
        /// Reference value that is invalid
        /// </summary>
        public string ReferenceValue { get; set; } = string.Empty;

        /// <summary>
        /// Reason for invalidity
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Result of data comparison operations
    /// </summary>
    public class DataComparisonResult
    {
        /// <summary>
        /// Whether the data matches
        /// </summary>
        public bool DataMatches { get; set; }

        /// <summary>
        /// Comparison confidence score (0-100)
        /// </summary>
        public double ConfidenceScore { get; set; }

        /// <summary>
        /// Number of fields compared
        /// </summary>
        public int FieldsCompared { get; set; }

        /// <summary>
        /// Number of matching fields
        /// </summary>
        public int MatchingFields { get; set; }

        /// <summary>
        /// Number of differing fields
        /// </summary>
        public int DifferingFields { get; set; }

        /// <summary>
        /// List of field differences
        /// </summary>
        public List<FieldDifference> Differences { get; set; } = new List<FieldDifference>();

        /// <summary>
        /// Comparison duration
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Details about a field difference
    /// </summary>
    public class FieldDifference
    {
        /// <summary>
        /// Field name
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Original value
        /// </summary>
        public string OriginalValue { get; set; } = string.Empty;

        /// <summary>
        /// Migrated value
        /// </summary>
        public string MigratedValue { get; set; } = string.Empty;

        /// <summary>
        /// Difference type
        /// </summary>
        public string DifferenceType { get; set; } = string.Empty;

        /// <summary>
        /// Whether this difference is acceptable
        /// </summary>
        public bool IsAcceptable { get; set; }
    }

    /// <summary>
    /// Result of storage validation operations
    /// </summary>
    public class StorageValidationResult
    {
        /// <summary>
        /// Whether all storage systems are accessible
        /// </summary>
        public bool AllStorageAccessible { get; set; }

        /// <summary>
        /// SQL database validation result
        /// </summary>
        public StorageConnectionResult SqlDatabase { get; set; } = new StorageConnectionResult();

        /// <summary>
        /// Cosmos DB validation result
        /// </summary>
        public StorageConnectionResult CosmosDb { get; set; } = new StorageConnectionResult();

        /// <summary>
        /// Blob storage validation result
        /// </summary>
        public StorageConnectionResult BlobStorage { get; set; } = new StorageConnectionResult();

        /// <summary>
        /// Overall validation duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// When the validation was performed
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of individual storage connection validation
    /// </summary>
    public class StorageConnectionResult
    {
        /// <summary>
        /// Whether the connection is successful
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// Connection response time in milliseconds
        /// </summary>
        public double ResponseTimeMs { get; set; }

        /// <summary>
        /// Whether read operations are working
        /// </summary>
        public bool CanRead { get; set; }

        /// <summary>
        /// Whether write operations are working
        /// </summary>
        public bool CanWrite { get; set; }

        /// <summary>
        /// Available storage capacity percentage
        /// </summary>
        public double AvailableCapacity { get; set; }

        /// <summary>
        /// Error message if connection failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Additional connection details
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Result of data transformation operations
    /// </summary>
    public class ReportDataTransformResult
    {
        /// <summary>
        /// Whether the transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformed report data
        /// </summary>
        public ReportData? ReportData { get; set; }

        /// <summary>
        /// Number of sections extracted
        /// </summary>
        public int SectionsExtracted { get; set; }

        /// <summary>
        /// Number of fields extracted
        /// </summary>
        public int FieldsExtracted { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of component transformation operations
    /// </summary>
    public class ComponentTransformResult
    {
        /// <summary>
        /// Whether the transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformed components
        /// </summary>
        public List<ReportComponent> Components { get; set; } = new List<ReportComponent>();

        /// <summary>
        /// Number of components extracted
        /// </summary>
        public int ComponentsExtracted { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of section transformation
    /// </summary>
    public class ReportSectionTransformResult
    {
        /// <summary>
        /// Transformed section
        /// </summary>
        public ReportSection? Section { get; set; }

        /// <summary>
        /// Whether transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of field transformation
    /// </summary>
    public class ReportFieldTransformResult
    {
        /// <summary>
        /// Transformed field
        /// </summary>
        public ReportSectionField? Field { get; set; }

        /// <summary>
        /// Whether transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of data validation operations
    /// </summary>
    public class DataValidationResult
    {
        /// <summary>
        /// Whether the data is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Data structure information
        /// </summary>
        public Dictionary<string, object> StructureInfo { get; set; } = new Dictionary<string, object>();
    }
}