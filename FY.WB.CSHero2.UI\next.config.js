/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: [],
    optimizeCss: false,
    esmExternals: 'loose',
  },
  // Webpack configuration to handle case sensitivity issues
  webpack: (config, { dev, isServer }) => {
    // Resolve case sensitivity issues on Windows
    config.resolve.symlinks = false;

    // Add case-sensitive paths plugin for development
    if (dev && !isServer) {
      const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
      config.plugins.push(new CaseSensitivePathsPlugin());
    }

    return config;
  },
  async rewrites() {
    return [
      {
        // Redirect v1 paths to our internal API routes
        source: '/v1/:path*',
        destination: '/api/v1/:path*',
      },
      // Keep the storage path for static files
      process.env.NODE_ENV === 'development' ? {
        source: '/storage/:path*',
        destination: 'http://localhost:3010/storage/:path*',
      } : {
        source: '/storage/:path*',
        destination: '/api/storage/:path*',
      },
    ].filter(Boolean);
  },
  images: {
    domains: ['localhost'],
    unoptimized: process.env.NODE_ENV === 'development',
    dangerouslyAllowSVG: true,
  },
  webpack: (config, { dev, isServer }) => {
    // Fix for case sensitivity issues on Windows
    if (dev && !isServer) {
      config.watchOptions = {
        ...config.watchOptions,
        ignored: /node_modules/,
      };
    }
    
    // Add resolver configuration to handle case sensitivity
    config.resolve = {
      ...config.resolve,
      // Ensure consistent module resolution
      symlinks: false,
    };

    // Add optimization to prevent duplicate modules with different casing
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          default: {
            ...config.optimization.splitChunks?.cacheGroups?.default,
            enforce: true,
          },
        },
      },
    };
    
    return config;
  },
};

module.exports = nextConfig;
