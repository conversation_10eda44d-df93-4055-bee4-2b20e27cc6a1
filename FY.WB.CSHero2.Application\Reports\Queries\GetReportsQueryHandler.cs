using FY.WB.CSHero2.Application.Common.Dtos;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Reports.Dtos;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Reports.Queries
{
    public class GetReportsQueryHandler : IRequestHandler<GetReportsQuery, PagedResult<ReportDto>>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetReportsQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<PagedResult<ReportDto>> Handle(GetReportsQuery request, CancellationToken cancellationToken)
        {
            var query = _context.Reports.AsQueryable();

            // Filter by tenant ID if available
            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(r => r.TenantId == _currentUserService.TenantId);
            }

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(request.Parameters.SearchTerm))
            {
                var term = request.Parameters.SearchTerm.ToLower().Trim();
                query = query.Where(r =>
                    (r.Name != null && r.Name.ToLower().Contains(term)) ||
                    (r.ReportNumber != null && r.ReportNumber.ToLower().Contains(term)) ||
                    (r.ClientName != null && r.ClientName.ToLower().Contains(term)) ||
                    (r.Category != null && r.Category.ToLower().Contains(term)) ||
                    (r.Author != null && r.Author.ToLower().Contains(term)));
            }

            if (!string.IsNullOrWhiteSpace(request.Parameters.Status))
            {
                query = query.Where(r => r.Status.ToLower() == request.Parameters.Status.ToLower());
            }

            if (!string.IsNullOrWhiteSpace(request.Parameters.Category))
            {
                query = query.Where(r => r.Category != null && r.Category.ToLower() == request.Parameters.Category.ToLower());
            }

            if (!string.IsNullOrWhiteSpace(request.Parameters.ClientId))
            {
                if (Guid.TryParse(request.Parameters.ClientId, out var clientId))
                {
                    query = query.Where(r => r.ClientId == clientId);
                }
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(request.Parameters.SortBy))
            {
                var propertyInfo = typeof(Report).GetProperty(request.Parameters.SortBy, System.Reflection.BindingFlags.IgnoreCase | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                if (propertyInfo != null)
                {
                    var parameter = Expression.Parameter(typeof(Report), "r");
                    var propertyAccess = Expression.MakeMemberAccess(parameter, propertyInfo);
                    var orderByExp = Expression.Lambda(propertyAccess, parameter);

                    string methodName = (request.Parameters.SortOrder?.ToLower() == "desc") ? "OrderByDescending" : "OrderBy";

                    MethodCallExpression resultExpression = Expression.Call(
                        typeof(Queryable),
                        methodName,
                        new Type[] { typeof(Report), propertyInfo.PropertyType },
                        query.Expression,
                        Expression.Quote(orderByExp));

                    query = query.Provider.CreateQuery<Report>(resultExpression);
                }
            }
            else
            {
                query = query.OrderByDescending(r => r.CreationTime); // Default sort
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var reports = await query
                .Skip((request.Parameters.Page - 1) * request.Parameters.PageSize)
                .Take(request.Parameters.PageSize)
                .Select(r => new ReportDto
                {
                    Id = r.Id,
                    ReportNumber = r.ReportNumber,
                    ClientId = r.ClientId ?? Guid.Empty,
                    ClientName = r.ClientName,
                    Name = r.Name,
                    Category = r.Category,
                    SlideCount = r.SlideCount,
                    Status = r.Status,
                    Author = r.Author,
                    CreationTime = r.CreationTime,
                    LastModificationTime = r.LastModificationTime
                })
                .ToListAsync(cancellationToken);

            return new PagedResult<ReportDto>(reports, request.Parameters.Page, request.Parameters.PageSize, totalCount);
        }
    }
}
