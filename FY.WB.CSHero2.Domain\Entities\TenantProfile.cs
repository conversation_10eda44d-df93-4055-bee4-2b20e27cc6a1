using System;
using System.Collections.Generic;
using System.Text.Json;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class TenantProfile : FullAuditedMultiTenantEntity<Guid>
    {
        // Navigation collections
        public virtual ICollection<Client> Clients { get; set; } = new List<Client>();
        
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // e.g., "active", "suspended"
        public string Phone { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Subscription { get; set; } = string.Empty; // e.g., "professional", "enterprise"
        public DateTime LastLoginTime { get; set; }
        public string BillingCycle { get; set; } = string.Empty; // e.g., "monthly", "annual", "quarterly"
        public DateTime NextBillingDate { get; set; }
        public string SubscriptionStatus { get; set; } = string.Empty; // e.g., "active", "overdue"

        // Store payment method as JSON
        private string _paymentMethod = "{}";
        public string PaymentMethod
        {
            get => _paymentMethod;
            set => _paymentMethod = string.IsNullOrEmpty(value) ? "{}" : value;
        }

        // Store billing address as JSON
        private string _billingAddress = "{}";
        public string BillingAddress
        {
            get => _billingAddress;
            set => _billingAddress = string.IsNullOrEmpty(value) ? "{}" : value;
        }

        public TenantProfile() : base() { }

        public TenantProfile(
            Guid id,
            string name,
            string email,
            string status,
            string phone,
            string company,
            string subscription,
            DateTime lastLoginTime,
            string billingCycle,
            DateTime nextBillingDate,
            string subscriptionStatus,
            PaymentMethodInfo? paymentMethod = null,
            BillingAddressInfo? billingAddress = null)
            : base(id)
        {
            Name = name;
            Email = email;
            Status = status;
            Phone = phone;
            Company = company;
            Subscription = subscription;
            LastLoginTime = lastLoginTime;
            BillingCycle = billingCycle;
            NextBillingDate = nextBillingDate;
            SubscriptionStatus = subscriptionStatus;

            if (paymentMethod != null)
                SetPaymentMethod(paymentMethod);
            
            if (billingAddress != null)
                SetBillingAddress(billingAddress);
        }

        public void UpdateProfile(
            string name,
            string email,
            string phone,
            string company)
        {
            Name = name;
            Email = email;
            Phone = phone;
            Company = company;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void UpdateSubscription(
            string subscription,
            string billingCycle,
            DateTime nextBillingDate)
        {
            Subscription = subscription;
            BillingCycle = billingCycle;
            NextBillingDate = nextBillingDate;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetStatus(string status)
        {
            Status = status;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void RecordLogin()
        {
            LastLoginTime = DateTime.UtcNow;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetPaymentMethod(PaymentMethodInfo paymentMethod)
        {
            PaymentMethod = JsonSerializer.Serialize(paymentMethod);
        }

        public PaymentMethodInfo? GetPaymentMethod()
        {
            return JsonSerializer.Deserialize<PaymentMethodInfo>(PaymentMethod);
        }

        public void SetBillingAddress(BillingAddressInfo billingAddress)
        {
            BillingAddress = JsonSerializer.Serialize(billingAddress);
        }

        public BillingAddressInfo? GetBillingAddress()
        {
            return JsonSerializer.Deserialize<BillingAddressInfo>(BillingAddress);
        }
    }

    public class PaymentMethodInfo
    {
        public string CardType { get; set; } = string.Empty;
        public string LastFourDigits { get; set; } = string.Empty;
        public string ExpirationDate { get; set; } = string.Empty;
        public string SecurityMethod { get; set; } = string.Empty;
    }

    public class BillingAddressInfo
    {
        public string Street { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
    }
}
