import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { cookies } from 'next/headers'; // Import cookies

const ASPNET_API_URL = process.env.ASPNET_API_URL || 'http://localhost:5056';

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  const { reportId } = params;

  if (!reportId) {
    return NextResponse.json({ message: 'Report ID is required' }, { status: 400 });
  }

  const cookieStore = cookies();
  const authTokenCookie = cookieStore.get('authToken');

  if (!authTokenCookie) {
    return NextResponse.json({ message: 'Authentication token not found' }, { status: 401 });
  }

  const headers = new Headers();
  headers.append('Authorization', `Bearer ${authTokenCookie.value}`);
  headers.append('Content-Type', 'application/json');

  try {
    console.log(`BFF: Fetching report ${reportId} from backend: ${ASPNET_API_URL}/api/Reports/${reportId}`);
    const apiResponse = await fetch(`${ASPNET_API_URL}/api/Reports/${reportId}`, {
      method: 'GET',
      headers: headers,
    });

    const responseBody = await apiResponse.json();

    if (!apiResponse.ok) {
      console.error(`BFF: Error from backend while fetching report ${reportId}: ${apiResponse.status} ${JSON.stringify(responseBody)}`);
      return NextResponse.json(
        { message: responseBody.message || `Failed to fetch report from backend: ${apiResponse.status}` },
        { status: apiResponse.status }
      );
    }

    console.log(`BFF: Successfully fetched report ${reportId}.`);
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error(`BFF: Internal error while fetching report ${reportId}:`, error);
    return NextResponse.json({ message: 'Internal server error in BFF' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  const { reportId } = params;

  if (!reportId) {
    return NextResponse.json({ message: 'Report ID is required' }, { status: 400 });
  }

  const cookieStore = cookies();
  const authTokenCookie = cookieStore.get('authToken');

  if (!authTokenCookie) {
    return NextResponse.json({ message: 'Authentication token not found' }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Add the reportId to the body to match backend expectations
    // Ensure the ID is properly formatted as a GUID
    const bodyWithId = {
      ...body,
      id: reportId // reportId is already a string GUID from the URL
    };

    const headers = new Headers();
    headers.append('Authorization', `Bearer ${authTokenCookie.value}`);
    headers.append('Content-Type', 'application/json');

    console.log(`BFF: Updating report ${reportId} via backend: ${ASPNET_API_URL}/api/Reports/${reportId}`);
    console.log(`BFF: Request body:`, JSON.stringify(bodyWithId));
    const apiResponse = await fetch(`${ASPNET_API_URL}/api/Reports/${reportId}`, {
      method: 'PUT', // Backend uses PUT for updates
      headers: headers,
      body: JSON.stringify(bodyWithId),
    });

    if (!apiResponse.ok) {
      let errorMessage = `Failed to update report in backend: ${apiResponse.status}`;
      try {
        const errorBody = await apiResponse.json();
        errorMessage = errorBody.message || errorMessage;
        console.error(`BFF: Error from backend while updating report ${reportId}: ${apiResponse.status} ${JSON.stringify(errorBody)}`);
      } catch (e) {
        // If error response is not JSON, use the status text
        errorMessage = apiResponse.statusText || errorMessage;
        console.error(`BFF: Error from backend while updating report ${reportId}: ${apiResponse.status} ${errorMessage}`);
      }
      return NextResponse.json(
        { message: errorMessage },
        { status: apiResponse.status }
      );
    }

    console.log(`BFF: Successfully updated report ${reportId}`);

    // Backend returns 204 No Content on successful update, so no response body to parse
    // Return a success response with the updated data
    return NextResponse.json({ message: 'Report updated successfully' }, { status: 200 });
  } catch (error) {
    console.error(`BFF: Error updating report ${reportId}:`, error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
