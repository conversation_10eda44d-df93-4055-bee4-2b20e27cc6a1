{"styleMappings": [{"companyName": "TechCorp Solutions", "category": "Satisfaction Survey", "theme": "satisfaction-analysis", "primaryColor": "#10b981", "secondaryColor": "#059669", "reportId": "CSR-2025-001"}, {"companyName": "Health Plus", "category": "Performance Metrics", "theme": "performance-dashboard", "primaryColor": "#3b82f6", "secondaryColor": "#1e40af", "reportId": "CSR-2025-002"}, {"companyName": "TechFusion Inc.", "category": "Action Plan", "theme": "action-planning", "primaryColor": "#f59e0b", "secondaryColor": "#d97706", "reportId": "CSR-2025-003"}, {"companyName": "TechCorp Solutions", "category": "Channel Analysis", "theme": "channel-effectiveness", "primaryColor": "#8b5cf6", "secondaryColor": "#7c3aed", "reportId": "CSR-2025-004"}, {"companyName": "Health Plus", "category": "User Experience", "theme": "ux-analysis", "primaryColor": "#ef4444", "secondaryColor": "#dc2626", "reportId": "CSR-2025-005"}, {"companyName": "TechFusion Inc.", "category": "Product Improvement", "theme": "product-analysis", "primaryColor": "#06b6d4", "secondaryColor": "#0891b2", "reportId": "CSR-2025-006"}], "htmlTemplates": {"base": {"structure": "report-container", "sections": ["header", "content", "footer"], "framework": "TailwindCSS", "responsive": true}, "header": {"classes": ["report-header", "text-white", "p-8"], "elements": ["title", "client", "date", "logo"], "gradient": true}, "content": {"classes": ["report-content", "p-8"], "sections": ["metrics", "charts", "insights"], "layout": "grid"}, "footer": {"classes": ["report-footer", "bg-gray-50", "p-6", "text-center"], "elements": ["branding", "confidentiality"]}}, "cssFramework": {"name": "TailwindCSS", "version": "3.0", "customProperties": {"fonts": {"primary": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif", "secondary": "'Roboto', sans-serif"}, "spacing": {"container": "max-w-6xl", "section": "p-8", "card": "p-6"}, "effects": {"shadow": "shadow-xl", "rounded": "rounded-lg", "transition": "transition-all duration-300"}}}, "componentDefinitions": {"header": {"componentId": "header", "componentType": "header", "cssClasses": ["report-header", "text-white", "p-8"], "layout": {"width": "100%", "height": "auto", "padding": "2rem", "display": "block"}, "elements": {"title": {"tag": "h1", "classes": ["text-4xl", "font-bold", "mb-2"]}, "client": {"tag": "p", "classes": ["text-lg", "opacity-90"]}, "date": {"tag": "p", "classes": ["text-sm", "opacity-75", "mt-1"]}, "logo": {"tag": "div", "classes": ["w-16", "h-16", "bg-white", "bg-opacity-20", "rounded-lg", "flex", "items-center", "justify-center"]}}}, "metrics": {"componentId": "metrics", "componentType": "metrics-grid", "cssClasses": ["grid", "grid-cols-1", "lg:grid-cols-3", "gap-8", "mb-8"], "layout": {"width": "100%", "height": "auto", "display": "grid"}, "elements": {"card": {"tag": "div", "classes": ["metric-card", "bg-gradient-to-br", "from-gray-50", "to-gray-100", "rounded-lg", "p-6"]}, "title": {"tag": "h3", "classes": ["text-lg", "font-semibold", "text-gray-700", "mb-2"]}, "value": {"tag": "div", "classes": ["text-3xl", "font-bold"]}, "description": {"tag": "p", "classes": ["text-sm", "text-gray-500", "mt-1"]}}}, "chart": {"componentId": "chart", "componentType": "chart", "cssClasses": ["chart-container", "bg-gray-50", "rounded-lg", "p-6", "min-h-80"], "layout": {"width": "100%", "height": "320px", "padding": "1.5rem", "display": "flex"}, "elements": {"placeholder": {"tag": "div", "classes": ["chart-placeholder", "flex", "items-center", "justify-center", "h-full", "text-gray-400"]}}}, "insights": {"componentId": "insights", "componentType": "insights", "cssClasses": ["insights-content", "space-y-4"], "layout": {"width": "100%", "height": "auto", "display": "block"}, "elements": {"item": {"tag": "div", "classes": ["insight-item", "p-4", "bg-blue-50", "rounded-lg", "border-l-4"]}, "title": {"tag": "h4", "classes": ["font-semibold", "text-gray-800"]}, "content": {"tag": "p", "classes": ["text-gray-600", "mt-1"]}}}, "footer": {"componentId": "footer", "componentType": "footer", "cssClasses": ["report-footer", "bg-gray-50", "p-6", "text-center"], "layout": {"width": "100%", "height": "auto", "padding": "1.5rem", "display": "block"}, "elements": {"text": {"tag": "p", "classes": ["text-sm", "text-gray-500"]}, "branding": {"tag": "span", "classes": ["font-medium"]}}}}, "themeConfigurations": {"satisfaction-analysis": {"name": "Satisfaction Analysis", "primaryColor": "#10b981", "secondaryColor": "#059669", "accentColor": "#34d399", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}, "performance-dashboard": {"name": "Performance Dashboard", "primaryColor": "#3b82f6", "secondaryColor": "#1e40af", "accentColor": "#60a5fa", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}, "action-planning": {"name": "Action Planning", "primaryColor": "#f59e0b", "secondaryColor": "#d97706", "accentColor": "#fbbf24", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}, "channel-effectiveness": {"name": "Channel Effectiveness", "primaryColor": "#8b5cf6", "secondaryColor": "#7c3aed", "accentColor": "#a78bfa", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}, "ux-analysis": {"name": "UX Analysis", "primaryColor": "#ef4444", "secondaryColor": "#dc2626", "accentColor": "#f87171", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}, "product-analysis": {"name": "Product Analysis", "primaryColor": "#06b6d4", "secondaryColor": "#0891b2", "accentColor": "#22d3ee", "textColor": "#1f2937", "backgroundColor": "#ffffff", "gradientDirection": "135deg"}}, "responsiveBreakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1280px"}, "printStyles": {"enabled": true, "colorAdjust": "exact", "pageSize": "A4", "margins": "1in"}}