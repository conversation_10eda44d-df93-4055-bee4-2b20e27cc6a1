<!-- Guide Purpose: Coding patterns, naming conventions, error handling, and validation for multi-storage implementation -->
<!-- Target Audience: Developers implementing storage services and data access layers -->

# Implementation Standards Guide

## Overview

This guide establishes coding standards, naming conventions, error handling patterns, and validation rules for implementing the multi-storage architecture. Following these standards ensures consistency, maintainability, and reliability across all storage systems.

## Naming Conventions

### Document ID Patterns

#### Cosmos DB Document IDs
```csharp
// Pattern: rpt_{reportId}_v_{versionId}
public static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
{
    var reportIdClean = reportId.ToString("N");  // Remove hyphens
    var versionIdClean = versionId.ToString("N");
    return $"rpt_{reportIdClean}_v_{versionIdClean}";
}

// Examples:
// "rpt_550e8400e29b41d4a716************_v_c8bc5efcccbd42c0a7aa019767f8b83d"
// "rpt_183b6723a3334e03b22770bd20a432aa_v_d4e5f6677889012345678901234def67"

// Draft documents
public static string GenerateDraftDocumentId(Guid reportId)
{
    var reportIdClean = reportId.ToString("N");
    return $"rpt_{reportIdClean}_draft";
}
```

#### Blob Storage Path Patterns
```csharp
// Pattern: tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/{type}
public static class BlobPathGenerator
{
    public static string GenerateComponentsPath(string tenantId, Guid reportId, int versionNumber)
    {
        return $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/components.json";
    }
    
    public static string GenerateStylesPath(string tenantId, Guid reportId, int versionNumber)
    {
        return $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/styles.json";
    }
    
    public static string GenerateAssetsPath(string tenantId, Guid reportId, int versionNumber, string fileName)
    {
        return $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/assets/{fileName}";
    }
    
    public static string GenerateExportsPath(string tenantId, Guid reportId, string format, string fileName)
    {
        return $"tenants/{tenantId}/reports/{reportId}/exports/{format}/{fileName}";
    }
}

// Examples:
// "tenants/941fdfc2-c519-48ec-96af-6f893aca18ad/reports/550e8400-e29b-41d4-a716-************/versions/v1/components.json"
// "tenants/941fdfc2-c519-48ec-96af-6f893aca18ad/reports/550e8400-e29b-41d4-a716-************/versions/v1/styles.json"
```

### Service Interface Naming

```csharp
// Storage service interfaces
public interface ICosmosDbReportService
public interface IBlobStorageReportService
public interface ISqlReportRepository

// Multi-storage orchestration
public interface IMultiStorageReportService
public interface IReportStorageCoordinator

// Validation services
public interface IReportDataValidator
public interface ICosmosDocumentValidator
public interface IBlobContentValidator
```

### Configuration Section Names

```json
{
  "CosmosDb": {
    "ConnectionString": "...",
    "DatabaseName": "CSHeroReports",
    "ContainerName": "Reports"
  },
  "BlobStorage": {
    "ConnectionString": "...",
    "ContainerName": "report-components",
    "ReportDataContainer": "report-data"
  },
  "MultiStorage": {
    "DefaultStrategy": "Hybrid",
    "EnableMigration": true,
    "BatchSize": 50
  }
}
```

## Coding Patterns

### 1. Repository Pattern Implementation

#### Base Repository Interface
```csharp
public interface IStorageRepository<T, TKey>
{
    Task<T> GetByIdAsync(TKey id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<TKey> CreateAsync(T entity, CancellationToken cancellationToken = default);
    Task UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(TKey id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(TKey id, CancellationToken cancellationToken = default);
}
```

#### Cosmos DB Repository Implementation
```csharp
public class CosmosDbReportRepository : IStorageRepository<VersionedReportDataDocument, string>
{
    private readonly Container _container;
    private readonly ILogger<CosmosDbReportRepository> _logger;
    private readonly ICurrentTenantService _currentTenant;

    public CosmosDbReportRepository(
        CosmosClient cosmosClient,
        IConfiguration configuration,
        ILogger<CosmosDbReportRepository> logger,
        ICurrentTenantService currentTenant)
    {
        var databaseName = configuration["CosmosDb:DatabaseName"];
        var containerName = configuration["CosmosDb:ContainerName"];
        _container = cosmosClient.GetContainer(databaseName, containerName);
        _logger = logger;
        _currentTenant = currentTenant;
    }

    public async Task<VersionedReportDataDocument> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenantId = _currentTenant.TenantId?.ToString() 
                ?? throw new InvalidOperationException("Tenant context required");

            var response = await _container.ReadItemAsync<VersionedReportDataDocument>(
                id, 
                new PartitionKey(tenantId),
                cancellationToken: cancellationToken);

            _logger.LogDebug("Retrieved document {DocumentId} for tenant {TenantId}", id, tenantId);
            return response.Resource;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            _logger.LogWarning("Document {DocumentId} not found", id);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document {DocumentId}", id);
            throw;
        }
    }

    public async Task<string> CreateAsync(VersionedReportDataDocument entity, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate entity before creation
            ValidateEntity(entity);

            var response = await _container.CreateItemAsync(
                entity,
                new PartitionKey(entity.TenantId),
                cancellationToken: cancellationToken);

            _logger.LogInformation("Created document {DocumentId} for tenant {TenantId}", 
                entity.Id, entity.TenantId);

            return response.Resource.Id;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.Conflict)
        {
            _logger.LogWarning("Document {DocumentId} already exists", entity.Id);
            throw new InvalidOperationException($"Document {entity.Id} already exists");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating document {DocumentId}", entity.Id);
            throw;
        }
    }

    private void ValidateEntity(VersionedReportDataDocument entity)
    {
        if (string.IsNullOrEmpty(entity.Id))
            throw new ArgumentException("Document ID is required");

        if (string.IsNullOrEmpty(entity.TenantId))
            throw new ArgumentException("TenantId is required");

        if (entity.ReportId == Guid.Empty)
            throw new ArgumentException("ReportId is required");

        if (entity.VersionId == Guid.Empty)
            throw new ArgumentException("VersionId is required");
    }
}
```

#### Blob Storage Repository Implementation
```csharp
public class BlobStorageReportRepository : IStorageRepository<ComponentsBlob, string>
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    private readonly ILogger<BlobStorageReportRepository> _logger;
    private readonly ICurrentTenantService _currentTenant;

    public BlobStorageReportRepository(
        BlobServiceClient blobServiceClient,
        IConfiguration configuration,
        ILogger<BlobStorageReportRepository> logger,
        ICurrentTenantService currentTenant)
    {
        _blobServiceClient = blobServiceClient;
        _containerName = configuration["BlobStorage:ContainerName"];
        _logger = logger;
        _currentTenant = currentTenant;
    }

    public async Task<ComponentsBlob> GetByIdAsync(string blobPath, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate tenant access
            ValidateTenantAccess(blobPath);

            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(blobPath);

            if (!await blobClient.ExistsAsync(cancellationToken))
            {
                _logger.LogWarning("Blob {BlobPath} not found", blobPath);
                return null;
            }

            var response = await blobClient.DownloadContentAsync(cancellationToken);
            var content = response.Value.Content.ToString();
            var blob = JsonSerializer.Deserialize<ComponentsBlob>(content);

            _logger.LogDebug("Retrieved blob {BlobPath}", blobPath);
            return blob;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving blob {BlobPath}", blobPath);
            throw;
        }
    }

    public async Task<string> CreateAsync(ComponentsBlob entity, CancellationToken cancellationToken = default)
    {
        try
        {
            // Generate blob path
            var blobPath = BlobPathGenerator.GenerateComponentsPath(
                entity.TenantId, 
                entity.ReportId, 
                entity.VersionNumber);

            // Validate entity
            ValidateEntity(entity);

            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(blobPath);

            // Serialize and upload
            var content = JsonSerializer.Serialize(entity, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await blobClient.UploadAsync(
                BinaryData.FromString(content),
                overwrite: false,
                cancellationToken: cancellationToken);

            // Set metadata
            await blobClient.SetMetadataAsync(new Dictionary<string, string>
            {
                ["reportId"] = entity.ReportId.ToString(),
                ["versionId"] = entity.VersionId.ToString(),
                ["tenantId"] = entity.TenantId,
                ["contentType"] = "application/json",
                ["createdAt"] = DateTime.UtcNow.ToString("O")
            }, cancellationToken: cancellationToken);

            _logger.LogInformation("Created blob {BlobPath}", blobPath);
            return blobPath;
        }
        catch (RequestFailedException ex) when (ex.Status == 409)
        {
            _logger.LogWarning("Blob already exists for report {ReportId} version {VersionNumber}", 
                entity.ReportId, entity.VersionNumber);
            throw new InvalidOperationException("Blob already exists");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating blob for report {ReportId}", entity.ReportId);
            throw;
        }
    }

    private void ValidateTenantAccess(string blobPath)
    {
        var tenantId = _currentTenant.TenantId?.ToString();
        if (string.IsNullOrEmpty(tenantId))
            throw new UnauthorizedAccessException("Tenant context required");

        if (!blobPath.StartsWith($"tenants/{tenantId}/"))
            throw new UnauthorizedAccessException("Cross-tenant access denied");
    }

    private void ValidateEntity(ComponentsBlob entity)
    {
        if (string.IsNullOrEmpty(entity.TenantId))
            throw new ArgumentException("TenantId is required");

        if (entity.ReportId == Guid.Empty)
            throw new ArgumentException("ReportId is required");

        if (entity.VersionId == Guid.Empty)
            throw new ArgumentException("VersionId is required");

        if (entity.VersionNumber <= 0)
            throw new ArgumentException("VersionNumber must be positive");
    }
}
```

### 2. Multi-Storage Orchestration Pattern

```csharp
public class MultiStorageReportService : IMultiStorageReportService
{
    private readonly IApplicationDbContext _sqlContext;
    private readonly ICosmosDbReportService _cosmosService;
    private readonly IBlobStorageReportService _blobService;
    private readonly ILogger<MultiStorageReportService> _logger;
    private readonly IMapper _mapper;

    public async Task<CompleteReportDto> GetCompleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        using var activity = Activity.StartActivity("GetCompleteReport");
        activity?.SetTag("report.id", reportId.ToString());

        try
        {
            // 1. Get metadata from SQL (always required)
            var metadataTask = GetReportMetadataAsync(reportId, cancellationToken);
            
            // 2. Start parallel external storage operations
            var report = await metadataTask;
            if (report == null)
            {
                activity?.SetTag("result", "not_found");
                return null;
            }

            var tasks = new List<Task>();
            VersionedReportDataDocument documentData = null;
            ComponentsBlob componentsData = null;

            // 3. Fetch document data if available
            if (!string.IsNullOrEmpty(report.DataDocumentId))
            {
                tasks.Add(Task.Run(async () =>
                {
                    documentData = await _cosmosService.GetDocumentAsync(report.DataDocumentId, cancellationToken);
                    activity?.SetTag("cosmos.success", documentData != null);
                }));
            }

            // 4. Fetch components if available
            if (!string.IsNullOrEmpty(report.ComponentsBlobId))
            {
                tasks.Add(Task.Run(async () =>
                {
                    componentsData = await _blobService.GetComponentsAsync(report.ComponentsBlobId, cancellationToken);
                    activity?.SetTag("blob.success", componentsData != null);
                }));
            }

            // 5. Wait for all external operations
            if (tasks.Any())
            {
                await Task.WhenAll(tasks);
            }

            // 6. Combine results
            var result = new CompleteReportDto
            {
                Metadata = _mapper.Map<ReportDto>(report),
                DocumentData = documentData,
                Components = componentsData,
                HasExternalData = documentData != null || componentsData != null
            };

            activity?.SetTag("result", "success");
            return result;
        }
        catch (Exception ex)
        {
            activity?.SetTag("error", true);
            activity?.SetTag("error.message", ex.Message);
            _logger.LogError(ex, "Error getting complete report {ReportId}", reportId);
            throw;
        }
    }

    private async Task<Report> GetReportMetadataAsync(Guid reportId, CancellationToken cancellationToken)
    {
        return await _sqlContext.Reports
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .Include(r => r.Client)
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
    }
}
```

## Error Handling Patterns

### 1. Storage-Specific Exception Handling

```csharp
public class StorageExceptionHandler
{
    private readonly ILogger<StorageExceptionHandler> _logger;

    public async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        string storageType,
        int maxRetries = 3)
    {
        var attempt = 0;
        while (attempt <= maxRetries)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (ShouldRetry(ex, attempt, maxRetries))
            {
                attempt++;
                var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt)); // Exponential backoff
                
                _logger.LogWarning(ex, 
                    "Operation {OperationName} failed on {StorageType} (attempt {Attempt}/{MaxRetries}). Retrying in {Delay}ms",
                    operationName, storageType, attempt, maxRetries + 1, delay.TotalMilliseconds);
                
                await Task.Delay(delay);
            }
        }

        // This should never be reached due to the exception handling above
        throw new InvalidOperationException("Retry logic failed");
    }

    private bool ShouldRetry(Exception ex, int attempt, int maxRetries)
    {
        if (attempt >= maxRetries) return false;

        return ex switch
        {
            // Cosmos DB transient errors
            CosmosException cosmos when cosmos.StatusCode == HttpStatusCode.TooManyRequests => true,
            CosmosException cosmos when cosmos.StatusCode == HttpStatusCode.ServiceUnavailable => true,
            CosmosException cosmos when cosmos.StatusCode == HttpStatusCode.RequestTimeout => true,
            
            // Blob Storage transient errors
            RequestFailedException blob when blob.Status == 429 => true, // Throttling
            RequestFailedException blob when blob.Status >= 500 => true, // Server errors
            
            // SQL Server transient errors
            SqlException sql when sql.IsTransient => true,
            
            // Network errors
            HttpRequestException => true,
            TaskCanceledException => true,
            
            _ => false
        };
    }
}
```

### 2. Custom Exception Types

```csharp
public class StorageException : Exception
{
    public string StorageType { get; }
    public string Operation { get; }

    public StorageException(string storageType, string operation, string message) 
        : base(message)
    {
        StorageType = storageType;
        Operation = operation;
    }

    public StorageException(string storageType, string operation, string message, Exception innerException) 
        : base(message, innerException)
    {
        StorageType = storageType;
        Operation = operation;
    }
}

public class CosmosDbException : StorageException
{
    public HttpStatusCode StatusCode { get; }
    public double RequestCharge { get; }

    public CosmosDbException(string operation, HttpStatusCode statusCode, double requestCharge, string message) 
        : base("CosmosDB", operation, message)
    {
        StatusCode = statusCode;
        RequestCharge = requestCharge;
    }
}

public class BlobStorageException : StorageException
{
    public int StatusCode { get; }
    public string BlobPath { get; }

    public BlobStorageException(string operation, int statusCode, string blobPath, string message) 
        : base("BlobStorage", operation, message)
    {
        StatusCode = statusCode;
        BlobPath = blobPath;
    }
}

public class MultiStorageException : Exception
{
    public List<StorageException> StorageErrors { get; }

    public MultiStorageException(string message, List<StorageException> storageErrors) 
        : base(message)
    {
        StorageErrors = storageErrors ?? new List<StorageException>();
    }
}
```

### 3. Compensation Pattern for Failed Operations

```csharp
public class CompensationHandler
{
    private readonly ILogger<CompensationHandler> _logger;

    public async Task<CompensationResult> CompensateFailedCreationAsync(
        CreateReportRequest request,
        List<StorageException> failures)
    {
        var compensationTasks = new List<Task<bool>>();
        var compensationResults = new List<string>();

        foreach (var failure in failures)
        {
            switch (failure.StorageType)
            {
                case "SQL":
                    // SQL creation failed - nothing to compensate
                    break;
                    
                case "CosmosDB":
                    // Cosmos document was created but later operations failed
                    if (failure.Operation == "Create" && !string.IsNullOrEmpty(request.DocumentId))
                    {
                        compensationTasks.Add(CompensateCosmosCreationAsync(request.DocumentId));
                        compensationResults.Add($"Scheduled Cosmos document cleanup: {request.DocumentId}");
                    }
                    break;
                    
                case "BlobStorage":
                    // Blob was created but later operations failed
                    if (failure.Operation == "Create" && !string.IsNullOrEmpty(request.BlobPath))
                    {
                        compensationTasks.Add(CompensateBlobCreationAsync(request.BlobPath));
                        compensationResults.Add($"Scheduled blob cleanup: {request.BlobPath}");
                    }
                    break;
            }
        }

        if (compensationTasks.Any())
        {
            var results = await Task.WhenAll(compensationTasks);
            var successCount = results.Count(r => r);
            
            _logger.LogInformation(
                "Compensation completed: {SuccessCount}/{TotalCount} operations succeeded",
                successCount, results.Length);
        }

        return new CompensationResult
        {
            CompensationActions = compensationResults,
            Success = true
        };
    }

    private async Task<bool> CompensateCosmosCreationAsync(string documentId)
    {
        try
        {
            // Delete the created document
            await _cosmosService.DeleteDocumentAsync(documentId);
            _logger.LogInformation("Compensated Cosmos document creation: {DocumentId}", documentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to compensate Cosmos document creation: {DocumentId}", documentId);
            return false;
        }
    }

    private async Task<bool> CompensateBlobCreationAsync(string blobPath)
    {
        try
        {
            // Delete the created blob
            await _blobService.DeleteBlobAsync(blobPath);
            _logger.LogInformation("Compensated blob creation: {BlobPath}", blobPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to compensate blob creation: {BlobPath}", blobPath);
            return false;
        }
    }
}
```

## Validation Patterns

### 1. Document Validation

```csharp
public class CosmosDocumentValidator : ICosmosDocumentValidator
{
    private readonly ILogger<CosmosDocumentValidator> _logger;

    public ValidationResult ValidateDocument(VersionedReportDataDocument document)
    {
        var result = new ValidationResult();

        // Required field validation
        ValidateRequiredFields(document, result);
        
        // Business rule validation
        ValidateBusinessRules(document, result);
        
        // Size validation
        ValidateDocumentSize(document, result);
        
        // Content validation
        ValidateContent(document, result);

        return result;
    }

    private void ValidateRequiredFields(VersionedReportDataDocument document, ValidationResult result)
    {
        if (string.IsNullOrEmpty(document.Id))
            result.AddError("Document.Id is required");

        if (string.IsNullOrEmpty(document.TenantId))
            result.AddError("Document.TenantId is required");

        if (document.ReportId == Guid.Empty)
            result.AddError("Document.ReportId is required");

        if (document.VersionId == Guid.Empty)
            result.AddError("Document.VersionId is required");

        if (document.VersionNumber <= 0)
            result.AddError("Document.VersionNumber must be positive");
    }

    private void ValidateBusinessRules(VersionedReportDataDocument document, ValidationResult result)
    {
        // Validate document ID format
        var expectedId = GenerateVersionedDocumentId(document.ReportId, document.VersionId);
        if (document.Id != expectedId)
            result.AddError($"Document.Id format is invalid. Expected: {expectedId}, Actual: {document.Id}");

        // Validate tenant ID format
        if (!Guid.TryParse(document.TenantId, out _))
            result.AddError("Document.TenantId must be a valid GUID");

        // Validate sections
        if (document.Sections != null)
        {
            for (int i = 0; i < document.Sections.Count; i++)
            {
                ValidateSection(document.Sections[i], i, result);
            }
        }
    }

    private void ValidateDocumentSize(VersionedReportDataDocument document, ValidationResult result)
    {
        try
        {
            var json = JsonSerializer.Serialize(document);
            var sizeInBytes = Encoding.UTF8.GetByteCount(json);
            
            // Cosmos DB has a 2MB limit per document
            if (sizeInBytes > 2 * 1024 * 1024)
            {
                result.AddError($"Document size ({sizeInBytes} bytes) exceeds Cosmos DB limit (2MB)");
            }
            else if (sizeInBytes > 1.5 * 1024 * 1024)
            {
                result.AddWarning($"Document size ({sizeInBytes} bytes) is approaching Cosmos DB limit");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"Failed to calculate document size: {ex.Message}");
        }
    }

    private void ValidateSection(ReportDataSection section, int index, ValidationResult result)
    {
        var prefix = $"Sections[{index}]";

        if (section.Id == Guid.Empty)
            result.AddError($"{prefix}.Id is required");

        if (string.IsNullOrEmpty(section.Name))
            result.AddError($"{prefix}.Name is required");

        if (string.IsNullOrEmpty(section.Title))
            result.AddError($"{prefix}.Title is required");

        if (section.DisplayOrder < 0)
            result.AddError($"{prefix}.DisplayOrder must be non-negative");

        // Validate fields
        if (section.Fields != null)
        {
            for (int i = 0; i < section.Fields.Count; i++)
            {
                ValidateField(section.Fields[i], $"{prefix}.Fields[{i}]", result);
            }
        }
    }

    private void ValidateField(ReportDataField field, string prefix, ValidationResult result)
    {
        if (field.Id == Guid.Empty)
            result.AddError($"{prefix}.Id is required");

        if (string.IsNullOrEmpty(field.Name))
            result.AddError($"{prefix}.Name is required");

        if (string.IsNullOrEmpty(field.FieldType))
            result.AddError($"{prefix}.FieldType is required");

        if (field.DisplayOrder < 0)
            result.AddError($"{prefix}.DisplayOrder must be non-negative");

        // Validate field type-specific rules
        ValidateFieldTypeSpecificRules(field, prefix, result);
    }

    private void ValidateFieldTypeSpecificRules(ReportDataField field, string prefix, ValidationResult result)
    {
        switch (field.FieldType.ToLower())
        {
            case "number":
                if (!string.IsNullOrEmpty(field.DefaultValue) && !double.TryParse(field.DefaultValue, out _))
                    result.AddError($"{prefix}.DefaultValue must be a valid number for number fields");
                break;

            case "date":
                if (!string.IsNullOrEmpty(field.DefaultValue) && !DateTime.TryParse(field.DefaultValue, out _))
                    result.AddError($"{prefix}.DefaultValue must be a valid date for date fields");
                break;

            case "boolean":
                if (!string.IsNullOrEmpty(field.DefaultValue) && !bool.TryParse(field.DefaultValue, out _))
                    result.AddError($"{prefix}.DefaultValue must be a valid boolean for boolean fields");
                break;

            case "json":
                if (!string.IsNullOrEmpty(field.DefaultValue))
                {
                    try
                    {
                        JsonDocument.Parse(field.DefaultValue);
                    }
                    catch
                    {
                        result.AddError($"{prefix}.DefaultValue must be valid JSON for json fields");
                    }
                }
                break;
        }
    }
}

public class ValidationResult
{
    public List<string> Errors { get; } = new();
    public List<string> Warnings { get; } = new();
    public bool IsValid => !Errors.Any();

    public void AddError(string error) => Errors.Add(error);
    public void AddWarning(string warning) => Warnings.Add(warning);
}
```

### 2. Blob Content Validation

```csharp
public class BlobContentValidator : IBlobContentValidator
{
    public ValidationResult ValidateComponentsBlob(ComponentsBlob blob)
    {
        var result = new ValidationResult();

        // Basic validation
        if (blob.ReportId == Guid.Empty)
            result.AddError("ReportId is required");

        if (blob.VersionId == Guid.Empty)
            result.AddError("VersionId is required");

        if (string.IsNullOrEmpty(blob.TenantId))
            result.AddError("TenantId is required");

        // Component validation
        if (blob.Components != null)
        {
            for (int i = 0; i < blob.Components.Count; i++)
            {
                ValidateComponent(blob.Components[i], i, result);
            }
        }

        return result;
    }

    private void ValidateComponent(ComponentDefinition component, int index, ValidationResult result)
    {
        var prefix = $"Components[{index}]";

        if (string.IsNullOrEmpty(component.Name))
            result.AddError($"{prefix}.Name is required");

        if (string.IsNullOrEmpty(component.ComponentCode))
            result.AddError($"{prefix}.ComponentCode is required");

        // Validate React component syntax
        if (!string.IsNullOrEmpty(component.ComponentCode))
        {
            ValidateReactComponentSyntax(component.ComponentCode, prefix, result);
        }

        // Validate imports
        if (component.Imports != null)
        {
            foreach (var import in component.Imports)
            {
                if (string.IsNullOrWhiteSpace(import))
                    result.AddWarning($"{prefix} contains empty import");
            }
        }
    }

    private void ValidateReactComponentSyntax(string componentCode, string prefix, ValidationResult result)
    {
        // Basic syntax checks
        if (!componentCode.Contains("export"))
            result.AddError($"{prefix}.ComponentCode must contain an export statement");

        if (!componentCode.Contains("React"))
            result.AddWarning($"{prefix}.ComponentCode should import React");

        // Check for balanced brackets
        var openBraces = componentCode.Count(c => c == '{');
        var closeBraces = componentCode.Count(c => c == '}');
        if (openBraces != closeBraces)
            result.AddError($"{prefix}.ComponentCode has unbalanced braces");

        // Check for JSX return
        if (!componentCode.Contains("return") || !componentCode.Contains("<"))
            result.AddWarning($"{prefix}.ComponentCode should return JSX");
    }
}
```

## Data Integrity Patterns

### 1. Cross-Storage Consistency Checks

```csharp
public class DataIntegrityService : IDataIntegrityService
{
    private readonly IApplicationDbContext _sqlContext;
    private readonly ICosmosDbReportService _cosmosService;
    private readonly IBlobStorageReportService _blobService;
    private readonly ILogger<DataIntegrityService> _logger;

    public async Task<IntegrityCheckResult> ValidateReportIntegrityAsync(Guid reportId)
    {
        var result = new IntegrityCheckResult { ReportId = reportId };

        try
        {
            // 1. Get SQL metadata
            var report = await _sqlContext.Reports
                .Include(r => r.Versions)
                .FirstOrDefaultAsync(r => r.Id == reportId);

            if (report == null)
            {
                result.AddError("Report not found in SQL database");
                return result;
            }

            // 2. Validate current version references
            var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
            if (currentVersion != null)
            {
                await ValidateVersionIntegrityAsync(currentVersion, result);
            }

            // 3. Validate all versions
            foreach (var version in report.Versions)
            {
                await ValidateVersionIntegrityAsync(version, result);
            }

            result.IsValid = !result.Errors.Any();
        }
        catch (Exception ex)
        {
            result.AddError($"Integrity check failed: {ex.Message}");
            _logger.LogError(ex, "Error during integrity check for report {ReportId}", reportId);
        }

        return result;
    }

    private async Task ValidateVersionIntegrityAsync(ReportVersion version, IntegrityCheckResult result)
    {
        // Check Cosmos DB reference
        if (!string.IsNullOrEmpty(version.DataDocumentId))
        {
            try
            {
                var document = await _cosmosService.GetDocumentAsync(version.DataDocumentId);
                if (document == null)
                {
                    result.AddError($"Version {version.VersionNumber}: Cosmos document {version.DataDocumentId} not found");
                }
                else
                {
                    // Validate document consistency
                    if (document.ReportId != version.ReportId)
                        result.AddError($"Version {version.VersionNumber}: Document ReportId mismatch");
                    
                    if (document.VersionId != version.Id)
                        result.AddError($"Version {version.VersionNumber}: Document VersionId mismatch");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Version {version.VersionNumber}: Error accessing Cosmos document: {ex.Message}");
            }
        }

        // Check Blob Storage reference
        if (!string.IsNullOrEmpty(version.ComponentsBlobId))
        {
            try
            {
                var exists = await _blobService.ExistsAsync(version.ComponentsBlobId);
                if (!exists)
                {
                    result.AddError($"Version {version.VersionNumber}: Blob {version.ComponentsBlobId} not found");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Version {version.VersionNumber}: Error accessing blob: {ex.Message}");
            }
        }
    }
}

public class IntegrityCheckResult
{
    public Guid ReportId { get; set; }
    public List<string> Errors { get; } = new();
    public List<string> Warnings { get; } = new();
    public bool IsValid { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    public void AddError(string error) => Errors.Add(error);
    public void AddWarning(string warning) => Warnings.Add(warning);
}
```

### 2. Orphaned Data Cleanup

```csharp
public class OrphanedDataCleanupService : IOrphanedDataCleanupService
{
    public async Task<CleanupResult> CleanupOrphanedDataAsync(string tenantId)
    {
        var result = new CleanupResult();

        // 1. Find orphaned Cosmos documents
        var orphanedDocuments = await FindOrphanedCosmosDocumentsAsync(tenantId);
        foreach (var documentId in orphanedDocuments)
        {
            try
            {
                await _cosmosService.DeleteDocumentAsync(documentId);
                result.CosmosDocumentsDeleted++;
            }
            catch (Exception ex)
            {
                result.AddError($"Failed to delete Cosmos document {documentId}: {ex.Message}");
            }
        }

        // 2. Find orphaned blobs
        var orphanedBlobs = await FindOrphanedBlobsAsync(tenantId);
        foreach (var blobPath in orphanedBlobs)
        {
            try
            {
                await _blobService.DeleteBlobAsync(blobPath);
                result.BlobsDeleted++;
            }
            catch (Exception ex)
            {
                result.AddError($"Failed to delete blob {blobPath}: {ex.Message}");
            }
        }

        return result;
    }

    private async Task<List<string>> FindOrphanedCosmosDocumentsAsync(string tenantId)
    {
        // Get all document IDs from Cosmos for this tenant
        var cosmosDocuments = await _cosmosService.GetAllDocumentIdsAsync(tenantId);
        
        // Get all referenced document IDs from SQL
        var referencedDocuments = await _sqlContext.ReportVersions
            .Where(rv => rv.Report.TenantId.ToString() == tenantId)
            .Where(rv => !string.IsNullOrEmpty(rv.DataDocumentId))
            .Select(rv => rv.DataDocumentId)
            .ToListAsync();

        // Find orphaned documents
        return cosmosDocuments.Except(referencedDocuments).ToList();
    }
}
```

## Performance Optimization Standards

### 1. Caching Strategy

```csharp
public class CachingService : ICachingService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<CachingService> _logger;

    public async Task<T> GetOrSetAsync<T>(
        string key,
        Func<Task<T>> getItem,
        TimeSpan? absoluteExpiration = null,
        TimeSpan? slidingExpiration = null) where T : class
    {
        // Try memory cache first (L1)
        if (_memoryCache.TryGetValue(key, out T cachedItem))
        {
            _logger.LogDebug("Cache hit (L1): {Key}", key);
            return cachedItem;
        }

        // Try distributed cache (L2)
        var distributedValue = await _distributedCache.GetStringAsync(key);
        if (!string.IsNullOrEmpty(distributedValue))
        {
            var item = JsonSerializer.Deserialize<T>(distributedValue);
            
            // Store in memory cache for faster access
            _memoryCache.Set(key, item, TimeSpan.FromMinutes(5));
            
            _logger.LogDebug("Cache hit (L2): {Key}", key);
            return item;
        }

        // Cache miss - get from source
        _logger.LogDebug("Cache miss: {Key}", key);
        var result = await getItem();

        if (result != null)
        {
            // Store in both caches
            var serialized = JsonSerializer.Serialize(result);
            
            var distributedOptions = new DistributedCacheEntryOptions();
            if (absoluteExpiration.HasValue)
                distributedOptions.SetAbsoluteExpiration(absoluteExpiration.Value);
            if (slidingExpiration.HasValue)
                distributedOptions.SetSlidingExpiration(slidingExpiration.Value);

            await _distributedCache.SetStringAsync(key, serialized, distributedOptions);
            
            var memoryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                SlidingExpiration = TimeSpan.FromMinutes(2)
            };
            _memoryCache.Set(key, result, memoryOptions);
        }

        return result;
    }

    public async Task InvalidateAsync(string key)
    {
        _memoryCache.Remove(key);
        await _distributedCache.RemoveAsync(key);
        _logger.LogDebug("Cache invalidated: {Key}", key);
    }

    public async Task InvalidatePatternAsync(string pattern)
    {
        // For distributed cache, this would require a more sophisticated implementation
        // For now, we'll just log the pattern for manual cleanup
        _logger.LogWarning("Pattern invalidation requested but not implemented: {Pattern}", pattern);
    }
}
```

### 2. Batch Operations

```csharp
public class BatchOperationService : IBatchOperationService
{
    public async Task<BatchResult<T>> ExecuteBatchAsync<T>(
        IEnumerable<T> items,
        Func<T, Task> operation,
        int batchSize = 10,
        int maxConcurrency = 5)
    {
        var result = new BatchResult<T>();
        var semaphore = new SemaphoreSlim(maxConcurrency);
        var batches = items.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var tasks = batch.Select(async item =>
            {
                await semaphore.WaitAsync();
                try
                {
                    await operation(item);
                    result.SuccessfulItems.Add(item);
                }
                catch (Exception ex)
                {
                    result.FailedItems.Add(new BatchFailure<T> { Item = item, Error = ex.Message });
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
        }

        return result;
    }
}

public class BatchResult<T>
{
    public List<T> SuccessfulItems { get; } = new();
    public List<BatchFailure<T>> FailedItems { get; } = new();
    public int TotalProcessed => SuccessfulItems.Count + FailedItems.Count;
    public double SuccessRate => TotalProcessed == 0 ? 0 : (double)SuccessfulItems.Count / TotalProcessed;
}

public class BatchFailure<T>
{
    public T Item { get; set; }
    public string Error { get; set; }
}
```

## Security Standards

### 1. Tenant Isolation Validation

```csharp
public class TenantSecurityService : ITenantSecurityService
{
    private readonly ICurrentTenantService _currentTenant;
    private readonly ILogger<TenantSecurityService> _logger;

    public void ValidateTenantAccess(string resourceTenantId, string operation)
    {
        var currentTenantId = _currentTenant.TenantId?.ToString();
        
        if (string.IsNullOrEmpty(currentTenantId))
        {
            _logger.LogWarning("Attempted {Operation} without tenant context", operation);
            throw new UnauthorizedAccessException("Tenant context required");
        }

        if (currentTenantId != resourceTenantId)
        {
            _logger.LogWarning(
                "Cross-tenant access attempt: Current={CurrentTenant}, Resource={ResourceTenant}, Operation={Operation}",
                currentTenantId, resourceTenantId, operation);
            throw new UnauthorizedAccessException("Cross-tenant access denied");
        }
    }

    public void ValidateBlobPath(string blobPath, string operation)
    {
        var currentTenantId = _currentTenant.TenantId?.ToString();
        
        if (string.IsNullOrEmpty(currentTenantId))
            throw new UnauthorizedAccessException("Tenant context required");

        var expectedPrefix = $"tenants/{currentTenantId}/";
        if (!blobPath.StartsWith(expectedPrefix))
        {
            _logger.LogWarning(
                "Invalid blob path access: Path={BlobPath}, Expected prefix={ExpectedPrefix}, Operation={Operation}",
                blobPath, expectedPrefix, operation);
            throw new UnauthorizedAccessException("Invalid blob path for current tenant");
        }
    }
}
```

### 2. Data Encryption Standards

```csharp
public class EncryptionService : IEncryptionService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EncryptionService> _logger;

    public string EncryptSensitiveData(string data)
    {
        if (string.IsNullOrEmpty(data))
            return data;

        try
        {
            // Use AES encryption for sensitive data
            var key = _configuration["Encryption:Key"];
            var iv = _configuration["Encryption:IV"];
            
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(key);
            aes.IV = Convert.FromBase64String(iv);

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);
            
            swEncrypt.Write(data);
            return Convert.ToBase64String(msEncrypt.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt sensitive data");
            throw;
        }
    }

    public string DecryptSensitiveData(string encryptedData)
    {
        if (string.IsNullOrEmpty(encryptedData))
            return encryptedData;

        try
        {
            var key = _configuration["Encryption:Key"];
            var iv = _configuration["Encryption:IV"];
            
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(key);
            aes.IV = Convert.FromBase64String(iv);

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(Convert.FromBase64String(encryptedData));
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);
            
            return srDecrypt.ReadToEnd();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt sensitive data");
            throw;
        }
    }
}
```

## Testing Standards

### 1. Integration Test Patterns

```csharp
[TestFixture]
public class MultiStorageIntegrationTests
{
    private IServiceProvider _serviceProvider;
    private IMultiStorageReportService _reportService;

    [SetUp]
    public async Task SetUp()
    {
        // Setup test services with real storage connections
        var services = new ServiceCollection();
        
        // Add test configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.Test.json")
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add storage services
        services.AddCosmosDbServices(configuration);
        services.AddBlobStorageServices(configuration);
        services.AddSqlServerServices(configuration);
        
        _serviceProvider = services.BuildServiceProvider();
        _reportService = _serviceProvider.GetRequiredService<IMultiStorageReportService>();
        
        // Clean up test data
        await CleanupTestDataAsync();
    }

    [Test]
    public async Task CreateReport_ShouldStoreDataInAllSystems()
    {
        // Arrange
        var request = new CreateReportRequest
        {
            Name = "Test Report",
            ClientId = Guid.NewGuid(),
            TenantId = Guid.NewGuid()
        };

        // Act
        var reportId = await _reportService.CreateReportAsync(request);

        // Assert
        var report = await _reportService.GetCompleteReportAsync(reportId);
        
        Assert.That(report, Is.Not.Null);
        Assert.That(report.Metadata.Name, Is.EqualTo(request.Name));
        Assert.That(report.HasExternalData, Is.True);
        Assert.That(report.DocumentData, Is.Not.Null);
        Assert.That(report.Components, Is.Not.Null);
    }

    private async Task CleanupTestDataAsync()
    {
        // Cleanup logic for test data
    }
}
```

This comprehensive implementation standards guide provides the foundation for consistent, secure, and maintainable multi-storage development.
