// CosmosDocumentModels.cs - Hybrid for CosmosSeeder and legacy code compatibility

using System.Text.Json.Serialization;
using System.Text.Json;
using Newtonsoft.Json;

namespace FY.WB.CSHero2.Infrastructure.Models;

/// <summary>
/// Canonical document model for CosmosDB storage - represents versioned report data
/// This is the standardized model used across the application for CosmosDB operations
/// </summary>
public class VersionedReportDataDocument
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("TenantId")]
    public string TenantId { get; set; } = string.Empty;

    [JsonPropertyName("reportId")]
    public Guid ReportId { get; set; }

    [JsonPropertyName("versionId")]
    public Guid VersionId { get; set; }

    [JsonPropertyName("versionNumber")]
    public int VersionNumber { get; set; }

    [JsonPropertyName("isDraft")]
    public bool IsDraft { get; set; }

    [JsonPropertyName("reportName")]
    public string ReportName { get; set; } = string.Empty;

    [JsonPropertyName("reportNumber")]
    public string ReportNumber { get; set; } = string.Empty;

    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("author")]
    public string Author { get; set; } = string.Empty;

    [JsonPropertyName("clientId")]
    public Guid ClientId { get; set; }

    [JsonPropertyName("clientName")]
    public string ClientName { get; set; } = string.Empty;

    [JsonPropertyName("sections")]
    public List<ReportDataSection> Sections { get; set; } = new();

    [JsonPropertyName("componentDataJson")]
    public JsonElement ComponentDataJson { get; set; } = default;

    [JsonPropertyName("jsonData")]
    public JsonElement JsonData { get; set; } = default;

    [JsonPropertyName("metadata")]
    public ReportDataMetadata Metadata { get; set; } = new();
}

public class ReportDataSection
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("order")]
    public int Order { get; set; }

    [JsonPropertyName("fields")]
    public List<ReportDataField> Fields { get; set; } = new();

    // Legacy/compatibility properties (not serialized to Cosmos)
    [System.Text.Json.Serialization.JsonIgnore]
    public string? SectionType
    {
        get => Type;
        set { Type = value ?? ""; }
    }

    [System.Text.Json.Serialization.JsonIgnore]
    public string? Name
    {
        get => Title;
        set { Title = value ?? ""; }
    }

    [System.Text.Json.Serialization.JsonIgnore]
    public string? Description { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public int DisplayOrder
    {
        get => Order;
        set { Order = value; }
    }
    [System.Text.Json.Serialization.JsonIgnore]
    public bool IsRequired { get; set; }
}

public class ReportDataField
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;

    [JsonPropertyName("order")]
    public int Order { get; set; }

    // Legacy/compatibility properties (not serialized to Cosmos)
    [System.Text.Json.Serialization.JsonIgnore]
    public string? FieldType
    {
        get => Type;
        set { Type = value ?? ""; }
    }
    [System.Text.Json.Serialization.JsonIgnore]
    public string? Label { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public string? DefaultValue
    {
        get => Content;
        set { Content = value ?? ""; }
    }
    [System.Text.Json.Serialization.JsonIgnore]
    public bool IsRequired { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public int DisplayOrder
    {
        get => Order;
        set { Order = value; }
    }
    [System.Text.Json.Serialization.JsonIgnore]
    public Dictionary<string, object>? ValidationRules { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public List<string>? Options { get; set; }
}

public class ReportDataMetadata
{
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    [JsonPropertyName("lastModifiedAt")]
    public DateTime LastModifiedAt { get; set; }

    [JsonPropertyName("lastModifiedBy")]
    public string LastModifiedBy { get; set; } = string.Empty;

    // Legacy/compatibility properties (not serialized to Cosmos)
    [System.Text.Json.Serialization.JsonIgnore]
    public DateTime UpdatedAt
    {
        get => LastModifiedAt;
        set { LastModifiedAt = value; }
    }
    [System.Text.Json.Serialization.JsonIgnore]
    public int SectionCount { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public int FieldCount { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public string? Version { get; set; }
    [System.Text.Json.Serialization.JsonIgnore]
    public List<string>? Tags { get; set; }
}