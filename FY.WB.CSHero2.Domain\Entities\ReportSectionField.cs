using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents a field within a report section (e.g., heading, body text, chart data)
    /// </summary>
    public class ReportSectionField : FullAuditedMultiTenantEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the parent section
        /// </summary>
        public Guid ReportSectionId { get; set; }

        /// <summary>
        /// Name of the field (e.g., "heading", "body", "chartType", "data")
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Type of the field (e.g., "string", "number", "date", "json", "boolean")
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display label for the field
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// Value of the field (stored as string, can be JSON for complex data)
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Content of the field (stored as string, can be JSON for complex data)
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Order of the field within the section (for sorting)
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether this field is required
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Additional metadata for the field (stored as JSON)
        /// </summary>
        public string Metadata { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the template field this was created from
        /// </summary>
        public Guid? TemplateSourceFieldId { get; set; }

        /// <summary>
        /// Whether this field has been modified from its template source
        /// </summary>
        public bool IsModifiedFromTemplate { get; set; } = false;

        // Navigation property
        /// <summary>
        /// Navigation property to the parent section
        /// </summary>
        public virtual ReportSection Section { get; set; } = null!;

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public ReportSectionField() : base() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public ReportSectionField(
            Guid id,
            Guid reportSectionId,
            string name,
            string type,
            string content,
            int order)
            : base(id)
        {
            ReportSectionId = reportSectionId;
            Name = name;
            Type = type;
            Content = content;
            Order = order;
        }

        /// <summary>
        /// Updates the field details
        /// </summary>
        public void UpdateDetails(string name, string type, string content, int order)
        {
            Name = name;
            Type = type;
            Content = content;
            Order = order;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        /// <summary>
        /// Updates the field content
        /// </summary>
        public void UpdateContent(string content)
        {
            Content = content;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        /// <summary>
        /// Updates the field order
        /// </summary>
        public void UpdateOrder(int order)
        {
            Order = order;
            // LastModificationTime and LastModifierId will be set by DbContext
        }
    }
}
