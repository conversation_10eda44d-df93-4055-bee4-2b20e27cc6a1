"use client";

import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Special handling for ActionQueueContext errors
    if (error.message.includes('ActionQueueContext') || error.message.includes('Invariant')) {
      console.warn('Next.js hydration error detected. This may resolve automatically.');
      // For hydration errors, we can attempt a single retry after a delay
      setTimeout(() => {
        if (this.state.hasError) {
          console.log('Attempting error recovery...');
          this.resetError();
        }
      }, 1000);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

function DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {
  const isHydrationError = error?.message.includes('ActionQueueContext') ||
                          error?.message.includes('Invariant') ||
                          error?.message.includes('hydration');

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto text-center p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {isHydrationError ? 'Loading Application...' : 'Something went wrong'}
          </h2>
          <p className="text-gray-600 mb-4">
            {isHydrationError
              ? 'The application is starting up. This should resolve automatically in a moment.'
              : 'An unexpected error occurred. Please try refreshing the page.'
            }
          </p>
        </div>
        <div className="space-y-2">
          <button
            onClick={resetError}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.reload()}
            className="w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            Refresh Page
          </button>
        </div>
        {process.env.NODE_ENV === 'development' && error && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500">Error Details</summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

export default ErrorBoundary;
