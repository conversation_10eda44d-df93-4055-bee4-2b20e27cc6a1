using FY.WB.CSHero2.Domain.Entities.Core;
using System.Text.Json;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents style configuration for a report
    /// Stores style selections and customizations for report rendering
    /// </summary>
    public class ReportStyle : AuditedEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the report this style belongs to
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Navigation property to the parent report
        /// </summary>
        public virtual Report Report { get; set; } = null!;

        /// <summary>
        /// Selected theme name (e.g., "modern", "classic", "minimal")
        /// </summary>
        public string Theme { get; set; } = "modern";

        /// <summary>
        /// Selected color scheme (e.g., "blue", "green", "corporate")
        /// </summary>
        public string ColorScheme { get; set; } = "blue";

        /// <summary>
        /// Selected typography style (e.g., "sans-serif", "serif", "modern")
        /// </summary>
        public string Typography { get; set; } = "sans-serif";

        /// <summary>
        /// Selected spacing configuration (e.g., "compact", "normal", "spacious")
        /// </summary>
        public string Spacing { get; set; } = "normal";

        /// <summary>
        /// JSON serialized layout options (margins, padding, grid settings, etc.)
        /// </summary>
        public string LayoutOptionsJson { get; set; } = "{}";

        /// <summary>
        /// JSON serialized typography options (font sizes, line heights, etc.)
        /// </summary>
        public string TypographyOptionsJson { get; set; } = "{}";

        /// <summary>
        /// JSON serialized structure options (section ordering, visibility, etc.)
        /// </summary>
        public string StructureOptionsJson { get; set; } = "{}";

        /// <summary>
        /// JSON serialized content options (text formatting, image settings, etc.)
        /// </summary>
        public string ContentOptionsJson { get; set; } = "{}";

        /// <summary>
        /// JSON serialized visual options (borders, shadows, effects, etc.)
        /// </summary>
        public string VisualOptionsJson { get; set; } = "{}";

        /// <summary>
        /// Gets the layout options as a dictionary
        /// </summary>
        public Dictionary<string, object> GetLayoutOptions()
        {
            if (string.IsNullOrEmpty(LayoutOptionsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(LayoutOptionsJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the layout options from a dictionary
        /// </summary>
        public void SetLayoutOptions(Dictionary<string, object> options)
        {
            LayoutOptionsJson = JsonSerializer.Serialize(options ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Gets the typography options as a dictionary
        /// </summary>
        public Dictionary<string, object> GetTypographyOptions()
        {
            if (string.IsNullOrEmpty(TypographyOptionsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(TypographyOptionsJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the typography options from a dictionary
        /// </summary>
        public void SetTypographyOptions(Dictionary<string, object> options)
        {
            TypographyOptionsJson = JsonSerializer.Serialize(options ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Gets the structure options as a dictionary
        /// </summary>
        public Dictionary<string, object> GetStructureOptions()
        {
            if (string.IsNullOrEmpty(StructureOptionsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(StructureOptionsJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the structure options from a dictionary
        /// </summary>
        public void SetStructureOptions(Dictionary<string, object> options)
        {
            StructureOptionsJson = JsonSerializer.Serialize(options ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Gets the content options as a dictionary
        /// </summary>
        public Dictionary<string, object> GetContentOptions()
        {
            if (string.IsNullOrEmpty(ContentOptionsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(ContentOptionsJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the content options from a dictionary
        /// </summary>
        public void SetContentOptions(Dictionary<string, object> options)
        {
            ContentOptionsJson = JsonSerializer.Serialize(options ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Gets the visual options as a dictionary
        /// </summary>
        public Dictionary<string, object> GetVisualOptions()
        {
            if (string.IsNullOrEmpty(VisualOptionsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(VisualOptionsJson) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sets the visual options from a dictionary
        /// </summary>
        public void SetVisualOptions(Dictionary<string, object> options)
        {
            VisualOptionsJson = JsonSerializer.Serialize(options ?? new Dictionary<string, object>());
        }

        /// <summary>
        /// Creates a copy of this style for a new report
        /// </summary>
        public ReportStyle CreateCopy(Guid newReportId)
        {
            return new ReportStyle
            {
                Id = Guid.NewGuid(),
                ReportId = newReportId,
                Theme = Theme,
                ColorScheme = ColorScheme,
                Typography = Typography,
                Spacing = Spacing,
                LayoutOptionsJson = LayoutOptionsJson,
                TypographyOptionsJson = TypographyOptionsJson,
                StructureOptionsJson = StructureOptionsJson,
                ContentOptionsJson = ContentOptionsJson,
                VisualOptionsJson = VisualOptionsJson,
                CreationTime = DateTime.UtcNow
            };
        }
    }
}
