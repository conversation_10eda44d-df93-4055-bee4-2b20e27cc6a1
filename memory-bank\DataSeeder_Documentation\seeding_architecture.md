# Data Seeding Architecture Guide

## Table of Contents
1. [System Goals & Objectives](#system-goals--objectives)
2. [Architecture Overview](#architecture-overview)
3. [Entity Relationship Mappings](#entity-relationship-mappings)
4. [Data Flow & Seeding Sequence](#data-flow--seeding-sequence)
5. [File Structure & Components](#file-structure--components)
6. [Data Categories & Coverage](#data-categories--coverage)
7. [Multi-Storage Integration](#multi-storage-integration)
8. [Maintenance Guidelines](#maintenance-guidelines)
9. [Performance Considerations](#performance-considerations)
10. [Troubleshooting Guide](#troubleshooting-guide)

## System Goals & Objectives

### Primary Purpose
The data seeding system provides comprehensive, realistic test data for development, testing, and demonstration environments. It supports the multi-storage architecture by coordinating data across SQL Server, Cosmos DB, and Azure Blob Storage.

### Data Requirements by Environment

#### Development Environment
- **Volume**: Moderate dataset for efficient development cycles
- **Variety**: Representative samples of all entity types
- **Relationships**: Complete foreign key coverage
- **Performance**: Fast seeding (<30 seconds)

#### Testing Environment
- **Volume**: Comprehensive dataset for thorough testing
- **Edge Cases**: Boundary conditions and error scenarios
- **Consistency**: Reliable, repeatable data states
- **Isolation**: Clean slate for each test run

#### Demo Environment
- **Quality**: Polished, realistic business data
- **Completeness**: Full feature coverage
- **Presentation**: Professional, coherent narratives
- **Stability**: Consistent, predictable outcomes

### Data Volume Targets

| Entity Type | Current | Target | Purpose |
|-------------|---------|--------|---------|
| Tenant Profiles | 4 | 4 | Multi-tenancy testing |
| Clients | 7 | 20+ | Customer diversity |
| Reports | 7 | 25+ | Comprehensive coverage |
| Report Sections | 5 | 150+ | Content variety |
| Report Fields | 9 | 500+ | Data type coverage |
| Templates | Variable | 10+ | Template-based reports |
| Users | Variable | 25+ | Role-based testing |

## Architecture Overview

### Multi-Storage Integration

The seeding system coordinates data across three storage systems, ensuring consistency and proper relationships:

```mermaid
graph TB
    subgraph "Data Seeding Orchestrator"
        DataSeeder["DataSeeder.cs"]
        Coordinator["Multi-Storage Coordinator"]
    end
    
    subgraph "SQL Server Seeding"
        SQLSeeder["SQL Entity Seeder"]
        Metadata["Report Metadata"]
        Relationships["Entity Relationships"]
        Users["User Management"]
    end
    
    subgraph "Cosmos DB Seeding"
        CosmosSeeder["Cosmos Document Seeder"]
        Documents["Report Data Documents"]
        Sections["Section/Field JSON"]
        Queries["Query Optimization"]
    end
    
    subgraph "Blob Storage Seeding"
        BlobSeeder["Blob Component Seeder"]
        Components["React Components"]
        Assets["Static Assets"]
        Metadata2["Component Metadata"]
    end
    
    subgraph "Storage Systems"
        SQL[(SQL Server)]
        Cosmos[(Cosmos DB)]
        Blob[(Blob Storage)]
    end
    
    DataSeeder --> Coordinator
    Coordinator --> SQLSeeder
    Coordinator --> CosmosSeeder
    Coordinator --> BlobSeeder
    
    SQLSeeder --> SQL
    CosmosSeeder --> Cosmos
    BlobSeeder --> Blob
    
    SQL -.->|ID References| Cosmos
    SQL -.->|Blob References| Blob
    Cosmos -.->|Component Links| Blob
```

### Seeding Coordination Strategy

1. **Sequential Dependencies**: Entities with foreign keys seeded in dependency order
2. **Cross-Storage Linking**: Consistent ID generation across storage systems
3. **Transaction Management**: Rollback capabilities for failed operations
4. **Validation**: Cross-storage integrity checks

## Entity Relationship Mappings

### Core Entity Dependencies

```mermaid
erDiagram
    TenantProfile ||--o{ Client : "belongs to"
    TenantProfile ||--o{ Report : "owns"
    TenantProfile ||--o{ ApplicationUser : "contains"
    
    Client ||--o{ Report : "has reports"
    Template ||--o{ Report : "generates"
    
    Report ||--|| ReportVersion : "current version"
    Report ||--o{ ReportVersion : "has versions"
    Report ||--|| ReportStyle : "has style"
    Report ||--o{ ReportSection : "contains"
    
    ReportVersion ||--|| CosmosDocument : "data stored in"
    ReportVersion ||--|| BlobContainer : "components in"
    
    ReportSection ||--o{ ReportSectionField : "contains"
    
    CosmosDocument ||--o{ SectionData : "contains"
    BlobContainer ||--o{ ComponentFile : "contains"
```

### Seeding Order (Dependency Chain)

1. **TenantProfile** (Independent)
2. **ApplicationUser** (Depends on TenantProfile)
3. **Client** (Depends on TenantProfile)
4. **Template** (Independent)
5. **Report** (Depends on Client, Template, TenantProfile)
6. **ReportStyle** (Depends on Report)
7. **ReportVersion** (Depends on Report)
8. **ReportSection** (Depends on Report)
9. **ReportSectionField** (Depends on ReportSection)
10. **CosmosDocument** (Depends on ReportVersion, Sections, Fields)
11. **BlobComponents** (Depends on ReportVersion, Sections)

## Data Flow & Seeding Sequence

### Phase 1: SQL Foundation
```mermaid
sequenceDiagram
    participant DS as DataSeeder
    participant SQL as SQL Server
    participant Logger as Logger
    
    DS->>SQL: Seed TenantProfiles
    DS->>SQL: Seed ApplicationUsers
    DS->>SQL: Seed Clients
    DS->>SQL: Seed Templates
    DS->>SQL: Seed Reports
    DS->>SQL: Seed ReportStyles
    DS->>SQL: Seed ReportVersions
    DS->>SQL: Seed ReportSections
    DS->>SQL: Seed ReportSectionFields
    DS->>Logger: Log SQL completion
```

### Phase 2: Cosmos DB Documents
```mermaid
sequenceDiagram
    participant DS as DataSeeder
    participant SQL as SQL Server
    participant Cosmos as Cosmos DB
    participant Logger as Logger
    
    DS->>SQL: Query Report Data
    SQL-->>DS: Report + Sections + Fields
    DS->>DS: Transform to JSON Documents
    DS->>Cosmos: Create Report Documents
    DS->>SQL: Update Document References
    DS->>Logger: Log Cosmos completion
```

### Phase 3: Blob Storage Components
```mermaid
sequenceDiagram
    participant DS as DataSeeder
    participant SQL as SQL Server
    participant Blob as Blob Storage
    participant Logger as Logger
    
    DS->>SQL: Query Report Versions
    SQL-->>DS: Version + Section Types
    DS->>DS: Generate Component Code
    DS->>Blob: Upload Components
    DS->>Blob: Upload Metadata
    DS->>SQL: Update Blob References
    DS->>Logger: Log Blob completion
```

## File Structure & Components

### Current File Inventory

```
FY.WB.CSHero2.Infrastructure/
├── Persistence/
│   ├── Seeders/
│   │   └── DataSeeder.cs                    # Main seeding orchestrator
│   └── SeedData/                           # JSON data files
│       ├── tenant-profiles.json            # Tenant configurations
│       ├── clients.json                    # Client/customer data
│       ├── reports.json                    # Report metadata
│       ├── report-sections.json            # Report section structure
│       ├── report-section-fields.json      # Field-level data
│       ├── templates.json                  # Report templates
│       ├── forms.json                      # Form definitions
│       ├── invoices.json                   # Invoice data
│       └── uploads.json                    # File upload records
```

### Enhanced File Structure (Target)

```
FY.WB.CSHero2.Infrastructure/
├── Persistence/
│   ├── Seeders/
│   │   ├── DataSeeder.cs                   # Main orchestrator
│   │   ├── SqlSeeder.cs                    # SQL-specific seeding
│   │   ├── CosmosSeeder.cs                 # Cosmos DB seeding
│   │   ├── BlobSeeder.cs                   # Blob storage seeding
│   │   └── SeedingCoordinator.cs           # Cross-storage coordination
│   └── SeedData/
│       ├── SQL/                            # SQL Server data
│       │   ├── tenant-profiles.json
│       │   ├── clients.json
│       │   ├── reports.json
│       │   ├── report-sections.json
│       │   ├── report-section-fields.json
│       │   ├── report-versions.json        # NEW
│       │   ├── report-styles.json          # NEW
│       │   ├── templates.json
│       │   ├── forms.json
│       │   ├── invoices.json
│       │   └── uploads.json
│       ├── CosmosDB/                       # Cosmos DB documents
│       │   ├── report-documents/
│       │   │   ├── healthcare-reports/
│       │   │   ├── tech-reports/
│       │   │   ├── finance-reports/
│       │   │   └── research-reports/
│       │   └── document-templates/
│       │       ├── executive-summary.json
│       │       ├── financial-analysis.json
│       │       ├── key-metrics.json
│       │       └── project-timeline.json
│       ├── BlobStorage/                    # Blob storage content
│       │   ├── components/
│       │   │   ├── ExecutiveSummary.tsx
│       │   │   ├── FinancialChart.tsx
│       │   │   ├── MetricsTable.tsx
│       │   │   ├── ProjectTimeline.tsx
│       │   │   └── CustomChart.tsx
│       │   ├── metadata/
│       │   │   └── components-metadata.json
│       │   └── assets/
│       │       ├── logos/
│       │       ├── icons/
│       │       └── templates/
│       └── Templates/                      # Report templates
│           ├── healthcare-template.json
│           ├── technology-template.json
│           ├── finance-template.json
│           └── research-template.json
```

### File Responsibilities

#### Core Seeding Files

| File | Purpose | Dependencies | Update Frequency |
|------|---------|--------------|------------------|
| `DataSeeder.cs` | Main orchestrator, coordinates all seeding | All other seeders | High |
| `SqlSeeder.cs` | SQL Server entity seeding | SQL data files | Medium |
| `CosmosSeeder.cs` | Cosmos DB document seeding | SQL entities, Cosmos data | Medium |
| `BlobSeeder.cs` | Blob storage component seeding | SQL entities, Blob files | Low |

#### SQL Data Files

| File | Entity | Records | Relationships |
|------|--------|---------|---------------|
| `tenant-profiles.json` | TenantProfile | 4 | None (root) |
| `clients.json` | Client | 20+ | TenantProfile |
| `reports.json` | Report | 25+ | Client, Template, TenantProfile |
| `report-sections.json` | ReportSection | 150+ | Report |
| `report-section-fields.json` | ReportSectionField | 500+ | ReportSection |
| `report-versions.json` | ReportVersion | 75+ | Report |
| `report-styles.json` | ReportStyle | 25+ | Report |
| `templates.json` | Template | 10+ | TenantProfile |

#### Cosmos DB Files

| Directory | Purpose | Content Type |
|-----------|---------|--------------|
| `report-documents/` | Report data documents | JSON documents |
| `document-templates/` | Reusable section templates | JSON templates |

#### Blob Storage Files

| Directory | Purpose | Content Type |
|-----------|---------|--------------|
| `components/` | React component definitions | TypeScript/JSX |
| `metadata/` | Component metadata | JSON |
| `assets/` | Static assets | Images, CSS, etc. |

## Data Categories & Coverage

### Entity Coverage Requirements

#### TenantProfile (Multi-tenancy)
- **Count**: 4 tenants
- **Variety**: Different industries, sizes, configurations
- **Purpose**: Test multi-tenant isolation and features

#### Client (Customer Management)
- **Count**: 20+ clients
- **Distribution**: 5+ per tenant
- **Variety**: Different industries, company sizes, contact types
- **Purpose**: Test client-specific features and reporting

#### Report (Core Functionality)
- **Count**: 25+ reports
- **Categories**: Healthcare, Technology, Finance, Research
- **Status Variety**: Draft, In Progress, Completed, Archived
- **Purpose**: Test all report lifecycle stages

#### ReportSection (Content Structure)
- **Count**: 150+ sections
- **Types**: text, chart, table, timeline, metrics, custom
- **Distribution**: 5-8 sections per report
- **Purpose**: Test content rendering and organization

#### ReportSectionField (Data Granularity)
- **Count**: 500+ fields
- **Types**: string, number, date, json, boolean, richtext
- **Distribution**: 3-6 fields per section
- **Purpose**: Test data type handling and validation

### Data Type Coverage

#### Field Types
- **string**: Text content, headings, descriptions
- **number**: Metrics, counts, percentages
- **date**: Timestamps, deadlines, milestones
- **json**: Complex data structures, chart data
- **boolean**: Flags, toggles, status indicators
- **richtext**: Formatted content, HTML

#### Content Patterns
- **Executive Summaries**: Business overview content
- **Financial Data**: Revenue, profit, growth metrics
- **Charts**: Bar, line, pie, scatter plot data
- **Tables**: Structured data with headers and rows
- **Timelines**: Project milestones, historical data
- **KPIs**: Key performance indicators

### Validation Rules

#### Data Integrity
- All foreign keys must reference existing entities
- Audit fields (CreationTime, LastModificationTime) must be valid
- JSON content must be well-formed
- Enum values must match defined constants

#### Business Logic
- Report sections must have sequential order values
- Report fields must have sequential order within sections
- Version numbers must be sequential and unique per report
- Tenant isolation must be maintained

## Multi-Storage Integration

### Cross-Storage ID Strategy

#### Consistent ID Generation
```csharp
// SQL Server: Primary entity with GUID
var reportId = Guid.NewGuid();

// Cosmos DB: Document ID references SQL entity
var documentId = $"report-data-{reportId}";

// Blob Storage: Container path references SQL entity
var blobPath = $"reports/{reportId}/{versionId}/";
```

#### Reference Linking
- **SQL → Cosmos**: `Report.DataDocumentId` → Cosmos document ID
- **SQL → Blob**: `ReportVersion.ComponentsBlobId` → Blob container path
- **Cosmos → Blob**: Document metadata includes blob references

### Data Synchronization

#### Seeding Sequence
1. **SQL Entities**: Create all SQL entities with generated IDs
2. **Cosmos Documents**: Create documents using SQL entity IDs
3. **Blob Components**: Upload components using SQL entity references
4. **Update References**: Update SQL entities with Cosmos/Blob IDs

#### Consistency Checks
- Verify all SQL entities have corresponding Cosmos documents
- Verify all report versions have blob components
- Validate cross-storage ID references
- Check for orphaned records

### Error Handling

#### Rollback Strategy
- **SQL Transactions**: Use database transactions for SQL operations
- **Cosmos Cleanup**: Delete created documents on failure
- **Blob Cleanup**: Remove uploaded blobs on failure
- **Partial Recovery**: Continue with remaining entities after non-critical failures

## Maintenance Guidelines

### Adding New Seed Data

#### New Reports
1. **Add to reports.json**: Include all required fields and valid foreign keys
2. **Create sections**: Add corresponding entries in report-sections.json
3. **Create fields**: Add field entries in report-section-fields.json
4. **Add versions**: Create version entries in report-versions.json
5. **Add styles**: Create style configuration in report-styles.json
6. **Test relationships**: Verify all foreign key references are valid

#### New Entity Types
1. **Update DataSeeder.cs**: Add seeding logic for new entity
2. **Create seed file**: Add JSON file with sample data
3. **Update dependencies**: Ensure proper seeding order
4. **Add validation**: Include integrity checks
5. **Update documentation**: Document new entity and relationships

### Modifying Existing Data

#### Changing Entity Structure
1. **Update entity models**: Modify C# entity classes
2. **Update seed files**: Adjust JSON structure to match
3. **Update seeding logic**: Modify DataSeeder.cs if needed
4. **Test migrations**: Ensure database schema changes work
5. **Validate data**: Check all relationships remain valid

#### Updating Content
1. **Modify JSON files**: Update content while preserving structure
2. **Maintain relationships**: Ensure foreign keys remain valid
3. **Test seeding**: Run full seeding process to verify
4. **Check application**: Verify changes work in application

### Version Control Considerations

#### File Organization
- Keep related files together (e.g., all report-related files)
- Use consistent naming conventions
- Include file purpose in comments or documentation

#### Change Management
- Document significant changes in commit messages
- Review changes for data consistency
- Test seeding after modifications
- Coordinate changes across team members

#### Backup Strategy
- Maintain backup copies of working seed data
- Use branching for experimental changes
- Tag stable versions for easy rollback

### Testing Seeded Data

#### Validation Checks
```csharp
// Example validation methods
public static bool ValidateReportRelationships()
{
    // Check all reports have valid client references
    // Verify all sections belong to existing reports
    // Ensure all fields belong to existing sections
}

public static bool ValidateCrossStorageConsistency()
{
    // Verify SQL entities have Cosmos documents
    // Check blob references are valid
    // Validate ID consistency across systems
}
```

#### Integration Testing
- Test complete seeding process
- Verify application functionality with seeded data
- Check multi-tenant isolation
- Validate performance with full dataset

## Performance Considerations

### Optimization Strategies

#### Batch Operations
- Group related inserts into batches
- Use bulk operations where possible
- Minimize database round trips

#### Parallel Processing
- Seed independent entities concurrently
- Use async/await for I/O operations
- Implement proper synchronization

#### Memory Management
- Stream large JSON files instead of loading entirely
- Dispose resources properly
- Monitor memory usage during seeding

### Performance Targets

| Operation | Target Time | Current Time | Optimization |
|-----------|-------------|--------------|--------------|
| SQL Seeding | <10 seconds | ~5 seconds | Batch inserts |
| Cosmos Seeding | <15 seconds | TBD | Bulk operations |
| Blob Seeding | <10 seconds | TBD | Parallel uploads |
| Total Seeding | <30 seconds | ~5 seconds | Multi-threading |

### Monitoring

#### Metrics Collection
- Track seeding duration by entity type
- Monitor memory usage during operations
- Log error rates and types
- Measure database connection usage

#### Performance Alerts
- Alert on seeding time > 60 seconds
- Monitor for memory leaks
- Track error rate increases
- Alert on database connection exhaustion

## Troubleshooting Guide

### Common Issues

#### Foreign Key Violations
**Symptoms**: Seeding fails with FK constraint errors
**Causes**: 
- Incorrect ID references in JSON files
- Wrong seeding order
- Missing parent entities

**Solutions**:
- Verify all foreign key references exist
- Check seeding order follows dependency chain
- Validate JSON file consistency

#### Data Inconsistencies
**Symptoms**: Application errors with seeded data
**Causes**:
- Mismatched data types
- Invalid enum values
- Malformed JSON content

**Solutions**:
- Validate JSON structure before seeding
- Check enum values against defined constants
- Test data with application features

#### Performance Issues
**Symptoms**: Slow seeding process
**Causes**:
- Large dataset size
- Inefficient database operations
- Memory constraints

**Solutions**:
- Implement batch operations
- Use parallel processing
- Optimize database queries
- Monitor memory usage

#### Cross-Storage Inconsistencies
**Symptoms**: Missing data in Cosmos DB or Blob Storage
**Causes**:
- Failed cross-storage operations
- Incorrect ID linking
- Network connectivity issues

**Solutions**:
- Implement retry logic
- Validate cross-storage references
- Add comprehensive error handling
- Check network connectivity

### Diagnostic Tools

#### Logging
- Enable detailed logging for all seeding operations
- Log entity counts and timing information
- Include error details and stack traces
- Monitor cross-storage operations

#### Validation Scripts
- Create scripts to validate data consistency
- Check foreign key relationships
- Verify cross-storage ID references
- Validate business logic rules

#### Performance Profiling
- Use profiling tools to identify bottlenecks
- Monitor database query performance
- Track memory allocation patterns
- Analyze I/O operations

This architecture guide provides comprehensive coverage of the data seeding system, from high-level goals to detailed troubleshooting procedures. It serves as both a reference for understanding the current system and a roadmap for implementing the enhanced multi-storage architecture.
