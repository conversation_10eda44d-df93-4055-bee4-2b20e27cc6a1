1. **Database Seeding**
   • Seed data - reference 'DataSeeder_Documentation/README.md'
      - Adhere to foregin key requirements
      - Adhere to data validation requirements
   • Create `Infrastructure/Data/SeedData.cs` with an idempotent `SeedAsync(IServiceProvider)` method.
   • Source data from JSON files in `FY.WB.CSHero2.Infrastructure\Persistence\SeedData`.
   • Tests must call an API that first invokes `SeedData.SeedAsync` to guarantee a known state.
