# Report Structure Refactoring Plan

## Overview

This project aims to refactor the report structure to follow a hierarchical model:
1. Reports object (main container with metadata)
2. Reports section (a list that can be infinite)
3. Reports section fields (each section contains a list of fields)

The current implementation uses JSON serialization for report content, which lacks proper structure and type safety. This refactoring will create explicit entity relationships and improve data integrity.

## Project Structure

```
FY.WB.CSHero2/
├── Domain/
│   └── Entities/
│       ├── Report.cs (existing, to be updated)
│       ├── ReportSection.cs (new)
│       └── ReportSectionField.cs (new)
├── Infrastructure/
│   └── Persistence/
│       └── Configurations/
│           ├── ReportConfiguration.cs (to be updated)
│           ├── ReportSectionConfiguration.cs (new)
│           └── ReportSectionFieldConfiguration.cs (new)
├── Application/
│   ├── Reports/
│   │   ├── Commands/
│   │   │   ├── CreateReportCommand.cs (to be updated)
│   │   │   ├── CreateReportCommandHandler.cs (to be updated)
│   │   │   ├── UpdateReportCommand.cs (to be updated)
│   │   │   └── UpdateReportCommandHandler.cs (to be updated)
│   │   ├── Queries/
│   │   │   ├── GetReportByIdQuery.cs (to be updated)
│   │   │   └── GetReportByIdQueryHandler.cs (to be updated)
│   │   └── Dtos/
│   │       ├── ReportDto.cs (to be updated)
│   │       ├── CreateReportRequestDto.cs (to be updated)
│   │       ├── UpdateReportRequestDto.cs (to be updated)
│   │       ├── ReportSectionDto.cs (new)
│   │       └── ReportSectionFieldDto.cs (new)


```

## Task Breakdown

### Phase 1: Domain Model Changes

#### Task 1.1: Create New Domain Entities
- Create `ReportSection.cs` entity
- Create `ReportSectionField.cs` entity
- Update `Report.cs` to include navigation properties to sections

#### Task 1.2: Update Entity Configurations
- Create `ReportSectionConfiguration.cs`
- Create `ReportSectionFieldConfiguration.cs`
- Update `ReportConfiguration.cs` to include relationships

#### Task 1.3: Update ApplicationDbContext
- Add DbSets for new entities
- Ensure proper entity registration

### Phase 2: Database Migration

#### Task 2.1: Create Migration
- Generate EF Core migration for schema changes
- Review migration for correctness
- Create data migration script to convert existing JSON data to new structure

#### Task 2.2: Apply Migration
- Apply migration to development database
- Verify schema changes
- Run data migration script
- Verify data integrity after migration

### Phase 3: Application Layer Updates

#### Task 3.1: Update DTOs
- Create `ReportSectionDto.cs`
- Create `ReportSectionFieldDto.cs`
- Update `ReportDto.cs` to include sections
- Update `CreateReportRequestDto.cs` to include sections
- Update `UpdateReportRequestDto.cs` to include sections

#### Task 3.2: Update Commands and Handlers
- Update `CreateReportCommand.cs` and handler to handle sections and fields
- Update `UpdateReportCommand.cs` and handler to handle sections and fields
- Update `DeleteReportCommand.cs` and handler to handle cascading deletes

#### Task 3.3: Update Queries and Handlers
- Update `GetReportByIdQuery.cs` and handler to include sections and fields
- Update `GetReportsQuery.cs` and handler to include necessary data

### Phase 4: API and Frontend Integration

#### Task 4.1: Update API Controllers
- Update `ReportsController.cs` to handle the new structure
- Ensure proper validation and error handling
- Add endpoints for section and field management if needed

#### Task 4.2: Update Frontend Models
- Update TypeScript interfaces to match new structure
- Update API service functions to handle new structure
- Update UI components to display and edit the new structure

### Phase 5: Testing

#### Task 5.1: Integration Tests
- Create test database setup with real data
- Write integration tests for creating reports with sections and fields
- Write integration tests for updating reports with sections and fields
- Write integration tests for retrieving reports with sections and fields
- Write integration tests for deleting reports with sections and fields

#### Task 5.2: End-to-End Testing
- Test report creation flow with new structure
- Test report editing flow with new structure
- Test report viewing flow with new structure
- Test report deletion flow with new structure

### Phase 6: Bug Fixing and Optimization

#### Task 6.1: Identify and Fix Bugs
- Run comprehensive test suite
- Fix any bugs or issues identified
- Address edge cases and error scenarios

#### Task 6.2: Performance Optimization
- Optimize database queries for the new structure
- Add appropriate indexes for common query patterns
- Implement caching strategies if needed

### Phase 7: Documentation and Deployment

#### Task 7.1: Update Documentation
- Update API documentation to reflect new structure
- Update developer documentation with new entity relationships
- Create migration guide for any external integrations

#### Task 7.2: Deployment Planning
- Create deployment script for production
- Plan database migration strategy for production
- Schedule deployment window with minimal disruption

## Implementation Details

### Domain Model

```csharp
// Report (existing, main container)
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    // Existing properties...
    
    // Navigation property to sections
    public virtual ICollection<ReportSection> Sections { get; set; } = new List<ReportSection>();
}

// New entity for report sections
public class ReportSection : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Navigation properties
    public virtual Report Report { get; set; } = null!;
    public virtual ICollection<ReportSectionField> Fields { get; set; } = new List<ReportSectionField>();
}

// New entity for section fields
public class ReportSectionField : AuditedEntity<Guid>
{
    public Guid SectionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Navigation property
    public virtual ReportSection Section { get; set; } = null!;
}
```

### Database Configuration

```csharp
// ReportSectionConfiguration
public class ReportSectionConfiguration : IEntityTypeConfiguration<ReportSection>
{
    public void Configure(EntityTypeBuilder<ReportSection> builder)
    {
        builder.ToTable("ReportSections");
        builder.HasKey(s => s.Id);
        
        builder.Property(s => s.Title).IsRequired().HasMaxLength(200);
        builder.Property(s => s.Type).IsRequired().HasMaxLength(50);
        builder.Property(s => s.Order).IsRequired();
        
        builder.HasOne(s => s.Report)
            .WithMany(r => r.Sections)
            .HasForeignKey(s => s.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(s => new { s.ReportId, s.Order });
    }
}

// ReportSectionFieldConfiguration
public class ReportSectionFieldConfiguration : IEntityTypeConfiguration<ReportSectionField>
{
    public void Configure(EntityTypeBuilder<ReportSectionField> builder)
    {
        builder.ToTable("ReportSectionFields");
        builder.HasKey(f => f.Id);
        
        builder.Property(f => f.Name).IsRequired().HasMaxLength(100);
        builder.Property(f => f.Type).IsRequired().HasMaxLength(50);
        builder.Property(f => f.Content).HasColumnType("nvarchar(max)");
        builder.Property(f => f.Order).IsRequired();
        
        builder.HasOne(f => f.Section)
            .WithMany(s => s.Fields)
            .HasForeignKey(f => f.SectionId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasIndex(f => new { f.SectionId, f.Order });
    }
}
```

### Data Migration Strategy

1. Create new tables for `ReportSections` and `ReportSectionFields`
2. For each existing report:
   - Read the JSON content from `ReportVersion.JsonData`
   - Parse the sections array
   - For each section in the array:
     - Create a new `ReportSection` record
     - Extract fields from the section content
     - For each field:
       - Create a new `ReportSectionField` record
3. Verify data integrity after migration

### Integration Testing Approach

1. Use a real SQL Server database for testing (no mocks)
2. Set up test data with known reports, sections, and fields
3. Execute actual API calls through the controller
4. Verify database state after operations
5. Test multi-tenant data isolation
6. Test error handling and validation

## Timeline

- **Week 1**: Domain model changes and database migration
- **Week 2**: Application layer updates and API integration
- **Week 3**: Frontend integration and testing
- **Week 4**: Bug fixing, optimization, and documentation

## Risks and Mitigation

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data loss during migration | High | Medium | Create backups before migration, run in staging first |
| Performance issues with new structure | Medium | Low | Add appropriate indexes, optimize queries |
| Frontend compatibility issues | Medium | Medium | Thorough testing, temporary backward compatibility |
| Deployment disruption | High | Low | Schedule deployment during off-hours, have rollback plan |

## Success Criteria

1. All reports are successfully migrated to the new structure
2. All CRUD operations work correctly with the new structure
3. Frontend displays and edits reports correctly
4. Performance is equal to or better than the previous implementation
5. All tests pass with the new structure