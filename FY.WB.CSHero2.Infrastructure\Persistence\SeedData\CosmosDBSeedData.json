{"today": {"metrics": {"totalCustomers": {"current": 150, "previousPeriod": 145, "trend": [140, 142, 145, 143, 147, 149, 150]}, "newCustomers": {"current": 5, "previousPeriod": 3, "trend": [2, 3, 2, 4, 3, 4, 5]}, "reportsCreated": {"current": 25, "previousPeriod": 20, "trend": [18, 20, 22, 21, 23, 24, 25]}, "revenue": {"current": 2500, "previousPeriod": 2200, "trend": [2100, 2200, 2300, 2250, 2400, 2450, 2500]}}}, "wtd": {"metrics": {"totalCustomers": {"current": 160, "previousPeriod": 150, "trend": [145, 148, 150, 153, 155, 158, 160]}, "newCustomers": {"current": 15, "previousPeriod": 12, "trend": [10, 11, 12, 13, 14, 14, 15]}, "reportsCreated": {"current": 120, "previousPeriod": 100, "trend": [95, 98, 102, 105, 110, 115, 120]}, "revenue": {"current": 12000, "previousPeriod": 10000, "trend": [9500, 10000, 10500, 11000, 11200, 11500, 12000]}}}, "mtd": {"metrics": {"totalCustomers": {"current": 200, "previousPeriod": 180, "trend": [170, 175, 180, 185, 190, 195, 200]}, "newCustomers": {"current": 45, "previousPeriod": 35, "trend": [30, 33, 36, 38, 40, 42, 45]}, "reportsCreated": {"current": 450, "previousPeriod": 400, "trend": [380, 390, 400, 410, 420, 435, 450]}, "revenue": {"current": 45000, "previousPeriod": 40000, "trend": [38000, 39000, 40000, 41000, 42000, 43500, 45000]}}}, "ytd": {"metrics": {"totalCustomers": {"current": 1200, "previousPeriod": 1000, "trend": [950, 1000, 1050, 1100, 1150, 1180, 1200]}, "newCustomers": {"current": 350, "previousPeriod": 300, "trend": [280, 290, 300, 310, 320, 335, 350]}, "reportsCreated": {"current": 5200, "previousPeriod": 4800, "trend": [4600, 4700, 4800, 4900, 5000, 5100, 5200]}, "revenue": {"current": 520000, "previousPeriod": 480000, "trend": [460000, 470000, 480000, 490000, 500000, 510000, 520000]}}}, "reports": [{"reportId": "CSR-2025-001", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Q1 Customer Satisfaction Analysis", "category": "Satisfaction Survey", "slideCount": 12, "createdAt": "2025-01-15T10:30:00Z", "lastModified": "2025-01-18T14:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-002", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Support Ticket Resolution Time Analysis", "category": "Performance Metrics", "slideCount": 8, "createdAt": "2025-01-20T09:15:00Z", "lastModified": "2025-01-22T16:20:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-003", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Customer Feedback Implementation Plan", "category": "Action Plan", "slideCount": 15, "createdAt": "2025-01-25T13:45:00Z", "lastModified": "2025-02-10T11:30:00Z", "status": "In Progress", "author": "<PERSON>"}, {"reportId": "CSR-2025-004", "clientId": "CLIENT-003", "clientName": "Global Shipping Partners", "reportName": "Support Channel Effectiveness", "category": "Channel Analysis", "slideCount": 10, "createdAt": "2025-02-05T08:20:00Z", "lastModified": "2025-02-15T17:10:00Z", "status": "Under Review", "author": "<PERSON>"}, {"reportId": "CSR-2025-005", "clientId": "CLIENT-004", "clientName": "Pinnacle Healthcare", "reportName": "Customer Onboarding Experience", "category": "User Experience", "slideCount": 14, "createdAt": "2025-02-12T11:05:00Z", "lastModified": "2025-02-20T09:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-006", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Product Support Gap Analysis", "category": "Product Improvement", "slideCount": 18, "createdAt": "2025-02-18T14:30:00Z", "lastModified": "2025-02-22T16:15:00Z", "status": "Draft", "author": "<PERSON>"}], "categoryMappings": {"Satisfaction Survey": "<PERSON><PERSON><PERSON>", "Performance Metrics": "Performance", "Action Plan": "Strategy", "Channel Analysis": "Usage", "User Experience": "Usage", "Product Improvement": "Performance", "Financial": "Performance", "Research": "Strategy", "Security": "Performance", "Analytics": "Usage", "Healthcare": "<PERSON><PERSON><PERSON>", "Risk Management": "Strategy", "Wealth Management": "Performance", "Operations": "Performance", "Technology": "Usage", "Development": "Performance", "AI/ML": "Strategy"}, "timeframeMappings": {"Feedback": "today", "Performance": "wtd", "Strategy": "mtd", "Usage": "ytd"}, "chartConfigurations": {"Feedback": {"chartType": "pie", "colors": ["#10b981", "#3b82f6", "#f59e0b"], "dataPointCount": 7}, "Performance": {"chartType": "line", "colors": ["#3b82f6", "#1d4ed8"], "dataPointCount": 7}, "Strategy": {"chartType": "bar", "colors": ["#f59e0b", "#d97706"], "dataPointCount": 7}, "Usage": {"chartType": "area", "colors": ["#8b5cf6", "#7c3aed"], "dataPointCount": 7}}, "sampleDocuments": [{"id": "rpt_550e8400e29b41d4a716446655440000_v_c8bc5efcccbd42c0a7aa019767f8b83d", "TenantId": "941fdfc2-c519-48ec-96af-6f893aca18ad", "reportId": "550e8400-e29b-41d4-a716-446655440000", "versionId": "c8bc5efc-ccbd-42c0-a7aa-019767f8b83d", "versionNumber": 1, "isDraft": false, "reportName": "Quarterly Financial Analysis", "reportNumber": "CSR-2025-008", "category": "Financial", "status": "Completed", "author": "Financial Team", "clientId": "D4E5F667-7889-0123-4567-890123DEF678", "clientName": "BioTech Innovations", "sections": [{"id": "7ed32b50-d07d-425f-bd35-730c801ae9cd", "name": "Executive Summary", "title": "Executive Summary", "description": "", "sectionType": "text", "displayOrder": 0, "isRequired": false, "fields": [{"id": "c11c3838-3f94-4652-81f4-d1868017edb4", "name": "heading", "label": "heading", "fieldType": "string", "defaultValue": "Executive Summary", "isRequired": false, "displayOrder": 0, "validationRules": {}, "options": []}, {"id": "055a0d3e-f1f1-4fcf-aab0-ffc5b2ec430e", "name": "content", "label": "content", "fieldType": "richtext", "defaultValue": "This quarterly financial analysis provides a comprehensive overview of BioTech Innovations' performance for Q4 2024. Key highlights include revenue growth of 18% year-over-year, improved operational efficiency, and successful product launches in the biotechnology sector.", "isRequired": false, "displayOrder": 1, "validationRules": {}, "options": []}]}, {"id": "1b431c26-e371-4fb4-81d6-10438039a3e0", "name": "Financial Analysis", "title": "Financial Analysis", "description": "", "sectionType": "chart", "displayOrder": 1, "isRequired": false, "fields": [{"id": "0822bf04-26a4-410b-aee3-bf7c0eaaa6fb", "name": "chartType", "label": "chartType", "fieldType": "string", "defaultValue": "bar", "isRequired": false, "displayOrder": 0, "validationRules": {}, "options": []}, {"id": "c5fe5ab3-ad47-4fc8-a555-baa17a58ff73", "name": "data", "label": "data", "fieldType": "json", "defaultValue": "{\"labels\": [\"Q1 2024\", \"Q2 2024\", \"Q3 2024\", \"Q4 2024\"], \"values\": [2100000, 2350000, 2600000, 2850000], \"currency\": \"USD\"}", "isRequired": false, "displayOrder": 1, "validationRules": {}, "options": []}]}, {"id": "db1d9669-d9a5-4277-9715-a8578d8c7d2a", "name": "Key Metrics", "title": "Key Metrics", "description": "", "sectionType": "table", "displayOrder": 2, "isRequired": false, "fields": [{"id": "95be95ed-d8ac-42c6-9baf-830c6c2c88c6", "name": "headers", "label": "headers", "fieldType": "json", "defaultValue": "[\"Metric\", \"Q3 2024\", \"Q4 2024\", \"Change\", \"Target\"]", "isRequired": false, "displayOrder": 0, "validationRules": {}, "options": []}, {"id": "0a854b76-9c10-4264-b4af-c702e984f350", "name": "rows", "label": "rows", "fieldType": "json", "defaultValue": "[[\"Revenue\", \"$2.6M\", \"$2.85M\", \"+9.6%\", \"$2.8M\"], [\"Profit Margin\", \"22.5%\", \"24.8%\", \"+2.3%\", \"23%\"], [\"R&D Investment\", \"$520K\", \"$570K\", \"+9.6%\", \"$550K\"], [\"Customer Count\", \"1,850\", \"2,120\", \"+14.6%\", \"2,000\"]]", "isRequired": false, "displayOrder": 1, "validationRules": {}, "options": []}]}], "componentDataJson": "{\"layout\": \"standard\", \"theme\": \"corporate\"}", "jsonData": "{\"enhanced\": true, \"dashboardMetrics\": {\"totalCustomers\": {\"current\": 1200, \"previousPeriod\": 1000, \"trend\": [950, 1000, 1050, 1100, 1150, 1180, 1200]}}}}", "metadata": {"createdAt": "2024-03-07T08:30:00Z", "updatedAt": "2024-03-07T08:30:00Z", "sectionCount": 3, "fieldCount": 8, "version": "1", "tags": ["Financial", "Completed", "enhanced_with_mock_data"]}}]}