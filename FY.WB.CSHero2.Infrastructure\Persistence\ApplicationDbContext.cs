using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore; // For IdentityDbContext
using Microsoft.EntityFrameworkCore;
// using FY.WB.CSHero2.Domain; // For ApplicationUser - This might be causing ambiguity, ApplicationUser is now in this namespace.
using FY.WB.CSHero2.Domain.Entities; // For Client
using FY.WB.CSHero2.Domain.Entities.Core; // For AppTenantInfo if it uses base entities
using System.Reflection; // For Assembly.GetExecutingAssembly()
using FY.WB.CSHero2.Application.Common.Interfaces; // For IApplicationDbContext
using Finbuckle.MultiTenant.Abstractions; // For ITenantInfo
using System.Linq.Expressions;
using System.Linq;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Infrastructure.Persistence
{
    // AppTenantInfo is defined in AppTenantInfo.cs
    // ApplicationUser is defined in ApplicationUser.cs

    // Using standard IdentityDbContext with manual multi-tenancy implementation
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole<Guid>, Guid>, IApplicationDbContext
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly ITenantInfo? _tenantInfo;

        // Entity DbSets
        public DbSet<Client> Clients { get; set; } = null!;
        public DbSet<Report> Reports { get; set; } = null!;
        public DbSet<Template> Templates { get; set; } = null!;
        public DbSet<TenantProfile> TenantProfiles { get; set; } = null!;

        // V2 Report Rendering Engine DbSets
        public DbSet<ReportVersion> ReportVersions { get; set; } = null!;

        // Report Structure DbSets
        public DbSet<ReportSection> ReportSections { get; set; } = null!;
        public DbSet<ReportSectionField> ReportSectionFields { get; set; } = null!;
        // V3 Multi-storage DbSets
        public DbSet<ReportStyle> ReportStyles { get; set; } = null!;
        public DbSet<ReportStorageMetadata> ReportStorageMetadata { get; set; } = null!;

        // Factory-friendly constructor for IDbContextFactory usage
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
            // Dependencies will be injected manually by the factory
            _currentUserService = null!;
            _tenantInfo = null;
        }

        // Standard DI constructor
        public ApplicationDbContext(
            DbContextOptions<ApplicationDbContext> options,
            ICurrentUserService currentUserService,
            ITenantInfo? tenantInfo = null)
            : base(options)
        {
            _currentUserService = currentUserService;
            _tenantInfo = tenantInfo;
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Apply all configurations from the current assembly
            builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Add global query filters for IMultiTenant and ISoftDelete entities
            foreach (var entityType in builder.Model.GetEntityTypes())
            {
                var parameter = Expression.Parameter(entityType.ClrType, "e");
                Expression? combinedFilter = null;

                // Check if the entity implements IMultiTenant
                if (typeof(IMultiTenant).IsAssignableFrom(entityType.ClrType))
                {
                    var propertyAccess = Expression.PropertyOrField(parameter, "TenantId");

                    // Create a method call to our GetIsCurrentUserAdmin method
                    var isAdminMethod = typeof(ApplicationDbContext).GetMethod(
                        nameof(GetIsCurrentUserAdmin),
                        BindingFlags.NonPublic | BindingFlags.Instance);

                    var isAdminCall = Expression.Call(Expression.Constant(this), isAdminMethod);

                    // Create a method call to our GetCurrentTenantId method
                    var getTenantIdMethod = typeof(ApplicationDbContext).GetMethod(
                        nameof(GetCurrentTenantId),
                        BindingFlags.NonPublic | BindingFlags.Instance);

                    var tenantIdCall = Expression.Call(Expression.Constant(this), getTenantIdMethod);

                    // Create the expression: e => GetIsCurrentUserAdmin() || e.TenantId == GetCurrentTenantId()
                    var tenantIdEqualsExpression = Expression.Equal(propertyAccess, tenantIdCall);
                    combinedFilter = Expression.OrElse(isAdminCall, tenantIdEqualsExpression);
                }

                // Check if the entity implements ISoftDelete
                if (typeof(ISoftDelete).IsAssignableFrom(entityType.ClrType))
                {
                    var isDeletedProperty = Expression.PropertyOrField(parameter, "IsDeleted");
                    var isDeletedFalse = Expression.Equal(isDeletedProperty, Expression.Constant(false));

                    // Combine with existing filter if any
                    combinedFilter = combinedFilter == null ? isDeletedFalse : Expression.AndAlso(combinedFilter, isDeletedFalse);
                }

                // Apply the combined filter if we have one
                if (combinedFilter != null)
                {
                    var lambdaExpression = Expression.Lambda(combinedFilter, parameter);
                    builder.Entity(entityType.ClrType).HasQueryFilter(lambdaExpression);
                }
            }
        }

        // Helper methods for dynamic query filters
        private bool GetIsCurrentUserAdmin()
        {
            return _currentUserService.IsAdmin;
        }

        private Guid? GetCurrentTenantId()
        {
            return _currentUserService.TenantId;
        }

        public override int SaveChanges()
        {
            ApplyMultiTenancyAndAuditInfo();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyMultiTenancyAndAuditInfo();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void ApplyMultiTenancyAndAuditInfo()
        {
            var now = DateTime.UtcNow;
            var userId = _currentUserService.UserId != null ? Guid.Parse(_currentUserService.UserId) : (Guid?)null;
            var tenantId = _currentUserService.TenantId;

            foreach (var entry in ChangeTracker.Entries<IMultiTenant>())
            {
                // Set TenantId for new entities
                if (entry.State == EntityState.Added && tenantId.HasValue)
                {
                    entry.Entity.TenantId = tenantId;
                }
            }

            foreach (var entry in ChangeTracker.Entries<ICreationAuditedObject>())
            {
                if (entry.State == EntityState.Added)
                {
                    entry.Entity.CreationTime = now;
                    entry.Entity.CreatorId = userId;
                }
            }

            foreach (var entry in ChangeTracker.Entries<IModificationAuditedObject>())
            {
                if (entry.State == EntityState.Modified)
                {
                    entry.Entity.LastModificationTime = now;
                    entry.Entity.LastModifierId = userId;
                }
            }

            // Handle soft delete for entities that implement both ISoftDelete and IDeletionAuditedObject
            foreach (var entry in ChangeTracker.Entries().Where(e =>
                e.State == EntityState.Deleted &&
                e.Entity is ISoftDelete &&
                e.Entity is IDeletionAuditedObject))
            {
                entry.State = EntityState.Modified;

                // Set ISoftDelete properties
                ((ISoftDelete)entry.Entity).IsDeleted = true;

                // Set IDeletionAuditedObject properties
                ((IDeletionAuditedObject)entry.Entity).DeletionTime = now;
                ((IDeletionAuditedObject)entry.Entity).DeleterId = userId;
            }
        }
    }
}
