using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ReportSectionFieldConfiguration : IEntityTypeConfiguration<ReportSectionField>
    {
        public void Configure(EntityTypeBuilder<ReportSectionField> builder)
        {
            // Table configuration
            builder.ToTable("ReportSectionFields");
            builder.HasKey(f => f.Id);

            // Property configurations
            builder.Property(f => f.ReportSectionId)
                .IsRequired();

            builder.Property(f => f.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(f => f.Type)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(f => f.Content)
                .HasColumnType("nvarchar(max)");

            builder.Property(f => f.Order)
                .IsRequired();

            // Template tracking properties
            builder.Property(f => f.TemplateSourceFieldId)
                .IsRequired(false);

            builder.Property(f => f.IsModifiedFromTemplate)
                .IsRequired()
                .HasDefaultValue(false);

            // Relationship configurations
            builder.HasOne(f => f.Section)
                .WithMany(s => s.Fields)
                .HasForeignKey(f => f.ReportSectionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Index configurations
            builder.HasIndex(f => new { f.ReportSectionId, f.Order })
                .HasDatabaseName("IX_ReportSectionFields_ReportSectionId_Order");

            builder.HasIndex(f => f.ReportSectionId)
                .HasDatabaseName("IX_ReportSectionFields_ReportSectionId");

            builder.HasIndex(f => f.TemplateSourceFieldId)
                .HasDatabaseName("IX_ReportSectionFields_TemplateSourceFieldId");
        }
    }
}
