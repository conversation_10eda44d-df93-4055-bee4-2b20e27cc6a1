using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Application.Models.MultiStorage
{
    /// <summary>
    /// Cosmos DB document for storing report data (sections and fields)
    /// </summary>
    public class ReportData
    {
        /// <summary>
        /// Document ID in Cosmos DB
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Report ID from SQL database
        /// </summary>
        [JsonPropertyName("reportId")]
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Report version ID from SQL database
        /// </summary>
        [JsonPropertyName("versionId")]
        public string VersionId { get; set; } = string.Empty;

        /// <summary>
        /// Version number for tracking
        /// </summary>
        [JsonPropertyName("versionNumber")]
        public int VersionNumber { get; set; }

        /// <summary>
        /// Tenant ID for partitioning
        /// </summary>
        [JsonPropertyName("tenantId")]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// Report sections containing structured data
        /// </summary>
        [JsonPropertyName("sections")]
        public List<ReportSection> Sections { get; set; } = new List<ReportSection>();

        /// <summary>
        /// Document metadata
        /// </summary>
        [JsonPropertyName("metadata")]
        public ReportDataMetadata Metadata { get; set; } = new ReportDataMetadata();

        /// <summary>
        /// Creates a document ID for a report data document
        /// </summary>
        public static string CreateDocumentId(Guid reportId, Guid versionId)
        {
            return $"report-data-{reportId}-{versionId}";
        }

        /// <summary>
        /// Creates a document ID for the current version
        /// </summary>
        public static string CreateDocumentId(Guid reportId)
        {
            return $"report-data-{reportId}-current";
        }
    }

    /// <summary>
    /// Report section containing fields and content
    /// </summary>
    public class ReportSection
    {
        /// <summary>
        /// Unique section identifier
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Section title/name
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Section type (cover, text, chart, table, list, timeline, etc.)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display order within the report
        /// </summary>
        [JsonPropertyName("order")]
        public int Order { get; set; }

        /// <summary>
        /// Section fields containing the actual data
        /// </summary>
        [JsonPropertyName("fields")]
        public List<ReportSectionField> Fields { get; set; } = new List<ReportSectionField>();

        /// <summary>
        /// Section-level metadata
        /// </summary>
        [JsonPropertyName("metadata")]
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Individual field within a report section
    /// </summary>
    public class ReportSectionField
    {
        /// <summary>
        /// Unique field identifier within the section
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Field name/key
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Field type (text, number, date, image, chart, etc.)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Field content/value
        /// </summary>
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Display order within the section
        /// </summary>
        [JsonPropertyName("order")]
        public int Order { get; set; }

        /// <summary>
        /// Field-level metadata and configuration
        /// </summary>
        [JsonPropertyName("metadata")]
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Metadata for the report data document
    /// </summary>
    public class ReportDataMetadata
    {
        /// <summary>
        /// When the document was created
        /// </summary>
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who created the document
        /// </summary>
        [JsonPropertyName("createdBy")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the document was last modified
        /// </summary>
        [JsonPropertyName("lastModifiedAt")]
        public DateTime LastModifiedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who last modified the document
        /// </summary>
        [JsonPropertyName("lastModifiedBy")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// Document version for optimistic concurrency
        /// </summary>
        [JsonPropertyName("version")]
        public int Version { get; set; } = 1;

        /// <summary>
        /// Additional metadata
        /// </summary>
        [JsonPropertyName("additionalData")]
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }
}
