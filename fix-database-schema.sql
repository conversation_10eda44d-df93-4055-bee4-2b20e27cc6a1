-- Fix Database Schema Issues
-- 1. Increase ReportNumber column length from 20 to 50 characters
-- 2. Add missing TenantId and IsDeleted columns to ReportSections table

BEGIN TRANSACTION;

-- Fix 1: Increase ReportNumber column length
PRINT 'Updating ReportNumber column length from 20 to 50 characters...'
ALTER TABLE Reports
ALTER COLUMN ReportNumber NVARCHAR(50) NOT NULL;
PRINT 'ReportNumber column updated successfully.'

-- Fix 2: Add missing columns to ReportSections table
PRINT 'Adding TenantId column to ReportSections table...'
ALTER TABLE ReportSections
ADD TenantId UNIQUEIDENTIFIER NULL;
PRINT 'TenantId column added successfully.'

PRINT 'Adding IsDeleted column to ReportSections table...'
ALTER TABLE ReportSections
ADD IsDeleted BIT NOT NULL DEFAULT 0;
PRINT 'IsDeleted column added successfully.'

-- Add missing audit columns
PRINT 'Adding DeleterId column to ReportSections table...'
ALTER TABLE ReportSections
ADD DeleterId UNIQUEIDENTIFIER NULL;
PRINT 'DeleterId column added successfully.'

PRINT 'Adding DeletionTime column to ReportSections table...'
ALTER TABLE ReportSections
ADD DeletionTime DATETIME2 NULL;
PRINT 'DeletionTime column added successfully.'

-- Note: Indexes will be created automatically by Entity Framework on next migration

PRINT 'Database schema fixes completed successfully.'

COMMIT TRANSACTION;
