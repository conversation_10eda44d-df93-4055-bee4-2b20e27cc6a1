using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Application.Models.MultiStorage
{
    /// <summary>
    /// Component definition for storing in Azure Blob Storage
    /// </summary>
    public class ReportComponent
    {
        /// <summary>
        /// Unique component identifier
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Component name (used for file naming)
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Section ID this component represents
        /// </summary>
        [JsonPropertyName("sectionId")]
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// Generated React component code
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Required imports for the component
        /// </summary>
        [JsonPropertyName("imports")]
        public List<string> Imports { get; set; } = new List<string>();

        /// <summary>
        /// Component props/interface definitions
        /// </summary>
        [JsonPropertyName("props")]
        public List<string> Props { get; set; } = new List<string>();

        /// <summary>
        /// Component metadata
        /// </summary>
        [JsonPropertyName("metadata")]
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Metadata for a collection of components stored in blob storage
    /// </summary>
    public class ComponentsMetadata
    {
        /// <summary>
        /// Report ID
        /// </summary>
        [JsonPropertyName("reportId")]
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Version ID
        /// </summary>
        [JsonPropertyName("versionId")]
        public string VersionId { get; set; } = string.Empty;

        /// <summary>
        /// Tenant ID
        /// </summary>
        [JsonPropertyName("tenantId")]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// When the components were generated
        /// </summary>
        [JsonPropertyName("generatedAt")]
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who generated the components
        /// </summary>
        [JsonPropertyName("generatedBy")]
        public string GeneratedBy { get; set; } = string.Empty;

        /// <summary>
        /// Framework used (NextJS, React, etc.)
        /// </summary>
        [JsonPropertyName("framework")]
        public string Framework { get; set; } = "NextJS";

        /// <summary>
        /// Style framework used (TailwindCSS, etc.)
        /// </summary>
        [JsonPropertyName("styleFramework")]
        public string StyleFramework { get; set; } = "TailwindCSS";

        /// <summary>
        /// Component metadata list
        /// </summary>
        [JsonPropertyName("components")]
        public List<ComponentMetadata> Components { get; set; } = new List<ComponentMetadata>();

        /// <summary>
        /// Total size of all components in bytes
        /// </summary>
        [JsonPropertyName("totalSize")]
        public long TotalSize { get; set; }

        /// <summary>
        /// Additional metadata
        /// </summary>
        [JsonPropertyName("additionalData")]
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Metadata for an individual component
    /// </summary>
    public class ComponentMetadata
    {
        /// <summary>
        /// Component ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Component name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Section ID this component represents
        /// </summary>
        [JsonPropertyName("sectionId")]
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// File name in blob storage
        /// </summary>
        [JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Component size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// Component hash for change detection
        /// </summary>
        [JsonPropertyName("hash")]
        public string Hash { get; set; } = string.Empty;

        /// <summary>
        /// Required imports
        /// </summary>
        [JsonPropertyName("imports")]
        public List<string> Imports { get; set; } = new List<string>();

        /// <summary>
        /// Component props
        /// </summary>
        [JsonPropertyName("props")]
        public List<string> Props { get; set; } = new List<string>();

        /// <summary>
        /// When the component was generated
        /// </summary>
        [JsonPropertyName("generatedAt")]
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of component storage operation
    /// </summary>
    public class ComponentStorageResult
    {
        /// <summary>
        /// Blob ID/path where components are stored
        /// </summary>
        public string BlobId { get; set; } = string.Empty;

        /// <summary>
        /// Number of components stored
        /// </summary>
        public int ComponentCount { get; set; }

        /// <summary>
        /// Total size of stored components
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// Storage operation metadata
        /// </summary>
        public ComponentsMetadata Metadata { get; set; } = new ComponentsMetadata();

        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
