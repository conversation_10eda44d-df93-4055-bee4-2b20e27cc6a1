using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Models;
using FY.WB.CSHero2.Infrastructure.Services;
using Microsoft.Azure.Cosmos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public class UtcDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return DateTime.Parse(reader.GetString()!).ToUniversalTime();
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ"));
        }
    }

    public interface ICosmosSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default);
    }

    public class CosmosSeeder : ICosmosSeeder
    {
        private readonly ICosmosDbService? _cosmosDbService;
        private readonly ILogger<CosmosSeeder> _logger;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = null,
            WriteIndented = true,
            Converters = { new UtcDateTimeConverter() }
        };

        public CosmosSeeder(ICosmosDbService? cosmosDbService, ILogger<CosmosSeeder> logger)
        {
            _cosmosDbService = cosmosDbService;
            _logger = logger;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting Cosmos DB seeding...");

            if (_cosmosDbService == null)
            {
                _logger.LogWarning("Cosmos DB service not available, skipping Cosmos DB seeding");
                return;
            }

            try
            {
                var seedData = await LoadSeedDataAsync();
                if (seedData != null)
                {
                    _logger.LogInformation("Successfully loaded seed data with {ReportCount} reports", seedData.Reports.Count);
                }
                else
                {
                    _logger.LogWarning("Could not load seed data, proceeding with basic seeding");
                }

                var activeTenants = await context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(tp => tp.TenantId != null)
                    .Select(tp => tp.TenantId.ToString().ToUpperInvariant())
                    .ToListAsync(cancellationToken);

                if (!activeTenants.Any())
                {
                    _logger.LogError("No tenants found in TenantProfiles table - cannot proceed with Cosmos seeding");
                    return;
                }
                _logger.LogInformation("Found {TenantCount} tenants for Cosmos seeding: {TenantList}", 
                    activeTenants.Count, string.Join(", ", activeTenants));

                var existingDocumentIds = await GetExistingDocumentIdsAsync(cancellationToken);
                _logger.LogInformation("Found {ExistingCount} existing documents in Cosmos DB", existingDocumentIds.Count);

                var reports = await GetReportsWithDataAsync(context, cancellationToken);
                _logger.LogInformation("Found {ReportCount} reports to process for Cosmos DB seeding", reports.Count);

                Dictionary<string, List<SeedReport>>? categoryMapping = null;
                if (seedData != null && seedData.Reports.Any())
                {
                    categoryMapping = CreateCategoryMapping(seedData.Reports);
                    _logger.LogInformation("Created category mapping with {CategoryCount} categories", categoryMapping.Count);
                }

                var createdCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var report in reports)
                {
                    try
                    {
                        var reportVersions = await context.ReportVersions
                            .IgnoreQueryFilters()
                            .Where(rv => rv.ReportId == report.Id)
                            .OrderByDescending(rv => rv.VersionNumber)
                            .ToListAsync(cancellationToken);

                        foreach (var version in reportVersions)
                        {
                            var versionedDocumentId = GenerateVersionedDocumentId(report.Id, version.Id);

                            if (existingDocumentIds.Contains(versionedDocumentId))
                            {
                                _logger.LogDebug("Skipping report {ReportId} version {VersionId} - document already exists",
                                    report.Id, version.Id);
                                skippedCount++;
                                continue;
                            }

                            if (!report.TenantId.HasValue || report.TenantId.Value == Guid.Empty)
                            {
                                _logger.LogWarning("Skipping report {ReportId} '{ReportName}' - invalid TenantId",
                                    report.Id, report.Name);
                                errorCount++;
                                continue;
                            }

                            var tenantIdString = report.TenantId.Value.ToString().ToUpperInvariant();
                            if (!activeTenants.Contains(tenantIdString))
                            {
                                _logger.LogWarning("Skipping report {ReportId} '{ReportName}' - TenantId {TenantId} not found in active tenants. Available tenants: {ActiveTenants}",
                                    report.Id, report.Name, tenantIdString, string.Join(", ", activeTenants));
                                errorCount++;
                                continue;
                            }

                            var document = await CreateVersionedReportDocumentAsync(context, report, version, seedData, categoryMapping, cancellationToken);

                            if (string.IsNullOrEmpty(document.TenantId) || document.TenantId == "default")
                            {
                                _logger.LogWarning("Skipping report {ReportId} - document has invalid TenantId: {TenantId}",
                                    report.Id, document.TenantId);
                                errorCount++;
                                continue;
                            }

                            var validation = ValidateDocumentBeforeUpsert(document);

                            if (!validation.IsValid)
                            {
                                _logger.LogError("❌ VALIDATION FAILED: Document {DocumentId} has errors: {Errors}",
                                    document.Id, string.Join("; ", validation.Errors));
                                errorCount++;
                                continue;
                            }

                            _logger.LogDebug("✅ VALIDATION PASSED: Document {DocumentId} (Size: {Size} bytes)",
                                document.Id, validation.DocumentSize);

                            _logger.LogDebug("Attempting to upsert document {DocumentId} with partition key '{PartitionKey}' for report {ReportId} version {VersionId}",
                                document.Id, document.TenantId, report.Id, version.Id);

                            try
                            {
                                if (_cosmosDbService != null)
                                {
                                    await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
                                    _logger.LogInformation("✅ SUCCESS: Created document {DocumentId}", document.Id);
                                    createdCount++;
                                }
                            }
                            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.BadRequest && ex.SubStatusCode == 0)
                            {
                                _logger.LogError(ex, "❌ COSMOS VALIDATION ERROR: Document {DocumentId}. " +
                                    "Response: {ResponseBody}. Document JSON: {DocumentJson}",
                                    document.Id, ex.ResponseBody, validation.DocumentJson);
                                errorCount++;
                                continue;
                            }
                            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.BadRequest && ex.SubStatusCode == 1001)
                            {
                                _logger.LogError(ex, "Partition key mismatch for document {DocumentId}. " +
                                    "Document TenantId: '{DocumentTenantId}', Partition Key: '{PartitionKey}'. " +
                                    "Ensure JSON property name matches CosmosDB partition key path.",
                                    document.Id, document.TenantId, document.TenantId);
                                errorCount++;
                                continue;
                            }
                            catch (CosmosException ex)
                            {
                                _logger.LogError(ex, "CosmosDB error for document {DocumentId}. " +
                                    "StatusCode: {StatusCode}, SubStatusCode: {SubStatusCode}",
                                    document.Id, ex.StatusCode, ex.SubStatusCode);
                                errorCount++;
                                continue;
                            }

                            await UpdateReportVersionWithDocumentIdAsync(context, version.Id, versionedDocumentId, cancellationToken);

                            await UpdateStorageMetadataAsync(context, report.Id, document, cancellationToken);
                            _logger.LogDebug("Created Cosmos document for report {ReportId} version {VersionId} with partition key {PartitionKey}",
                                report.Id, version.Id, document.TenantId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error seeding report {ReportId} to Cosmos DB", report.Id);
                        errorCount++;
                    }
                }

                _logger.LogInformation("Cosmos DB seeding completed: {Created} created, {Skipped} skipped, {Errors} errors",
                    createdCount, skippedCount, errorCount);

                if (errorCount > 0)
                {
                    _logger.LogWarning("Cosmos DB seeding completed with {ErrorCount} errors. Check logs for details.", errorCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Critical error during Cosmos DB seeding");
                throw;
            }
        }

        public async Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default)
        {
            if (_cosmosDbService == null)
            {
                _logger.LogDebug("Cosmos DB service not available, returning empty document list");
                return new List<string>();
            }

            try
            {
                var query = "SELECT c.id, c.tenantId FROM c WHERE STARTSWITH(c.id, 'report-data-')";
                var documents = await _cosmosDbService.GetItemsAsync<DocumentIdResult>(query);

                var documentIds = documents.Select(d => d.Id).ToList();
                _logger.LogDebug("Found {Count} existing documents (including versioned) across all partitions", documentIds.Count);

                return documentIds;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting existing document IDs from Cosmos DB, assuming empty");
                return new List<string>();
            }
        }

        private async Task<List<Report>> GetReportsWithDataAsync(ApplicationDbContext context, CancellationToken cancellationToken)
        {
            var reports = await context.Reports
                .IgnoreQueryFilters()
                .ToListAsync(cancellationToken);

            foreach (var report in reports)
            {
                var sections = await context.ReportSections
                    .IgnoreQueryFilters()
                    .Where(rs => rs.ReportId == report.Id)
                    .Include(rs => rs.Fields)
                    .ToListAsync(cancellationToken);

                report.Sections = sections;
            }

            return reports;
        }

        private async Task<VersionedReportDataDocument> CreateVersionedReportDocumentAsync(
            ApplicationDbContext context,
            Report report,
            ReportVersion version,
            SeedData? seedData,
            Dictionary<string, List<SeedReport>>? categoryMapping,
            CancellationToken cancellationToken)
        {
            if (!report.TenantId.HasValue)
            {
                throw new InvalidOperationException($"Report {report.Id} must have a TenantId to be seeded into Cosmos DB.");
            }
            var tenantId = report.TenantId.Value.ToString();

            var sections = report.Sections?.Select(s => new ReportDataSection
            {
                Id = s.Id,
                Title = s.Title,
                Type = s.Type ?? "default",
                Order = s.Order,
                Fields = s.Fields?.Select(f => new ReportDataField
                {
                    Id = f.Id,
                    Name = f.Name,
                    Type = f.Type,
                    Content = f.Content ?? string.Empty,
                    Order = f.Order
                }).ToList() ?? new List<ReportDataField>()
            }).ToList() ?? new List<ReportDataSection>();

            var document = new VersionedReportDataDocument
            {
                Id = GenerateVersionedDocumentId(report.Id, version.Id),
                TenantId = tenantId,
                ReportId = report.Id,
                VersionId = version.Id,
                VersionNumber = version.VersionNumber,
                IsDraft = false,
                ReportName = report.Name,
                ReportNumber = report.ReportNumber,
                Category = report.Category,
                Status = report.Status,
                Author = report.Author,
                ClientId = report.ClientId ?? Guid.Empty,
                ClientName = report.ClientName,
                Sections = sections,
                ComponentDataJson = string.IsNullOrWhiteSpace(version.ComponentDataJson)
                    ? default
                    : JsonDocument.Parse(version.ComponentDataJson).RootElement,
                JsonData = string.IsNullOrWhiteSpace(version.JsonData)
                    ? default
                    : JsonDocument.Parse(version.JsonData).RootElement,
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = version.CreationTime.ToUniversalTime(),
                    CreatedBy = report.Author ?? "system",
                    LastModifiedAt = (version.LastModificationTime ?? version.CreationTime).ToUniversalTime(),
                    LastModifiedBy = report.Author ?? "system"
                }
            };

            if (seedData != null && categoryMapping != null)
            {
                try
                {
                    var matchingOldReport = FindMatchingOldReport(report, categoryMapping);
                    var metricsTimeframe = DetermineMetricsTimeframe(report.Category);
                    var dashboardMetrics = GetMetricsByTimeframe(seedData, metricsTimeframe);
                    var chartData = GenerateSampleChartData(report.Category);

                    var enhancedData = new Dictionary<string, object>
                    {
                        ["dashboardMetrics"] = dashboardMetrics,
                        ["chartData"] = chartData,
                        ["metricsTimeframe"] = metricsTimeframe
                    };

                    // Safely merge with existing JsonData
                    JsonElement finalJsonData;
                    if (document.JsonData.ValueKind != JsonValueKind.Undefined && document.JsonData.ValueKind != JsonValueKind.Null)
                    {
                        using (var ms = new MemoryStream())
                        {
                            using (var writer = new Utf8JsonWriter(ms))
                            {
                                writer.WriteStartObject();

                                // Write properties from existing JSON
                                foreach (var prop in document.JsonData.EnumerateObject())
                                {
                                    prop.WriteTo(writer);
                                }

                                // Write enhanced properties
                                foreach (var kvp in enhancedData)
                                {
                                    writer.WritePropertyName(kvp.Key);
                                    JsonSerializer.Serialize(writer, kvp.Value, JsonOptions);
                                }

                                writer.WriteEndObject();
                            }
                            var jsonString = System.Text.Encoding.UTF8.GetString(ms.ToArray());
                            finalJsonData = JsonDocument.Parse(jsonString).RootElement;
                        }
                    }
                    else
                    {
                        var jsonString = JsonSerializer.Serialize(enhancedData, JsonOptions);
                        finalJsonData = JsonDocument.Parse(jsonString).RootElement;
                    }

                    document.JsonData = finalJsonData;
                    // Removed: document.Metadata.Tags.Add("enhanced_with_seed_data");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to enhance document {DocumentId} with seed data.", document.Id);
                }
            }

            // Final validation remains the same
            var validationResult = ValidateDocumentForCosmosDb(document, report.Id, version.Id);
            if (!validationResult.IsValid)
            {
                var errorDetails = string.Join("; ", validationResult.Errors);
                _logger.LogError("❌ DOCUMENT VALIDATION FAILED for report {ReportId} version {VersionId}. Errors: {ValidationErrors}",
                    report.Id, version.Id, errorDetails);

                foreach (var error in validationResult.Errors)
                {
                    _logger.LogError("  - Validation Error: {Error}", error);
                }

                throw new InvalidOperationException($"Document validation failed: {errorDetails}");
            }

            return document;
        }

        private DocumentValidationResult ValidateDocumentBeforeUpsert(VersionedReportDataDocument document)
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(document.Id))
                errors.Add("Document.Id is null or empty");

            if (string.IsNullOrEmpty(document.TenantId))
                errors.Add("Document.TenantId is null or empty");

            if (document.ReportId == Guid.Empty)
                errors.Add("Document.ReportId is empty GUID");

            if (document.VersionId == Guid.Empty)
                errors.Add("Document.VersionId is empty GUID");

            if (document.Id.Length > 255)
                errors.Add($"Document.Id length {document.Id.Length} exceeds 255 character limit");

            if (document.Id.Contains("/") || document.Id.Contains("\\") || document.Id.Contains("?") || document.Id.Contains("#"))
                errors.Add("Document.Id contains invalid characters (/, \\, ?, #)");

            if (!Guid.TryParse(document.TenantId, out _))
                errors.Add($"Document.TenantId '{document.TenantId}' is not a valid GUID format");

            var documentJson = JsonSerializer.Serialize(document, JsonOptions);
            var documentSize = System.Text.Encoding.UTF8.GetByteCount(documentJson);

            if (documentSize > 2 * 1024 * 1024) // 2MB limit
                errors.Add($"Document size {documentSize} bytes exceeds CosmosDB 2MB limit");

            return new DocumentValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors,
                DocumentSize = documentSize,
                DocumentJson = documentJson
            };
        }

        private DocumentValidationResult ValidateDocumentForCosmosDb(VersionedReportDataDocument document, Guid reportId, Guid versionId)
        {
            var result = new DocumentValidationResult();

            try
            {
                // Validate top-level properties
                ValidateStringField(result, nameof(document.Id), document.Id);
                ValidateStringField(result, nameof(document.TenantId), document.TenantId);
                ValidateGuid(result, nameof(document.ReportId), document.ReportId);
                ValidateGuid(result, nameof(document.VersionId), document.VersionId);
                ValidateStringField(result, nameof(document.ReportName), document.ReportName);
                ValidateStringField(result, nameof(document.ReportNumber), document.ReportNumber);
                ValidateStringField(result, nameof(document.Category), document.Category);
                ValidateStringField(result, nameof(document.Status), document.Status);
                ValidateStringField(result, nameof(document.Author), document.Author);
                ValidateGuid(result, nameof(document.ClientId), document.ClientId);
                ValidateStringField(result, nameof(document.ClientName), document.ClientName);

                // Validate nested sections
                if (document.Sections != null)
                {
                    for (int i = 0; i < document.Sections.Count; i++)
                    {
                        var section = document.Sections[i];
                        var sectionPrefix = $"Sections[{i}]";
                        ValidateGuid(result, $"{sectionPrefix}.Id", section.Id);
                        ValidateStringField(result, $"{sectionPrefix}.Title", section.Title);
                        ValidateStringField(result, $"{sectionPrefix}.Type", section.Type);

                        if (section.Fields != null)
                        {
                            for (int j = 0; j < section.Fields.Count; j++)
                            {
                                var field = section.Fields[j];
                                var fieldPrefix = $"{sectionPrefix}.Fields[{j}]";
                                ValidateGuid(result, $"{fieldPrefix}.Id", field.Id);
                                ValidateStringField(result, $"{fieldPrefix}.Name", field.Name);
                                ValidateStringField(result, $"{fieldPrefix}.Type", field.Type);
                                ValidateStringField(result, $"{fieldPrefix}.Content", field.Content);
                            }
                        }
                    }
                }

                // Validate JSON data fields
                ValidateJsonElementField(result, nameof(document.ComponentDataJson), document.ComponentDataJson);
                ValidateJsonElementField(result, nameof(document.JsonData), document.JsonData);

                // Validate metadata
                if (document.Metadata != null)
                {
                    ValidateStringField(result, "Metadata.CreatedBy", document.Metadata.CreatedBy);
                    ValidateStringField(result, "Metadata.LastModifiedBy", document.Metadata.LastModifiedBy);
                }

                // Check document size
                try
                {
                    var serialized = JsonSerializer.Serialize(document, JsonOptions);
                    var sizeInBytes = System.Text.Encoding.UTF8.GetByteCount(serialized);
                    if (sizeInBytes > 1900000) // 1.9MB to leave some buffer
                    {
                        result.AddError($"Document size ({sizeInBytes} bytes) approaches CosmosDB limit (2MB)");
                    }
                }
                catch (Exception ex)
                {
                    result.AddError($"Failed to serialize document for size check: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Validation process failed: {ex.Message}");
                _logger.LogError(ex, "Error during document validation for report {ReportId} version {VersionId}", reportId, versionId);
            }

            return result;
        }

        private void ValidateGuid(DocumentValidationResult result, string fieldName, Guid guid)
        {
            if (guid == Guid.Empty)
            {
                result.AddError($"{fieldName} is an empty GUID");
            }

            // Warn against non-random GUIDs
            var guidString = guid.ToString();
            if (guidString.Select(c => c).Distinct().Count() < 10)
            {
                result.AddError($"{fieldName} has a non-random pattern: {guidString}");
            }
        }

        private void ValidateStringField(DocumentValidationResult result, string fieldName, string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                result.AddError($"{fieldName} is null or whitespace");
                return;
            }

            if (value.Contains('\0'))
            {
                result.AddError($"{fieldName} contains null character");
            }

            if (value.Length > 50000)
            {
                result.AddError($"{fieldName} is extremely long ({value.Length} characters)");
            }
        }

        private void ValidateJsonElementField(DocumentValidationResult result, string fieldName, JsonElement value)
        {
            if (value.ValueKind == JsonValueKind.Undefined || value.ValueKind == JsonValueKind.Null)
            {
                result.AddError($"{fieldName} is null or undefined");
                return;
            }

            try
            {
                var jsonString = value.GetRawText();
                if (jsonString.Contains('\0'))
                {
                    result.AddError($"{fieldName} contains null character");
                }

                if (jsonString.Length > 50000)
                {
                    result.AddError($"{fieldName} is extremely long ({jsonString.Length} characters)");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"{fieldName} is not valid JSON: {ex.Message}");
            }
        }


        private async Task UpdateReportVersionWithDocumentIdAsync(ApplicationDbContext context, Guid versionId, string documentId, CancellationToken cancellationToken)
        {
            var version = await context.ReportVersions
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);

            if (version != null)
            {
                version.DataDocumentId = documentId;
                await context.SaveChangesAsync(cancellationToken);
            }
        }

        private async Task UpdateStorageMetadataAsync(ApplicationDbContext context, Guid reportId, VersionedReportDataDocument document, CancellationToken cancellationToken)
        {
            var metadata = await context.ReportStorageMetadata
                .FirstOrDefaultAsync(m => m.ReportId == reportId, cancellationToken);

            if (metadata == null)
            {
                metadata = new ReportStorageMetadata
                {
                    ReportId = reportId,
                    DataStorageType = "CosmosDB"
                };
                context.ReportStorageMetadata.Add(metadata);
            }

            metadata.DataDocumentId = document.Id;
            metadata.LastSyncedAt = DateTime.UtcNow;

            await context.SaveChangesAsync(cancellationToken);
        }

        private async Task<SeedData?> LoadSeedDataAsync()
        {
            var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Persistence", "SeedData", "CosmosDBSeedData.json");
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("CosmosDBSeedData.json not found at {FilePath}", filePath);
                return null;
            }

            try
            {
                var json = await File.ReadAllTextAsync(filePath);
                var seedData = JsonSerializer.Deserialize<SeedData>(json, JsonOptions);

                if (seedData == null)
                {
                    _logger.LogWarning("Failed to deserialize CosmosDBSeedData.json");
                    return null;
                }

                _logger.LogInformation("Successfully loaded and deserialized CosmosDBSeedData.json");
                return seedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading or deserializing CosmosDBSeedData.json");
                return null;
            }
        }

        private Dictionary<string, List<SeedReport>> CreateCategoryMapping(List<SeedReport> seedReports)
        {
            var mapping = new Dictionary<string, List<SeedReport>>(StringComparer.OrdinalIgnoreCase);
            foreach (var report in seedReports)
            {
                if (!mapping.ContainsKey(report.Category))
                {
                    mapping[report.Category] = new List<SeedReport>();
                }
                mapping[report.Category].Add(report);
            }
            return mapping;
        }

        private SeedReport? FindMatchingOldReport(Report report, Dictionary<string, List<SeedReport>> categoryMapping)
        {
            if (categoryMapping.TryGetValue(report.Category, out var reportsInCategory))
            {
                return reportsInCategory.FirstOrDefault(r => r.ClientName.Equals(report.ClientName, StringComparison.OrdinalIgnoreCase));
            }
            return null;
        }

        private string DetermineMetricsTimeframe(string category)
        {
            return category.ToLower() switch
            {
                "satisfaction survey" or "user experience" or "healthcare" => "today",
                "performance metrics" or "product improvement" or "financial" or "security" or "wealth management" or "operations" or "development" => "wtd",
                "action plan" or "research" or "risk management" or "ai/ml" => "mtd",
                "channel analysis" or "analytics" or "technology" => "ytd",
                _ => "wtd"
            };
        }

        private object GetMetricsByTimeframe(SeedData seedData, string timeframe)
        {
            return timeframe.ToLower() switch
            {
                "today" => seedData.Today,
                "wtd" => seedData.Wtd,
                "mtd" => seedData.Mtd,
                "ytd" => seedData.Ytd,
                _ => new object()
            };
        }

        private Dictionary<string, object> GenerateSampleChartData(string category)
        {
            return new Dictionary<string, object>
            {
                ["type"] = category.ToLower().Contains("financial") ? "bar" : "line",
                ["labels"] = new[] { "Jan", "Feb", "Mar", "Apr", "May", "Jun" },
                ["datasets"] = new[]
                {
                    new {
                        label = "Metric 1",
                        data = new[] { 12, 19, 3, 5, 2, 3 },
                        borderColor = "rgb(255, 99, 132)",
                        backgroundColor = "rgba(255, 99, 132, 0.5)"
                    },
                    new {
                        label = "Metric 2",
                        data = new[] { 7, 11, 5, 8, 3, 7 },
                        borderColor = "rgb(54, 162, 235)",
                        backgroundColor = "rgba(54, 162, 235, 0.5)"
                    }
                }
            };
        }

        private static string GenerateDocumentId(Guid reportId)
        {
            return $"report-data-{reportId}";
        }

        private static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
        {
            var reportIdClean = reportId.ToString("N");
            var versionIdClean = versionId.ToString("N");
            return $"rpt_{reportIdClean}_v_{versionIdClean}";
        }


        public class DocumentIdResult
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
        }

        public class DocumentValidationResult
        {
            public bool IsValid { get; set; } = true;
            public List<string> Errors { get; set; } = new();
            public long DocumentSize { get; set; }
            public string DocumentJson { get; set; } = string.Empty;

            public void AddError(string error)
            {
                IsValid = false;
                Errors.Add(error);
            }
        }

        public class SeededReportInfo
        {
            public Guid ReportId { get; set; }
            public string DocumentId { get; set; } = string.Empty;
            public string TenantId { get; set; } = string.Empty;
        }

        public class SeededTenantInfo
        {
            public string TenantId { get; set; } = string.Empty;
            public List<SeededReportInfo> Reports { get; set; } = new();
        }

        public class SeedData
        {
            [JsonPropertyName("today")]
            public MetricsPeriod Today { get; set; } = new();

            [JsonPropertyName("wtd")]
            public MetricsPeriod Wtd { get; set; } = new();

            [JsonPropertyName("mtd")]
            public MetricsPeriod Mtd { get; set; } = new();

            [JsonPropertyName("ytd")]
            public MetricsPeriod Ytd { get; set; } = new();

            [JsonPropertyName("reports")]
            public List<SeedReport> Reports { get; set; } = new();
        }

        public class MetricsPeriod
        {
            [JsonPropertyName("metrics")]
            public MetricsData Metrics { get; set; } = new();
        }

        public class MetricsData
        {
            [JsonPropertyName("totalCustomers")]
            public MetricValue TotalCustomers { get; set; } = new();
            [JsonPropertyName("newCustomers")]
            public MetricValue NewCustomers { get; set; } = new();
            [JsonPropertyName("reportsCreated")]
            public MetricValue ReportsCreated { get; set; } = new();
            [JsonPropertyName("revenue")]
            public MetricValue Revenue { get; set; } = new();
        }

        public class MetricValue
        {
            [JsonPropertyName("current")]
            public double Current { get; set; }
            [JsonPropertyName("previousPeriod")]
            public double PreviousPeriod { get; set; }
            [JsonPropertyName("trend")]
            public List<double> Trend { get; set; } = new();
        }

        public class SeedReport
        {
            [JsonPropertyName("reportId")]
            public string ReportId { get; set; } = string.Empty;

            [JsonPropertyName("clientId")]
            public string ClientId { get; set; } = string.Empty;

            [JsonPropertyName("clientName")]
            public string ClientName { get; set; } = string.Empty;

            [JsonPropertyName("reportName")]
            public string ReportName { get; set; } = string.Empty;

            [JsonPropertyName("category")]
            public string Category { get; set; } = string.Empty;

            [JsonPropertyName("slideCount")]
            public int SlideCount { get; set; }

            [JsonPropertyName("createdAt")]
            public DateTime CreatedAt { get; set; }

            [JsonPropertyName("lastModified")]
            public DateTime LastModified { get; set; }

            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;

            [JsonPropertyName("author")]
            public string Author { get; set; } = string.Empty;
        }
    }
}
