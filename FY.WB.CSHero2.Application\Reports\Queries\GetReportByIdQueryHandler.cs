using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Reports.Dtos;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Reports.Queries
{
    public class GetReportByIdQueryHandler : IRequestHandler<GetReportByIdQuery, ReportDto?>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;

        public GetReportByIdQueryHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
        {
            _context = context;
            _currentUserService = currentUserService;
        }

        public async Task<ReportDto?> Handle(GetReportByIdQuery request, CancellationToken cancellationToken)
        {
            var query = _context.Reports.AsQueryable();

            if (_currentUserService.TenantId.HasValue)
            {
                query = query.Where(r => r.TenantId == _currentUserService.TenantId);
            }

            var report = await query
                .Include(r => r.Sections.OrderBy(s => s.Order))
                .ThenInclude(s => s.Fields.OrderBy(f => f.Order))
                .Where(r => r.Id == request.Id)
                .Select(r => new ReportDto
                {
                    Id = r.Id,
                    ReportNumber = r.ReportNumber,
                    ClientId = r.ClientId ?? Guid.Empty,
                    ClientName = r.ClientName,
                    Name = r.Name,
                    Category = r.Category,
                    SlideCount = r.SlideCount,
                    Status = r.Status,
                    Author = r.Author,
                    CreationTime = r.CreationTime,
                    LastModificationTime = r.LastModificationTime,
                    Content = new ReportContentDto
                    {
                        Template = "default", // You might want to get this from the report or template
                        Sections = r.Sections.OrderBy(s => s.Order).Select(s => new ReportSectionDto
                        {
                            Id = s.Id.ToString(),
                            Title = s.Title,
                            SectionTitle = s.Title, // Frontend compatibility
                            Type = s.Type,
                            Content = s.Content,
                            Order = s.Order,
                            Fields = s.Fields.OrderBy(f => f.Order).Select(f => new ReportSectionFieldDto
                            {
                                Id = f.Id.ToString(),
                                Name = f.Name,
                                Type = f.Type,
                                Label = f.Name, // Use Name as Label since Label column doesn't exist
                                Value = f.Content ?? string.Empty, // Map Content to Value for frontend compatibility
                                IsRequired = false, // Default value since IsRequired column doesn't exist
                                Order = f.Order,
                                Metadata = string.Empty // Default value since Metadata column doesn't exist
                            }).ToList()
                        }).ToList()
                    }
                })
                .FirstOrDefaultAsync(cancellationToken);

            return report;
        }
    }
}
