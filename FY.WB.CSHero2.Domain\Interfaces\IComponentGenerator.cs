using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Domain.Interfaces
{
    /// <summary>
    /// Service interface for generating React components from report data
    /// </summary>
    public interface IComponentGenerator
    {
        /// <summary>
        /// Generates React components for an entire report
        /// </summary>
        /// <param name="request">Component generation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Generated component result</returns>
        Task<ComponentResult> GenerateComponentAsync(ComponentRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates a React component for a specific section
        /// </summary>
        /// <param name="request">Section component generation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Generated component result</returns>
        Task<ComponentResult> GenerateSectionComponentAsync(SectionComponentRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates TypeScript type definitions for a component
        /// </summary>
        /// <param name="componentCode">Component code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>TypeScript type definitions</returns>
        Task<string> GenerateTypeDefinitionsAsync(string componentCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates a generated React component for syntax and best practices
        /// </summary>
        /// <param name="componentCode">Component code to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateComponentAsync(string componentCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Optimizes a component for performance
        /// </summary>
        /// <param name="componentCode">Component code to optimize</param>
        /// <param name="options">Optimization options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Optimized component code</returns>
        Task<string> OptimizeComponentAsync(string componentCode, OptimizationOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates component tests
        /// </summary>
        /// <param name="componentCode">Component code to generate tests for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Generated test code</returns>
        Task<string> GenerateComponentTestsAsync(string componentCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets available component templates
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of available component templates</returns>
        Task<List<ComponentTemplate>> GetComponentTemplatesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Previews a component without saving it
        /// </summary>
        /// <param name="request">Preview request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Preview result</returns>
        Task<ComponentPreview> PreviewComponentAsync(ComponentPreviewRequest request, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Request for generating components for an entire report
    /// </summary>
    public class ComponentRequest
    {
        public Guid ReportId { get; set; }
        public string SectionId { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public ComponentGenerationOptions Options { get; set; } = new ComponentGenerationOptions();
        public Template? Template { get; set; }
    }

    /// <summary>
    /// Request for generating a component for a specific section
    /// </summary>
    public class SectionComponentRequest
    {
        public Guid ReportId { get; set; }
        public string SectionId { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        public ComponentGenerationOptions Options { get; set; } = new ComponentGenerationOptions();
        public string? ExistingComponentCode { get; set; }
        public bool PreserveExistingStructure { get; set; } = false;
    }

    /// <summary>
    /// Options for component generation
    /// </summary>
    public class ComponentGenerationOptions
    {
        public string Framework { get; set; } = "NextJS";
        public bool UseTypeScript { get; set; } = true;
        public string StyleFramework { get; set; } = "TailwindCSS";
        public string ComponentLibrary { get; set; } = "Custom";
        public bool IncludeTests { get; set; } = false;
        public bool OptimizeForPerformance { get; set; } = true;
        public bool IncludeAccessibility { get; set; } = true;
        public bool IncludeResponsiveDesign { get; set; } = true;
        public string Theme { get; set; } = "Default";
        public Dictionary<string, object> CustomOptions { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Result of component validation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();
        public List<ValidationWarning> Warnings { get; set; } = new List<ValidationWarning>();
        public List<ValidationSuggestion> Suggestions { get; set; } = new List<ValidationSuggestion>();
        public ComponentQualityScore QualityScore { get; set; } = new ComponentQualityScore();
    }

    /// <summary>
    /// Validation error
    /// </summary>
    public class ValidationError
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public int ColumnNumber { get; set; }
        public string Severity { get; set; } = "Error";
    }

    /// <summary>
    /// Validation warning
    /// </summary>
    public class ValidationWarning
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public string Suggestion { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validation suggestion
    /// </summary>
    public class ValidationSuggestion
    {
        public string Type { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string ProposedChange { get; set; } = string.Empty;
        public int Priority { get; set; } = 1;
    }

    /// <summary>
    /// Component quality score
    /// </summary>
    public class ComponentQualityScore
    {
        public int OverallScore { get; set; } // 0-100
        public int PerformanceScore { get; set; } // 0-100
        public int AccessibilityScore { get; set; } // 0-100
        public int MaintainabilityScore { get; set; } // 0-100
        public int BestPracticesScore { get; set; } // 0-100
        public Dictionary<string, int> DetailedScores { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// Optimization options
    /// </summary>
    public class OptimizationOptions
    {
        public bool MinifyCode { get; set; } = true;
        public bool OptimizeImports { get; set; } = true;
        public bool RemoveUnusedCode { get; set; } = true;
        public bool OptimizePerformance { get; set; } = true;
        public bool OptimizeBundleSize { get; set; } = true;
        public List<string> PreserveFunctions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Component template
    /// </summary>
    public class ComponentTemplate
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string TemplateCode { get; set; } = string.Empty;
        public List<string> RequiredProps { get; set; } = new List<string>();
        public Dictionary<string, object> DefaultProps { get; set; } = new Dictionary<string, object>();
        public List<string> Tags { get; set; } = new List<string>();
    }

    /// <summary>
    /// Component preview request
    /// </summary>
    public class ComponentPreviewRequest
    {
        public string ComponentCode { get; set; } = string.Empty;
        public Dictionary<string, object> SampleData { get; set; } = new Dictionary<string, object>();
        public ComponentGenerationOptions Options { get; set; } = new ComponentGenerationOptions();
    }

    /// <summary>
    /// Component preview result
    /// </summary>
    public class ComponentPreview
    {
        public string RenderedHtml { get; set; } = string.Empty;
        public string PreviewUrl { get; set; } = string.Empty;
        public ValidationResult ValidationResult { get; set; } = new ValidationResult();
        public List<string> RequiredDependencies { get; set; } = new List<string>();
        public ComponentMetadata Metadata { get; set; } = new ComponentMetadata();
    }
}
