"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/providers/toast-provider";
import { ArrowLeft, Plus, Settings, Download, RefreshCw } from "lucide-react";
import { ChartComponent, ChartConfig, ChartData } from "@/components/features/reports/visualization/chart-component";

// Sample data for charts
const generateSampleData = (): ChartData[] => {
  return [
    { name: "Category A", value: Math.floor(Math.random() * 1000) },
    { name: "Category B", value: Math.floor(Math.random() * 1000) },
    { name: "Category C", value: Math.floor(Math.random() * 1000) },
    { name: "Category D", value: Math.floor(Math.random() * 1000) },
    { name: "Category E", value: Math.floor(Math.random() * 1000) }
  ];
};

// Sample time series data
const generateTimeSeriesData = (): ChartData[] => {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  return months.map(month => ({
    name: month,
    value: Math.floor(Math.random() * 1000),
    revenue: Math.floor(Math.random() * 1000),
    expenses: Math.floor(Math.random() * 800),
    profit: Math.floor(Math.random() * 500)
  }));
};

// Sample chart configurations
const sampleChartConfigs: ChartConfig[] = [
  {
    type: "bar",
    title: "Revenue by Category",
    description: "Breakdown of revenue by category",
    data: generateSampleData(),
    showLegend: true,
    showGrid: true,
    showTooltip: true
  },
  {
    type: "line",
    title: "Monthly Trends",
    description: "Performance metrics over time",
    data: generateTimeSeriesData(),
    series: [
      { key: "revenue", name: "Revenue" },
      { key: "expenses", name: "Expenses" },
      { key: "profit", name: "Profit" }
    ],
    showLegend: true,
    showGrid: true,
    showTooltip: true
  },
  {
    type: "pie",
    title: "Budget Allocation",
    description: "Current budget allocation by department",
    data: [
      { name: "Marketing", value: 35 },
      { name: "Sales", value: 25 },
      { name: "R&D", value: 20 },
      { name: "Operations", value: 15 },
      { name: "Admin", value: 5 }
    ],
    showLegend: true,
    showTooltip: true
  },
  {
    type: "area",
    title: "Cumulative Growth",
    description: "Cumulative growth over time",
    data: generateTimeSeriesData(),
    series: [
      { key: "revenue", name: "Revenue" },
      { key: "expenses", name: "Expenses" }
    ],
    stacked: true,
    showLegend: true,
    showGrid: true,
    showTooltip: true
  },
  {
    type: "gauge",
    title: "Project Completion",
    description: "Current project completion percentage",
    data: [
      { name: "Completion", value: 72 }
    ],
    showTooltip: true
  },
  {
    type: "donut",
    title: "Customer Segments",
    description: "Distribution of customers by segment",
    data: [
      { name: "Enterprise", value: 45 },
      { name: "SMB", value: 30 },
      { name: "Startup", value: 15 },
      { name: "Individual", value: 10 }
    ],
    showLegend: true,
    showTooltip: true
  },
  {
    type: "funnel",
    title: "Sales Funnel",
    description: "Conversion rates through the sales pipeline",
    data: [
      { name: "Leads", value: 1000 },
      { name: "Qualified", value: 750 },
      { name: "Proposals", value: 500 },
      { name: "Negotiations", value: 300 },
      { name: "Closed", value: 200 }
    ],
    showTooltip: true
  },
  {
    type: "bar",
    title: "Quarterly Performance",
    description: "Performance by quarter and department",
    data: [
      { name: "Q1", value: 540, marketing: 240, sales: 300 },
      { name: "Q2", value: 620, marketing: 280, sales: 340 },
      { name: "Q3", value: 710, marketing: 320, sales: 390 },
      { name: "Q4", value: 840, marketing: 380, sales: 460 }
    ],
    series: [
      { key: "marketing", name: "Marketing" },
      { key: "sales", name: "Sales" }
    ],
    stacked: true,
    showLegend: true,
    showGrid: true,
    showTooltip: true
  }
];

export default function VisualizationDashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>(sampleChartConfigs);
  const [selectedChartIndex, setSelectedChartIndex] = useState<number | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  // Handle refresh all charts
  const handleRefreshAll = () => {
    setChartConfigs(chartConfigs.map(config => {
      if (config.type === "bar" || config.type === "line" || config.type === "area") {
        return {
          ...config,
          data: config.type === "line" || config.type === "area" 
            ? generateTimeSeriesData() 
            : generateSampleData()
        };
      }
      return config;
    }));
  };

  // Handle chart settings click
  const handleChartSettings = (index: number) => {
    setSelectedChartIndex(index);
    setShowSettings(true);
  };

  // Handle chart refresh
  const handleChartRefresh = (index: number) => {
    const updatedConfigs = [...chartConfigs];
    if (updatedConfigs[index].type === "bar" || updatedConfigs[index].type === "line" || updatedConfigs[index].type === "area") {
      updatedConfigs[index] = {
        ...updatedConfigs[index],
        data: updatedConfigs[index].type === "line" || updatedConfigs[index].type === "area"
          ? generateTimeSeriesData()
          : generateSampleData()
      };
      setChartConfigs(updatedConfigs);
    }
  };

  // Handle chart download
  const handleChartDownload = (index: number) => {
    // In a real implementation, this would generate and download a chart image
    toast({
      title: 'Chart Download',
      description: `Chart "${chartConfigs[index].title}" would be downloaded here`,
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Data Visualization</h1>
          <p className="text-gray-500 mt-2">
            Explore different chart types and visualizations for your reports
          </p>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={() => router.back()}
            className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </button>
          <button
            onClick={handleRefreshAll}
            className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/70 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {chartConfigs.map((chartConfig, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border p-6">
            <ChartComponent
              config={chartConfig}
              height={300}
              onSettingsClick={() => handleChartSettings(index)}
              onRefreshClick={() => handleChartRefresh(index)}
              onDownloadClick={() => handleChartDownload(index)}
            />
          </div>
        ))}
      </div>

      {/* Chart Settings Modal (placeholder) */}
      {showSettings && selectedChartIndex !== null && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full">
            <h2 className="text-xl font-semibold mb-4">
              Chart Settings: {chartConfigs[selectedChartIndex].title}
            </h2>
            <p className="text-gray-500 mb-6">
              In a full implementation, this modal would allow you to customize the chart settings.
            </p>
            <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg mb-6">
              <Settings className="h-12 w-12 mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">Chart settings would be configurable here</p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setShowSettings(false)}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/70"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Chart Button (fixed at bottom right) */}
      <div className="fixed bottom-8 right-8">
        <button
          onClick={() => router.push("/client/reports/create")}
          className="h-14 w-14 bg-primary text-white rounded-full shadow-lg hover:bg-primary/70 flex items-center justify-center"
          title="Create New Report"
        >
          <Plus className="h-6 w-6" />
        </button>
      </div>
    </div>
  );
}
