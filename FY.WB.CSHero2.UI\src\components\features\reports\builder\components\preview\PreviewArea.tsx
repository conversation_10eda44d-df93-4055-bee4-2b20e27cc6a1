"use client";

import { useState, useEffect } from "react";
import { Plus, Download } from "lucide-react";
import { PreviewAreaProps, Section, ChatMessage } from "../../types";
import { SectionCard } from "./SectionCard";
import { ChatInterface } from "../ChatInterface";
import { ExportDialog } from "./ExportDialog";

export function PreviewArea({
  section,
  onUpdateSection,
  allSections = [],
  styleTemplateId = '',
  pulsingSectionId = null,
  activeSectionId,
  onSectionActivate
}: PreviewAreaProps & { activeSectionId: string | null, onSectionActivate: (id: string) => void }) {
  const [isReportRendered, setIsReportRendered] = useState<boolean>(false);
  const [reportHtml, setReportHtml] = useState<string>("");
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [activeChatSection, setActiveChatSection] = useState<string | null>(null);
  const [sections, setSections] = useState<{ id: string; title: string }[]>(allSections);

  // Update sections when allSections changes
  useEffect(() => {
    if (allSections.length > 0) {
      setSections(allSections);
    }
  }, [allSections]);

  // Handle section click
  const handleSectionClick = (id: string) => {
    onSectionActivate(id);
    // Optionally, update the section in parent if needed
    if (onUpdateSection) {
      const clickedSection = allSections.find(s => s.id === id);
      if (clickedSection) {
        onUpdateSection(clickedSection as Section);
      }
    }
  };

  // Handle render report button click
  const handleRenderReport = async () => {
    try {
      // Determine which style template to use
      let templatePath = '/assets/style-templates/minimal/minimal-style-template.html'; // Default
      
      if (styleTemplateId === 'professional') {
        templatePath = '/assets/style-templates/professional/professional-style-template.html';
      } else if (styleTemplateId === 'modern') {
        templatePath = '/assets/style-templates/modern/innocloud-revised-report.html';
      } else if (styleTemplateId === 'minimal') {
        templatePath = '/assets/style-templates/minimal/minimal-style-template.html';
      }
      
      // For development, we'll just fetch the selected style template HTML
      const response = await fetch(templatePath);
      if (response.ok) {
        const html = await response.text();
        setReportHtml(html);
        setIsReportRendered(true);

        // Add a system message to the chat
        const newMessage: ChatMessage = {
          id: `msg-${Date.now()}`,
          content: "Report has been generated. You can now refine specific sections using the chat interface.",
          sender: 'system',
          timestamp: new Date().toISOString()
        };
        setChatMessages(prev => [...prev, newMessage]);
      } else {
        console.error('Error fetching report HTML:', response.statusText);
      }
    } catch (error) {
      console.error('Error rendering report:', error);
    }
  };

  // Handle send message in chat
  const handleSendMessage = (message: string, sectionId?: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}-user`,
      content: message,
      sender: 'user',
      timestamp: new Date().toISOString(),
      sectionId
    };
    setChatMessages(prev => [...prev, userMessage]);

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: ChatMessage = {
        id: `msg-${Date.now()}-system`,
        content: `I've received your feedback${sectionId ? ` for the ${sections.find(s => s.id === sectionId)?.title || 'selected section'}` : ''}. In a real implementation, this would update the prompt data and regenerate the report section.`,
        sender: 'system',
        timestamp: new Date().toISOString(),
        sectionId
      };
      setChatMessages(prev => [...prev, aiMessage]);
    }, 1000);
  };

  // Toggle chat interface
  const handleToggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  // Find a section by ID
  const findSectionById = (id: string): Section | undefined => {
    return allSections.find(s => s.id === id) as Section | undefined;
  };

  if (isReportRendered) {
    return (
      <div className="h-full relative">
        {/* Report Preview */}
        <div className="pt-16 h-full overflow-auto bg-gray-100 p-4">
          <iframe
            srcDoc={reportHtml}
            className="w-full border-0 rounded-lg shadow-lg bg-white"
            style={{ height: 'calc(100vh - 100px)' }}
            title="Report Preview"
          />
        </div>

        {/* Action Buttons - Fixed to the right side, adjusted when chat is open */}
        <div className={`fixed top-20 z-20 flex space-x-2 transition-all duration-300 ${
          isChatOpen ? 'right-[400px]' : 'right-4'
        }`}>
          <button
            onClick={() => setIsExportDialogOpen(true)}
            className="px-4 py-2 bg-primary text-white rounded-full shadow-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={handleToggleChat}
            className="px-4 py-2 bg-primary text-white rounded-full shadow-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            {isChatOpen ? 'Hide Chat' : 'Show Chat'}
          </button>
        </div>

        {/* Export Dialog */}
        <ExportDialog
          isOpen={isExportDialogOpen}
          onClose={() => setIsExportDialogOpen(false)}
          onExport={(format) => {
            console.log(`Exporting report in ${format} format`);
            // In a real implementation, this would trigger the actual export process
          }}
        />

        {/* Chat Interface - Slide in from right */}
        {isChatOpen && (
          <div className="fixed right-0 top-16 bottom-0 w-96 z-10 animate-slide-in-right">
            <div className="h-full flex flex-col">
              <div className="bg-white p-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold">AI Chat Assistant</h2>
              </div>
              <div className="flex-1 overflow-hidden">
                <ChatInterface
                  messages={chatMessages}
                  onSendMessage={handleSendMessage}
                  activeSection={activeChatSection}
                  sections={allSections.map(s => ({ id: s.id, title: s.title }))}
                  onSectionChange={setActiveChatSection}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto bg-gray-100 p-8">
      <div className="preview-container mx-auto p-8" style={{ maxWidth: '850px' }}>
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-4">AI-Driven Report Builder</h2>
          <p className="text-gray-600 mb-2">
            This editor uses fields to construct an AI prompt. Fill in the fields and click "Render Report" to generate your report.
          </p>
          <p className="text-sm text-primary mb-6">
            <span className="inline-flex items-center mr-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Note:
            </span>
            Click on a section to edit its fields. Only the active section (highlighted in blue) is editable.
          </p>
        </div>
        
        <div className="text-left mb-8">
          {allSections.length > 0 ? (
            <div className="space-y-6">
              {allSections.map((sectionItem) => {
                const fullSection = findSectionById(sectionItem.id);
                if (!fullSection) return null;
                
                return (
                  <SectionCard
                    key={fullSection.id}
                    section={fullSection}
                    isActive={activeSectionId === fullSection.id}
                    isPulsing={pulsingSectionId === fullSection.id}
                    onSectionClick={handleSectionClick}
                    onUpdateSection={onUpdateSection || (() => {})}
                  />
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
              <p className="text-gray-500">No sections available. Add sections from the sidebar.</p>
            </div>
          )}
        </div>
        
        <div className="text-center">
          <button
            onClick={handleRenderReport}
            className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Render Report
          </button>
          <p className="text-sm text-gray-500 mt-2">
            This will generate your report based on the data you've entered.
          </p>
        </div>
      </div>
    </div>
  );
}
