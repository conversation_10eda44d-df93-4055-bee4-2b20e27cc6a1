"use client";

import { useState, useEffect } from "react";
import { Plus, Download } from "lucide-react";
import { PreviewAreaProps, Section, ChatMessage, StyleOptions } from "../../types";
import { SectionCard } from "./SectionCard";
import { ChatInterface } from "../ChatInterface";
import { ExportDialog } from "./ExportDialog";

export function PreviewArea({
  section,
  onUpdateSection,
  allSections = [],
  styleTemplateId = '',
  pulsingSectionId = null,
  activeSectionId,
  onSectionActivate,
  styles = {}
}: PreviewAreaProps & {
  activeSectionId: string | null,
  onSectionActivate: (id: string) => void,
  styles?: StyleOptions
}) {
  const [isReportRendered, setIsReportRendered] = useState<boolean>(false);
  const [reportHtml, setReportHtml] = useState<string>("");
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [activeChatSection, setActiveChatSection] = useState<string | null>(null);
  const [sections, setSections] = useState<{ id: string; title: string }[]>(allSections);

  // Update sections when allSections changes
  useEffect(() => {
    if (allSections.length > 0) {
      setSections(allSections);
    }
  }, [allSections]);

  // Update preview when styles change (if report is already rendered)
  useEffect(() => {
    console.log(`[PreviewArea] Styles changed:`, styles);

    if (isReportRendered && reportHtml) {
      console.log(`[PreviewArea] Re-applying styles to rendered report`);
      // Re-apply styles to the existing HTML
      const baseHtml = reportHtml.replace(/<style>.*?<\/style>/gs, ''); // Remove existing custom styles
      const styledHtml = applyCustomStyles(baseHtml);
      setReportHtml(styledHtml);
    }
  }, [styles, isReportRendered]);

  // Function to apply custom styles to HTML template
  const applyCustomStyles = (html: string): string => {
    console.log(`[PreviewArea] Applying custom styles:`, styles);
    let modifiedHtml = html;

    // Apply layout styles if they exist
    if (styles.layout) {
      console.log(`[PreviewArea] Applying layout styles:`, styles.layout);
      const layout = styles.layout;

      // Apply page size and orientation
      if (layout.pageSize || layout.orientation) {
        const pageStyle = `
          @page {
            ${layout.pageSize ? `size: ${layout.pageSize === 'custom' ?
              (layout.pageSizeDimensions ? `${layout.pageSizeDimensions.width} ${layout.pageSizeDimensions.height}` : 'A4') :
              layout.pageSize}` : ''}
            ${layout.orientation ? ` ${layout.orientation}` : ''};
            ${layout.margins ? `margin: ${layout.margins.top || '1in'} ${layout.margins.right || '1in'} ${layout.margins.bottom || '1in'} ${layout.margins.left || '1in'};` : ''}
          }
        `;

        // Insert page styles into the HTML
        modifiedHtml = modifiedHtml.replace('</head>', `<style>${pageStyle}</style></head>`);
      }

      // Apply column layout
      if (layout.columns && layout.columns.count && parseInt(layout.columns.count.toString()) > 1) {
        const columnStyle = `
          .content-area, .main-content, .report-content {
            column-count: ${layout.columns.count};
            ${layout.columns.gutterWidth ? `column-gap: ${layout.columns.gutterWidth === 'custom' ? layout.columns.customGutterWidth || '0.5in' : layout.columns.gutterWidth + 'in'};` : ''}
            ${layout.columns.separator === 'line' ? 'column-rule: 1px solid #ccc;' : ''}
          }
        `;

        modifiedHtml = modifiedHtml.replace('</head>', `<style>${columnStyle}</style></head>`);
      }

      // Apply title page styles
      if (layout.titlePage) {
        const titlePage = layout.titlePage;
        let titlePageStyle = '';

        if (titlePage.title) {
          titlePageStyle += `
            .report-title, h1.title {
              ${titlePage.title.font ? `font-family: ${titlePage.title.font};` : ''}
              ${titlePage.title.size ? `font-size: ${titlePage.title.size};` : ''}
              ${titlePage.title.alignment ? `text-align: ${titlePage.title.alignment};` : ''}
            }
          `;
        }

        if (titlePage.subtitle && titlePage.subtitle.show) {
          titlePageStyle += `
            .report-subtitle, .subtitle {
              display: block;
              ${titlePage.subtitle.font ? `font-family: ${titlePage.subtitle.font};` : ''}
              ${titlePage.subtitle.size ? `font-size: ${titlePage.subtitle.size};` : ''}
            }
          `;
        } else if (titlePage.subtitle && !titlePage.subtitle.show) {
          titlePageStyle += `.report-subtitle, .subtitle { display: none; }`;
        }

        if (titlePage.author && titlePage.author.show) {
          titlePageStyle += `
            .report-author, .author {
              display: block;
              ${titlePage.author.format === 'grid' ? 'display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;' : ''}
            }
          `;
        } else if (titlePage.author && !titlePage.author.show) {
          titlePageStyle += `.report-author, .author { display: none; }`;
        }

        if (titlePage.date && titlePage.date.show) {
          titlePageStyle += `.report-date, .date { display: block; }`;
        } else if (titlePage.date && !titlePage.date.show) {
          titlePageStyle += `.report-date, .date { display: none; }`;
        }

        if (titlePageStyle) {
          modifiedHtml = modifiedHtml.replace('</head>', `<style>${titlePageStyle}</style></head>`);
        }
      }

      // Apply header/footer styles
      if (layout.headerFooter) {
        const headerFooter = layout.headerFooter;
        let headerFooterStyle = '';

        if (headerFooter.headerContent === 'none') {
          headerFooterStyle += `.page-header, header { display: none; }`;
        }

        if (headerFooter.footerContent === 'none') {
          headerFooterStyle += `.page-footer, footer { display: none; }`;
        }

        if (headerFooter.pageNumbering) {
          const numbering = headerFooter.pageNumbering;
          if (numbering.style === 'none') {
            headerFooterStyle += `.page-number { display: none; }`;
          } else {
            headerFooterStyle += `
              .page-number {
                ${numbering.position ? `text-align: ${numbering.position.includes('center') ? 'center' : numbering.position.includes('right') ? 'right' : 'left'};` : ''}
              }
            `;
          }
        }

        if (headerFooterStyle) {
          modifiedHtml = modifiedHtml.replace('</head>', `<style>${headerFooterStyle}</style></head>`);
        }
      }

      // Apply cover design styles
      if (layout.coverDesign) {
        const cover = layout.coverDesign;
        let coverStyle = '';

        if (cover.background) {
          switch (cover.background) {
            case 'solid':
              coverStyle += `.cover-page, .title-page { background-color: #f8f9fa; }`;
              break;
            case 'gradient':
              coverStyle += `.cover-page, .title-page { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }`;
              break;
            case 'none':
              coverStyle += `.cover-page, .title-page { background: none; }`;
              break;
          }
        }

        if (cover.titlePosition) {
          const position = cover.titlePosition;
          if (position === 'centered') {
            coverStyle += `.cover-title, .title-page .report-title { text-align: center; margin: auto; }`;
          } else if (position === 'left') {
            coverStyle += `.cover-title, .title-page .report-title { text-align: left; }`;
          } else if (position === 'right') {
            coverStyle += `.cover-title, .title-page .report-title { text-align: right; }`;
          }
        }

        if (coverStyle) {
          modifiedHtml = modifiedHtml.replace('</head>', `<style>${coverStyle}</style></head>`);
        }
      }
    }

    return modifiedHtml;
  };

  // Handle section click
  const handleSectionClick = (id: string) => {
    onSectionActivate(id);
    // Optionally, update the section in parent if needed
    if (onUpdateSection) {
      const clickedSection = allSections.find(s => s.id === id);
      if (clickedSection) {
        onUpdateSection(clickedSection as Section);
      }
    }
  };

  // Handle render report button click
  const handleRenderReport = async () => {
    try {
      // Determine which style template to use
      let templatePath = '/assets/style-templates/minimal/minimal-style-template.html'; // Default

      if (styleTemplateId === 'professional') {
        templatePath = '/assets/style-templates/professional/professional-style-template.html';
      } else if (styleTemplateId === 'modern') {
        templatePath = '/assets/style-templates/modern/innocloud-revised-report.html';
      } else if (styleTemplateId === 'minimal') {
        templatePath = '/assets/style-templates/minimal/minimal-style-template.html';
      }

      // For development, we'll just fetch the selected style template HTML
      const response = await fetch(templatePath);
      if (response.ok) {
        const html = await response.text();

        // Apply custom styles to the HTML template
        const styledHtml = applyCustomStyles(html);

        setReportHtml(styledHtml);
        setIsReportRendered(true);

        // Add a system message to the chat
        const newMessage: ChatMessage = {
          id: `msg-${Date.now()}`,
          content: "Report has been generated with your custom styles applied. You can now refine specific sections using the chat interface.",
          sender: 'system',
          timestamp: new Date().toISOString()
        };
        setChatMessages(prev => [...prev, newMessage]);
      } else {
        console.error('Error fetching report HTML:', response.statusText);
      }
    } catch (error) {
      console.error('Error rendering report:', error);
    }
  };

  // Handle send message in chat
  const handleSendMessage = (message: string, sectionId?: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}-user`,
      content: message,
      sender: 'user',
      timestamp: new Date().toISOString(),
      sectionId
    };
    setChatMessages(prev => [...prev, userMessage]);

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: ChatMessage = {
        id: `msg-${Date.now()}-system`,
        content: `I've received your feedback${sectionId ? ` for the ${sections.find(s => s.id === sectionId)?.title || 'selected section'}` : ''}. In a real implementation, this would update the prompt data and regenerate the report section.`,
        sender: 'system',
        timestamp: new Date().toISOString(),
        sectionId
      };
      setChatMessages(prev => [...prev, aiMessage]);
    }, 1000);
  };

  // Toggle chat interface
  const handleToggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  // Find a section by ID
  const findSectionById = (id: string): Section | undefined => {
    return allSections.find(s => s.id === id) as Section | undefined;
  };

  if (isReportRendered) {
    return (
      <div className="h-full relative">
        {/* Report Preview */}
        <div className="pt-16 h-full overflow-auto bg-gray-100 p-4">
          <iframe
            srcDoc={reportHtml}
            className="w-full border-0 rounded-lg shadow-lg bg-white"
            style={{ height: 'calc(100vh - 100px)' }}
            title="Report Preview"
          />
        </div>

        {/* Action Buttons - Fixed to the right side, adjusted when chat is open */}
        <div className={`fixed top-20 z-20 flex space-x-2 transition-all duration-300 ${
          isChatOpen ? 'right-[400px]' : 'right-4'
        }`}>
          <button
            onClick={() => setIsExportDialogOpen(true)}
            className="px-4 py-2 bg-primary text-white rounded-full shadow-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={handleToggleChat}
            className="px-4 py-2 bg-primary text-white rounded-full shadow-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          >
            {isChatOpen ? 'Hide Chat' : 'Show Chat'}
          </button>
        </div>

        {/* Export Dialog */}
        <ExportDialog
          isOpen={isExportDialogOpen}
          onClose={() => setIsExportDialogOpen(false)}
          onExport={(format) => {
            console.log(`Exporting report in ${format} format`);
            // In a real implementation, this would trigger the actual export process
          }}
        />

        {/* Chat Interface - Slide in from right */}
        {isChatOpen && (
          <div className="fixed right-0 top-16 bottom-0 w-96 z-10 animate-slide-in-right">
            <div className="h-full flex flex-col">
              <div className="bg-white p-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold">AI Chat Assistant</h2>
              </div>
              <div className="flex-1 overflow-hidden">
                <ChatInterface
                  messages={chatMessages}
                  onSendMessage={handleSendMessage}
                  activeSection={activeChatSection}
                  sections={allSections.map(s => ({ id: s.id, title: s.title }))}
                  onSectionChange={setActiveChatSection}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto bg-gray-100 p-8">
      <div className="preview-container mx-auto p-8" style={{ maxWidth: '850px' }}>
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-4">AI-Driven Report Builder</h2>
          <p className="text-gray-600 mb-2">
            This editor uses fields to construct an AI prompt. Fill in the fields and click "Render Report" to generate your report.
          </p>
          <p className="text-sm text-primary mb-6">
            <span className="inline-flex items-center mr-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Note:
            </span>
            Click on a section to edit its fields. Only the active section (highlighted in blue) is editable.
          </p>
        </div>
        
        <div className="text-left mb-8">
          {allSections.length > 0 ? (
            <div className="space-y-6">
              {allSections.map((sectionItem) => {
                const fullSection = findSectionById(sectionItem.id);
                if (!fullSection) return null;
                
                return (
                  <SectionCard
                    key={fullSection.id}
                    section={fullSection}
                    isActive={activeSectionId === fullSection.id}
                    isPulsing={pulsingSectionId === fullSection.id}
                    onSectionClick={handleSectionClick}
                    onUpdateSection={onUpdateSection || (() => {})}
                  />
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 border border-dashed border-gray-300 rounded-lg">
              <p className="text-gray-500">No sections available. Add sections from the sidebar.</p>
            </div>
          )}
        </div>
        
        <div className="text-center">
          <button
            onClick={handleRenderReport}
            className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/70 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Render Report
          </button>
          <p className="text-sm text-gray-500 mt-2">
            This will generate your report based on the data you've entered.
          </p>

          {/* Style Debug Info */}
          {styles && Object.keys(styles).length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-left">
              <h4 className="text-sm font-semibold text-blue-800 mb-2">🎨 Custom Styles Applied:</h4>
              <div className="text-xs text-blue-700 space-y-1">
                {styles.layout?.pageSize && (
                  <div>📄 Page Size: {styles.layout.pageSize}</div>
                )}
                {styles.layout?.orientation && (
                  <div>🔄 Orientation: {styles.layout.orientation}</div>
                )}
                {styles.layout?.columns?.count && parseInt(styles.layout.columns.count.toString()) > 1 && (
                  <div>📰 Columns: {styles.layout.columns.count}</div>
                )}
                {styles.layout?.titlePage?.title?.font && (
                  <div>🔤 Title Font: {styles.layout.titlePage.title.font}</div>
                )}
                {styles.layout?.headerFooter?.pageNumbering?.style && (
                  <div>🔢 Page Numbers: {styles.layout.headerFooter.pageNumbering.style}</div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
