﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [AspNetRoles] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [Templates] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Category] nvarchar(50) NOT NULL,
    [ThumbnailUrl] nvarchar(255) NOT NULL,
    [IsPublic] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DefaultStyleJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [StyleDocumentId] nvarchar(max) NULL,
    [UsageCount] int NOT NULL DEFAULT 0,
    [Version] nvarchar(20) NOT NULL DEFAULT N'1.0.0',
    [IsActive] bit NOT NULL DEFAULT CAST(1 AS bit),
    [EstimatedCompletionTimeMinutes] int NOT NULL DEFAULT 30,
    [Tags] nvarchar(max) NOT NULL DEFAULT N'[]',
    [Sections] nvarchar(max) NOT NULL DEFAULT N'[]',
    [Fields] nvarchar(max) NOT NULL DEFAULT N'[]',
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Templates] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [TenantProfiles] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Status] nvarchar(20) NOT NULL,
    [Phone] nvarchar(20) NOT NULL,
    [Company] nvarchar(100) NOT NULL,
    [Subscription] nvarchar(50) NOT NULL,
    [LastLoginTime] datetime2 NOT NULL,
    [BillingCycle] nvarchar(20) NOT NULL,
    [NextBillingDate] datetime2 NOT NULL,
    [SubscriptionStatus] nvarchar(20) NOT NULL,
    [PaymentMethod] nvarchar(max) NOT NULL DEFAULT N'{}',
    [BillingAddress] nvarchar(max) NOT NULL DEFAULT N'{}',
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_TenantProfiles] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [Uploads] (
    [Id] uniqueidentifier NOT NULL,
    [Filename] nvarchar(255) NOT NULL,
    [Size] bigint NOT NULL,
    [ContentType] nvarchar(100) NOT NULL,
    [StoragePath] nvarchar(1000) NOT NULL,
    [StorageProvider] nvarchar(50) NULL,
    [ExternalUrl] nvarchar(2000) NULL,
    [Checksum] nvarchar(100) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Uploads] PRIMARY KEY ([Id]),
    CONSTRAINT [CK_Upload_Size] CHECK ([Size] >= 0)
);
GO

CREATE TABLE [AspNetRoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] uniqueidentifier NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetRoleClaims_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUsers] (
    [Id] uniqueidentifier NOT NULL,
    [CompanyName] nvarchar(100) NULL,
    [CompanyUrl] nvarchar(100) NULL,
    [TenantId] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    [DeleterId] uniqueidentifier NULL,
    [IsAdmin] bit NOT NULL,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    CONSTRAINT [PK_AspNetUsers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUsers_TenantProfiles_TenantId] FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id]) ON DELETE NO ACTION
);
GO

CREATE TABLE [Clients] (
    [Id] uniqueidentifier NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Status] nvarchar(50) NOT NULL,
    [CompanyName] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NULL,
    [Address] nvarchar(200) NULL,
    [CompanySize] nvarchar(50) NULL,
    [Industry] nvarchar(100) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NOT NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Clients] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Clients_TenantProfiles_TenantId] FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id]) ON DELETE NO ACTION
);
GO

CREATE TABLE [Forms] (
    [Id] uniqueidentifier NOT NULL,
    [Title] nvarchar(200) NOT NULL,
    [CustomerName] nvarchar(100) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Category] nvarchar(50) NOT NULL,
    [Priority] nvarchar(20) NOT NULL,
    [Description] nvarchar(2000) NOT NULL,
    [Date] datetime2 NOT NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NOT NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Forms] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Forms_TenantProfiles_TenantId] FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id]) ON DELETE NO ACTION
);
GO

CREATE TABLE [Invoices] (
    [Id] uniqueidentifier NOT NULL,
    [OrderNumber] nvarchar(20) NOT NULL,
    [Type] nvarchar(20) NOT NULL,
    [Plans] nvarchar(200) NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Status] nvarchar(20) NOT NULL,
    [Date] datetime2 NOT NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NOT NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Invoices_TenantProfiles_TenantId] FOREIGN KEY ([TenantId]) REFERENCES [TenantProfiles] ([Id]) ON DELETE NO ACTION
);
GO

CREATE TABLE [AspNetUserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] uniqueidentifier NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUserClaims_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] uniqueidentifier NOT NULL,
    CONSTRAINT [PK_AspNetUserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_AspNetUserLogins_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserRoles] (
    [UserId] uniqueidentifier NOT NULL,
    [RoleId] uniqueidentifier NOT NULL,
    CONSTRAINT [PK_AspNetUserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_AspNetUserRoles_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserTokens] (
    [UserId] uniqueidentifier NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_AspNetUserTokens_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [Reports] (
    [Id] uniqueidentifier NOT NULL,
    [ReportNumber] nvarchar(20) NOT NULL,
    [ClientId] uniqueidentifier NOT NULL,
    [ClientName] nvarchar(100) NOT NULL,
    [Name] nvarchar(200) NOT NULL,
    [Category] nvarchar(50) NOT NULL,
    [SlideCount] int NOT NULL,
    [Status] nvarchar(50) NOT NULL,
    [Author] nvarchar(100) NOT NULL,
    [TemplateId] uniqueidentifier NULL,
    [CurrentVersionId] uniqueidentifier NULL,
    [ReportType] nvarchar(50) NOT NULL DEFAULT N'Standard',
    [DataDocumentId] nvarchar(100) NULL,
    [ComponentsBlobId] nvarchar(255) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    [TenantId] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    [DeleterId] uniqueidentifier NULL,
    [DeletionTime] datetime2 NULL,
    CONSTRAINT [PK_Reports] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Reports_Clients_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Clients] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Reports_Templates_TemplateId] FOREIGN KEY ([TemplateId]) REFERENCES [Templates] ([Id]) ON DELETE SET NULL
);
GO

CREATE TABLE [ReportSections] (
    [Id] uniqueidentifier NOT NULL,
    [ReportId] uniqueidentifier NOT NULL,
    [Title] nvarchar(200) NOT NULL,
    [Type] nvarchar(50) NOT NULL,
    [Order] int NOT NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ReportSections] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ReportSections_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [Reports] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [ReportStyles] (
    [Id] uniqueidentifier NOT NULL,
    [ReportId] uniqueidentifier NOT NULL,
    [Theme] nvarchar(50) NOT NULL DEFAULT N'modern',
    [ColorScheme] nvarchar(50) NOT NULL DEFAULT N'blue',
    [Typography] nvarchar(50) NOT NULL DEFAULT N'sans-serif',
    [Spacing] nvarchar(50) NOT NULL DEFAULT N'normal',
    [LayoutOptionsJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [TypographyOptionsJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [StructureOptionsJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [ContentOptionsJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [VisualOptionsJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ReportStyles] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ReportStyles_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [Reports] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [ReportVersions] (
    [Id] uniqueidentifier NOT NULL,
    [ReportId] uniqueidentifier NOT NULL,
    [VersionNumber] int NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [ComponentDataJson] nvarchar(max) NOT NULL,
    [JsonData] nvarchar(max) NOT NULL,
    [IsCurrent] bit NOT NULL DEFAULT CAST(0 AS bit),
    [ComponentDataSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [JsonDataSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [StyleDocumentId] nvarchar(max) NULL,
    [DataBlobPath] nvarchar(max) NULL,
    [IsDataInBlob] bit NOT NULL,
    [DataDocumentId] nvarchar(100) NULL,
    [ComponentsBlobId] nvarchar(255) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ReportVersions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ReportVersions_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [Reports] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [ReportSectionFields] (
    [Id] uniqueidentifier NOT NULL,
    [SectionId] uniqueidentifier NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [Type] nvarchar(50) NOT NULL,
    [Content] nvarchar(max) NOT NULL,
    [Order] int NOT NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ReportSectionFields] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ReportSectionFields_ReportSections_SectionId] FOREIGN KEY ([SectionId]) REFERENCES [ReportSections] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [ComponentDefinitions] (
    [Id] uniqueidentifier NOT NULL,
    [ReportVersionId] uniqueidentifier NOT NULL,
    [SectionId] nvarchar(100) NOT NULL,
    [SectionName] nvarchar(200) NOT NULL,
    [ComponentCode] nvarchar(max) NOT NULL,
    [TypeDefinitions] nvarchar(max) NOT NULL,
    [ImportsJson] nvarchar(max) NOT NULL DEFAULT N'[]',
    [MetadataJson] nvarchar(max) NOT NULL DEFAULT N'{}',
    [GeneratedAt] datetime2 NOT NULL,
    [GeneratedBy] uniqueidentifier NOT NULL,
    [ComponentHash] nvarchar(100) NOT NULL,
    [ComponentSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [IsValid] bit NOT NULL DEFAULT CAST(1 AS bit),
    [ValidationErrors] nvarchar(max) NOT NULL DEFAULT N'',
    [Framework] nvarchar(50) NOT NULL DEFAULT N'NextJS',
    [StyleFramework] nvarchar(50) NOT NULL DEFAULT N'TailwindCSS',
    [AssetBlobPath] nvarchar(max) NULL,
    [StyleDocumentId] nvarchar(max) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ComponentDefinitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ComponentDefinitions_ReportVersions_ReportVersionId] FOREIGN KEY ([ReportVersionId]) REFERENCES [ReportVersions] ([Id]) ON DELETE CASCADE
);
GO

CREATE INDEX [IX_AspNetRoleClaims_RoleId] ON [AspNetRoleClaims] ([RoleId]);
GO

CREATE UNIQUE INDEX [RoleNameIndex] ON [AspNetRoles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;
GO

CREATE INDEX [IX_AspNetUserClaims_UserId] ON [AspNetUserClaims] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserLogins_UserId] ON [AspNetUserLogins] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserRoles_RoleId] ON [AspNetUserRoles] ([RoleId]);
GO

CREATE INDEX [EmailIndex] ON [AspNetUsers] ([NormalizedEmail]);
GO

CREATE INDEX [IX_AspNetUsers_Email] ON [AspNetUsers] ([Email]);
GO

CREATE INDEX [IX_AspNetUsers_TenantId] ON [AspNetUsers] ([TenantId]);
GO

CREATE UNIQUE INDEX [UserNameIndex] ON [AspNetUsers] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;
GO

CREATE INDEX [IX_Clients_TenantId] ON [Clients] ([TenantId]);
GO

CREATE UNIQUE INDEX [IX_Clients_TenantId_Email] ON [Clients] ([TenantId], [Email]);
GO

CREATE INDEX [IX_ComponentDefinitions_ComponentHash] ON [ComponentDefinitions] ([ComponentHash]);
GO

CREATE INDEX [IX_ComponentDefinitions_CreationTime] ON [ComponentDefinitions] ([CreationTime]);
GO

CREATE INDEX [IX_ComponentDefinitions_IsValid] ON [ComponentDefinitions] ([IsValid]);
GO

CREATE INDEX [IX_ComponentDefinitions_ReportVersionId] ON [ComponentDefinitions] ([ReportVersionId]);
GO

CREATE UNIQUE INDEX [IX_ComponentDefinitions_ReportVersionId_SectionId] ON [ComponentDefinitions] ([ReportVersionId], [SectionId]);
GO

CREATE INDEX [IX_ComponentDefinitions_SectionId] ON [ComponentDefinitions] ([SectionId]);
GO

CREATE INDEX [IX_Forms_Category] ON [Forms] ([Category]);
GO

CREATE INDEX [IX_Forms_Category_Priority_Date] ON [Forms] ([Category], [Priority], [Date]);
GO

CREATE INDEX [IX_Forms_Date] ON [Forms] ([Date]);
GO

CREATE INDEX [IX_Forms_Email] ON [Forms] ([Email]);
GO

CREATE INDEX [IX_Forms_Priority] ON [Forms] ([Priority]);
GO

CREATE INDEX [IX_Forms_TenantId] ON [Forms] ([TenantId]);
GO

CREATE UNIQUE INDEX [IX_Invoices_OrderNumber] ON [Invoices] ([OrderNumber]);
GO

CREATE INDEX [IX_Invoices_TenantId] ON [Invoices] ([TenantId]);
GO

CREATE INDEX [IX_Reports_ClientId] ON [Reports] ([ClientId]);
GO

CREATE INDEX [IX_Reports_CurrentVersionId] ON [Reports] ([CurrentVersionId]);
GO

CREATE UNIQUE INDEX [IX_Reports_ReportNumber] ON [Reports] ([ReportNumber]);
GO

CREATE INDEX [IX_Reports_ReportType] ON [Reports] ([ReportType]);
GO

CREATE INDEX [IX_Reports_TemplateId] ON [Reports] ([TemplateId]);
GO

CREATE INDEX [IX_Reports_TemplateId_ReportType] ON [Reports] ([TemplateId], [ReportType]);
GO

CREATE INDEX [IX_ReportSectionFields_SectionId] ON [ReportSectionFields] ([SectionId]);
GO

CREATE INDEX [IX_ReportSectionFields_SectionId_Order] ON [ReportSectionFields] ([SectionId], [Order]);
GO

CREATE INDEX [IX_ReportSections_ReportId] ON [ReportSections] ([ReportId]);
GO

CREATE INDEX [IX_ReportSections_ReportId_Order] ON [ReportSections] ([ReportId], [Order]);
GO

CREATE INDEX [IX_ReportStyles_ColorScheme] ON [ReportStyles] ([ColorScheme]);
GO

CREATE INDEX [IX_ReportStyles_CreationTime] ON [ReportStyles] ([CreationTime]);
GO

CREATE UNIQUE INDEX [IX_ReportStyles_ReportId] ON [ReportStyles] ([ReportId]);
GO

CREATE INDEX [IX_ReportStyles_Theme] ON [ReportStyles] ([Theme]);
GO

CREATE INDEX [IX_ReportVersions_CreationTime] ON [ReportVersions] ([CreationTime]);
GO

CREATE INDEX [IX_ReportVersions_IsCurrent] ON [ReportVersions] ([IsCurrent]);
GO

CREATE INDEX [IX_ReportVersions_ReportId] ON [ReportVersions] ([ReportId]);
GO

CREATE UNIQUE INDEX [IX_ReportVersions_ReportId_VersionNumber] ON [ReportVersions] ([ReportId], [VersionNumber]);
GO

CREATE INDEX [IX_Templates_Category] ON [Templates] ([Category]);
GO

CREATE INDEX [IX_Templates_IsActive] ON [Templates] ([IsActive]);
GO

CREATE INDEX [IX_Templates_IsPublic] ON [Templates] ([IsPublic]);
GO

CREATE INDEX [IX_Templates_IsPublic_IsActive_Category] ON [Templates] ([IsPublic], [IsActive], [Category]);
GO

CREATE INDEX [IX_Templates_Name_Category] ON [Templates] ([Name], [Category]);
GO

CREATE INDEX [IX_TenantProfiles_Company] ON [TenantProfiles] ([Company]);
GO

CREATE INDEX [IX_TenantProfiles_Company_SubscriptionStatus] ON [TenantProfiles] ([Company], [SubscriptionStatus]);
GO

CREATE UNIQUE INDEX [IX_TenantProfiles_Email] ON [TenantProfiles] ([Email]);
GO

CREATE INDEX [IX_TenantProfiles_Status] ON [TenantProfiles] ([Status]);
GO

CREATE INDEX [IX_TenantProfiles_Status_Subscription] ON [TenantProfiles] ([Status], [Subscription]);
GO

CREATE INDEX [IX_TenantProfiles_Subscription] ON [TenantProfiles] ([Subscription]);
GO

CREATE INDEX [IX_TenantProfiles_SubscriptionStatus] ON [TenantProfiles] ([SubscriptionStatus]);
GO

CREATE INDEX [IX_Uploads_ContentType] ON [Uploads] ([ContentType]);
GO

CREATE INDEX [IX_Uploads_Filename] ON [Uploads] ([Filename]);
GO

CREATE UNIQUE INDEX [IX_Uploads_Filename_StoragePath] ON [Uploads] ([Filename], [StoragePath]);
GO

CREATE INDEX [IX_Uploads_Filename_StorageProvider] ON [Uploads] ([Filename], [StorageProvider]);
GO

CREATE INDEX [IX_Uploads_StorageProvider] ON [Uploads] ([StorageProvider]);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250604004236_01_Foundation', N'8.0.15');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [ReportVersions] ADD [StorageStrategy] nvarchar(50) NOT NULL DEFAULT N'SQL';
GO

ALTER TABLE [ReportVersions] ADD [StylesBlobId] nvarchar(255) NULL;
GO

ALTER TABLE [ReportVersions] ADD [StylesSize] bigint NOT NULL DEFAULT CAST(0 AS bigint);
GO

ALTER TABLE [ReportSections] ADD [CosmosDocumentId] nvarchar(255) NULL;
GO

ALTER TABLE [ReportSections] ADD [IsMigratedToCosmos] bit NOT NULL DEFAULT CAST(0 AS bit);
GO

ALTER TABLE [ReportSections] ADD [IsModifiedFromTemplate] bit NOT NULL DEFAULT CAST(0 AS bit);
GO

ALTER TABLE [ReportSections] ADD [MigrationDate] datetime2 NULL;
GO

ALTER TABLE [ReportSections] ADD [TemplateSourceSectionId] uniqueidentifier NULL;
GO

ALTER TABLE [ReportSectionFields] ADD [IsMigratedToCosmos] bit NOT NULL DEFAULT CAST(0 AS bit);
GO

ALTER TABLE [ReportSectionFields] ADD [IsModifiedFromTemplate] bit NOT NULL DEFAULT CAST(0 AS bit);
GO

ALTER TABLE [ReportSectionFields] ADD [MigrationDate] datetime2 NULL;
GO

ALTER TABLE [ReportSectionFields] ADD [TemplateSourceFieldId] uniqueidentifier NULL;
GO

ALTER TABLE [Reports] ADD [DraftDataDocumentId] nvarchar(255) NULL;
GO

ALTER TABLE [Reports] ADD [IsDraft] bit NOT NULL DEFAULT CAST(1 AS bit);
GO

ALTER TABLE [Reports] ADD [LastSavedAt] datetime2 NULL;
GO

CREATE TABLE [ReportStorageMetadata] (
    [Id] uniqueidentifier NOT NULL,
    [ReportId] uniqueidentifier NOT NULL,
    [StorageStrategy] nvarchar(50) NOT NULL DEFAULT N'SQL',
    [MigrationStatus] nvarchar(50) NOT NULL DEFAULT N'NotMigrated',
    [MigrationStartDate] datetime2 NULL,
    [MigrationCompletedDate] datetime2 NULL,
    [MigrationErrorMessage] nvarchar(1000) NULL,
    [SqlStorageSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [CosmosStorageSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [BlobStorageSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [TotalStorageSize] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [AccessCount] bigint NOT NULL DEFAULT CAST(0 AS bigint),
    [LastAccessDate] datetime2 NULL,
    [PerformanceMetrics] nvarchar(max) NULL,
    [OptimizationMetadata] nvarchar(max) NULL,
    [CreationTime] datetime2 NOT NULL,
    [CreatorId] uniqueidentifier NULL,
    [LastModificationTime] datetime2 NULL,
    [LastModifierId] uniqueidentifier NULL,
    CONSTRAINT [PK_ReportStorageMetadata] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ReportStorageMetadata_Reports_ReportId] FOREIGN KEY ([ReportId]) REFERENCES [Reports] ([Id]) ON DELETE CASCADE
);
GO

CREATE INDEX [IX_ReportVersions_StorageStrategy] ON [ReportVersions] ([StorageStrategy]);
GO

CREATE INDEX [IX_ReportSections_IsMigratedToCosmos] ON [ReportSections] ([IsMigratedToCosmos]);
GO

CREATE INDEX [IX_ReportSections_TemplateSourceSectionId] ON [ReportSections] ([TemplateSourceSectionId]);
GO

CREATE INDEX [IX_ReportSectionFields_IsMigratedToCosmos] ON [ReportSectionFields] ([IsMigratedToCosmos]);
GO

CREATE INDEX [IX_ReportSectionFields_TemplateSourceFieldId] ON [ReportSectionFields] ([TemplateSourceFieldId]);
GO

CREATE INDEX [IX_Reports_IsDraft] ON [Reports] ([IsDraft]);
GO

CREATE INDEX [IX_Reports_LastSavedAt] ON [Reports] ([LastSavedAt]);
GO

CREATE INDEX [IX_ReportStorageMetadata_LastAccessDate] ON [ReportStorageMetadata] ([LastAccessDate]);
GO

CREATE INDEX [IX_ReportStorageMetadata_MigrationStatus] ON [ReportStorageMetadata] ([MigrationStatus]);
GO

CREATE UNIQUE INDEX [IX_ReportStorageMetadata_ReportId] ON [ReportStorageMetadata] ([ReportId]);
GO

CREATE INDEX [IX_ReportStorageMetadata_StorageStrategy] ON [ReportStorageMetadata] ([StorageStrategy]);
GO

CREATE INDEX [IX_ReportStorageMetadata_TotalStorageSize] ON [ReportStorageMetadata] ([TotalStorageSize]);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250605031045_OptimizeMultiStorageStructure', N'8.0.15');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

DROP INDEX [IX_Forms_Category] ON [Forms];
GO

DROP INDEX [IX_Forms_Category_Priority_Date] ON [Forms];
GO

DROP INDEX [IX_Forms_Date] ON [Forms];
GO

DROP INDEX [IX_Forms_Email] ON [Forms];
GO

DROP INDEX [IX_Forms_Priority] ON [Forms];
GO

DROP INDEX [IX_Forms_TenantId] ON [Forms];
GO

DROP INDEX [IX_Invoices_OrderNumber] ON [Invoices];
GO

DROP INDEX [IX_Invoices_TenantId] ON [Invoices];
GO

DROP INDEX [IX_Uploads_ContentType] ON [Uploads];
GO

DROP INDEX [IX_Uploads_Filename] ON [Uploads];
GO

DROP INDEX [IX_Uploads_Filename_StoragePath] ON [Uploads];
GO

DROP INDEX [IX_Uploads_Filename_StorageProvider] ON [Uploads];
GO

DROP INDEX [IX_Uploads_StorageProvider] ON [Uploads];
GO

DROP TABLE [Forms];
GO

DROP TABLE [Invoices];
GO

DROP TABLE [Uploads];
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250606025225_RemoveDeprecatedTables', N'8.0.15');
GO

COMMIT;
GO

