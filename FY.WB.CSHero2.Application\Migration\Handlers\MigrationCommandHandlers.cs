using MediatR;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Migration.Commands;
using FY.WB.CSHero2.Application.Models.Migration;
using FY.WB.CSHero2.Application.Services.Migration;

namespace FY.WB.CSHero2.Application.Migration.Handlers
{
    /// <summary>
    /// Handler for migrating all reports command
    /// </summary>
    public class MigrateAllReportsCommandHandler : IRequestHandler<MigrateAllReportsCommand, MigrationResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<MigrateAllReportsCommandHandler> _logger;

        public MigrateAllReportsCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<MigrateAllReportsCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationResult> Handle(MigrateAllReportsCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting migration of all reports. InitiatedBy: {InitiatedBy}, CorrelationId: {CorrelationId}",
                request.InitiatedBy, request.CorrelationId);

            try
            {
                var result = await _migrationService.MigrateAllReportsAsync(request.Options, cancellationToken);
                
                _logger.LogInformation("Migration of all reports completed. Success: {Success}, TotalReports: {TotalReports}, Duration: {Duration}",
                    result.Success, result.TotalReports, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during migration of all reports");
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for migrating specific report command
    /// </summary>
    public class MigrateReportCommandHandler : IRequestHandler<MigrateReportCommand, MigrationResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<MigrateReportCommandHandler> _logger;

        public MigrateReportCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<MigrateReportCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationResult> Handle(MigrateReportCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting migration of report {ReportId}. InitiatedBy: {InitiatedBy}",
                request.ReportId, request.InitiatedBy);

            try
            {
                var result = await _migrationService.MigrateReportAsync(request.ReportId, request.Options, cancellationToken);
                
                _logger.LogInformation("Migration of report {ReportId} completed. Success: {Success}",
                    request.ReportId, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during migration of report {ReportId}", request.ReportId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for migrating specific report version command
    /// </summary>
    public class MigrateReportVersionCommandHandler : IRequestHandler<MigrateReportVersionCommand, MigrationResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<MigrateReportVersionCommandHandler> _logger;

        public MigrateReportVersionCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<MigrateReportVersionCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationResult> Handle(MigrateReportVersionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting migration of report {ReportId}, version {VersionId}. InitiatedBy: {InitiatedBy}",
                request.ReportId, request.VersionId, request.InitiatedBy);

            try
            {
                var result = await _migrationService.MigrateReportVersionAsync(
                    request.ReportId, request.VersionId, request.Options, cancellationToken);
                
                _logger.LogInformation("Migration of report {ReportId}, version {VersionId} completed. Success: {Success}",
                    request.ReportId, request.VersionId, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during migration of report {ReportId}, version {VersionId}", 
                    request.ReportId, request.VersionId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for validating migration command
    /// </summary>
    public class ValidateMigrationCommandHandler : IRequestHandler<ValidateMigrationCommand, ValidationResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<ValidateMigrationCommandHandler> _logger;

        public ValidateMigrationCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<ValidateMigrationCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<ValidationResult> Handle(ValidateMigrationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting validation of migration for report {ReportId}, version {VersionId}. InitiatedBy: {InitiatedBy}",
                request.ReportId, request.VersionId, request.InitiatedBy);

            try
            {
                ValidationResult result;
                
                if (request.VersionId.HasValue)
                {
                    result = await _migrationService.ValidateVersionMigrationAsync(
                        request.ReportId, request.VersionId.Value, cancellationToken);
                }
                else
                {
                    result = await _migrationService.ValidateMigrationAsync(request.ReportId, cancellationToken);
                }
                
                _logger.LogInformation("Validation of migration for report {ReportId} completed. IsValid: {IsValid}",
                    request.ReportId, result.IsValid);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during validation of migration for report {ReportId}", request.ReportId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for rollback migration command
    /// </summary>
    public class RollbackMigrationCommandHandler : IRequestHandler<RollbackMigrationCommand, RollbackResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<RollbackMigrationCommandHandler> _logger;

        public RollbackMigrationCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<RollbackMigrationCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<RollbackResult> Handle(RollbackMigrationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting rollback of migration for report {ReportId}, version {VersionId}. InitiatedBy: {InitiatedBy}, Reason: {Reason}",
                request.ReportId, request.VersionId, request.InitiatedBy, request.Reason);

            try
            {
                RollbackResult result;
                
                if (request.VersionId.HasValue)
                {
                    result = await _migrationService.RollbackVersionMigrationAsync(
                        request.ReportId, request.VersionId.Value, cancellationToken);
                }
                else
                {
                    result = await _migrationService.RollbackReportMigrationAsync(request.ReportId, cancellationToken);
                }
                
                _logger.LogInformation("Rollback of migration for report {ReportId} completed. Success: {Success}",
                    request.ReportId, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during rollback of migration for report {ReportId}", request.ReportId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for performing dry run command
    /// </summary>
    public class PerformDryRunCommandHandler : IRequestHandler<PerformDryRunCommand, DryRunResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<PerformDryRunCommandHandler> _logger;

        public PerformDryRunCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<PerformDryRunCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<DryRunResult> Handle(PerformDryRunCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting dry run migration. ReportId: {ReportId}, InitiatedBy: {InitiatedBy}",
                request.ReportId, request.InitiatedBy);

            try
            {
                var result = await _migrationService.PerformDryRunAsync(request.ReportId, request.Options, cancellationToken);
                
                _logger.LogInformation("Dry run migration completed. ReportsToMigrate: {ReportsToMigrate}",
                    result.ReportsToMigrate);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during dry run migration");
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for canceling migration command
    /// </summary>
    public class CancelMigrationCommandHandler : IRequestHandler<CancelMigrationCommand, CancellationResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<CancelMigrationCommandHandler> _logger;

        public CancelMigrationCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<CancelMigrationCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<CancellationResult> Handle(CancelMigrationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting cancellation of migration operation {OperationId}. InitiatedBy: {InitiatedBy}, Reason: {Reason}, Force: {Force}",
                request.OperationId, request.InitiatedBy, request.Reason, request.Force);

            try
            {
                var result = await _migrationService.CancelMigrationAsync(request.OperationId, cancellationToken);
                
                _logger.LogInformation("Cancellation of migration operation {OperationId} completed. Success: {Success}",
                    request.OperationId, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cancellation of migration operation {OperationId}", request.OperationId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for cleanup failed migration command
    /// </summary>
    public class CleanupFailedMigrationCommandHandler : IRequestHandler<CleanupFailedMigrationCommand, CleanupResult>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<CleanupFailedMigrationCommandHandler> _logger;

        public CleanupFailedMigrationCommandHandler(
            IReportDataMigrationService migrationService,
            ILogger<CleanupFailedMigrationCommandHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<CleanupResult> Handle(CleanupFailedMigrationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting cleanup of failed migration artifacts. ReportId: {ReportId}, OperationId: {OperationId}, InitiatedBy: {InitiatedBy}",
                request.ReportId, request.OperationId, request.InitiatedBy);

            try
            {
                var result = await _migrationService.CleanupFailedMigrationAsync(request.ReportId, cancellationToken);
                
                _logger.LogInformation("Cleanup of failed migration artifacts completed. Success: {Success}, ItemsCleanedUp: {ItemsCleanedUp}",
                    result.Success, result.ItemsCleanedUp);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cleanup of failed migration artifacts");
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for validate storage connectivity command
    /// </summary>
    public class ValidateStorageConnectivityCommandHandler : IRequestHandler<ValidateStorageConnectivityCommand, StorageValidationResult>
    {
        private readonly IMigrationValidationService _validationService;
        private readonly ILogger<ValidateStorageConnectivityCommandHandler> _logger;

        public ValidateStorageConnectivityCommandHandler(
            IMigrationValidationService validationService,
            ILogger<ValidateStorageConnectivityCommandHandler> logger)
        {
            _validationService = validationService;
            _logger = logger;
        }

        public async Task<StorageValidationResult> Handle(ValidateStorageConnectivityCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting validation of storage connectivity. DeepValidation: {DeepValidation}, TimeoutSeconds: {TimeoutSeconds}",
                request.DeepValidation, request.TimeoutSeconds);

            try
            {
                var result = await _validationService.ValidateStorageConnectivityAsync(cancellationToken);
                
                _logger.LogInformation("Validation of storage connectivity completed. AllStorageAccessible: {AllStorageAccessible}",
                    result.AllStorageAccessible);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during validation of storage connectivity");
                throw;
            }
        }
    }
}