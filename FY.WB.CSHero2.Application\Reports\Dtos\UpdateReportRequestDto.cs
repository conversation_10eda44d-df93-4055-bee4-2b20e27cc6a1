using System;
using System.Collections.Generic;

namespace FY.WB.CSHero2.Application.Reports.Dtos
{
    public class UpdateReportRequestDto
    {
        public Guid Id { get; set; } // Id of the Report to update
        // ReportNumber, ClientId, ClientName are typically not updatable for an existing report.
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        
        // Report structure - added to support updating sections and fields
        public ReportContentDto? Content { get; set; }
        public object? Style { get; set; }
    }
}
