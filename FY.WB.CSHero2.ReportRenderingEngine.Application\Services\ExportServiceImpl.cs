using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Comprehensive export service implementation supporting multiple formats
    /// </summary>
    public class ExportServiceImpl : IExportService
    {
        private readonly ApplicationDbContext _context;
        private readonly IReportService _reportService;
        private readonly IVersioningService _versioningService;
        private readonly ILogger<ExportServiceImpl> _logger;
        private readonly Dictionary<string, IExportProvider> _exportProviders;

        public ExportServiceImpl(
            ApplicationDbContext context,
            IReportService reportService,
            IVersioningService versioningService,
            ILogger<ExportServiceImpl> logger,
            IEnumerable<IExportProvider> exportProviders)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _versioningService = versioningService ?? throw new ArgumentNullException(nameof(versioningService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _exportProviders = exportProviders?.ToDictionary(p => p.Format, p => p)
                ?? new Dictionary<string, IExportProvider>();
        }

        /// <summary>
        /// Exports a report to PDF format
        /// </summary>
        public async Task<byte[]> ExportToPdfAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting report {ReportId} to PDF", reportId);

                var provider = GetExportProvider("PDF");
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var result = await provider.ExportAsync(exportData, options, cancellationToken);

                await LogExportHistoryAsync(reportId, "PDF", options, result.Length, "Success", cancellationToken);

                _logger.LogInformation("Successfully exported report {ReportId} to PDF ({Size} bytes)",
                    reportId, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportId} to PDF", reportId);
                await LogExportHistoryAsync(reportId, "PDF", options, 0, "Failed", cancellationToken, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Exports a report to PowerPoint format
        /// </summary>
        public async Task<byte[]> ExportToPowerPointAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting report {ReportId} to PowerPoint", reportId);

                var provider = GetExportProvider("PowerPoint");
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var result = await provider.ExportAsync(exportData, options, cancellationToken);

                await LogExportHistoryAsync(reportId, "PowerPoint", options, result.Length, "Success", cancellationToken);

                _logger.LogInformation("Successfully exported report {ReportId} to PowerPoint ({Size} bytes)",
                    reportId, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportId} to PowerPoint", reportId);
                await LogExportHistoryAsync(reportId, "PowerPoint", options, 0, "Failed", cancellationToken, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Exports a report to Word format
        /// </summary>
        public async Task<byte[]> ExportToWordAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting report {ReportId} to Word", reportId);

                var provider = GetExportProvider("Word");
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var result = await provider.ExportAsync(exportData, options, cancellationToken);

                await LogExportHistoryAsync(reportId, "Word", options, result.Length, "Success", cancellationToken);

                _logger.LogInformation("Successfully exported report {ReportId} to Word ({Size} bytes)",
                    reportId, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportId} to Word", reportId);
                await LogExportHistoryAsync(reportId, "Word", options, 0, "Failed", cancellationToken, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Exports a report to Excel format
        /// </summary>
        public async Task<byte[]> ExportToExcelAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting report {ReportId} to Excel", reportId);

                var provider = GetExportProvider("Excel");
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var result = await provider.ExportAsync(exportData, options, cancellationToken);

                await LogExportHistoryAsync(reportId, "Excel", options, result.Length, "Success", cancellationToken);

                _logger.LogInformation("Successfully exported report {ReportId} to Excel ({Size} bytes)",
                    reportId, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportId} to Excel", reportId);
                await LogExportHistoryAsync(reportId, "Excel", options, 0, "Failed", cancellationToken, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Exports a report to HTML format (static)
        /// </summary>
        public async Task<string> ExportToHtmlAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting report {ReportId} to HTML", reportId);

                var provider = GetExportProvider("HTML");
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var result = await provider.ExportAsync(exportData, options, cancellationToken);
                var htmlContent = Encoding.UTF8.GetString(result);

                await LogExportHistoryAsync(reportId, "HTML", options, result.Length, "Success", cancellationToken);

                _logger.LogInformation("Successfully exported report {ReportId} to HTML ({Size} bytes)",
                    reportId, result.Length);

                return htmlContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportId} to HTML", reportId);
                await LogExportHistoryAsync(reportId, "HTML", options, 0, "Failed", cancellationToken, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Gets a list of supported export formats
        /// </summary>
        public async Task<List<string>> GetSupportedFormatsAsync()
        {
            var formats = _exportProviders.Keys.ToList();
            _logger.LogDebug("Retrieved {Count} supported export formats", formats.Count);
            return await Task.FromResult(formats);
        }

        /// <summary>
        /// Gets export capabilities for a specific format
        /// </summary>
        public async Task<ExportCapabilities> GetExportCapabilitiesAsync(string format)
        {
            try
            {
                var provider = GetExportProvider(format);
                var capabilities = await provider.GetCapabilitiesAsync();

                _logger.LogDebug("Retrieved capabilities for format {Format}", format);
                return capabilities;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting capabilities for format {Format}", format);
                return new ExportCapabilities { Format = format };
            }
        }

        /// <summary>
        /// Previews an export without generating the full file
        /// </summary>
        public async Task<ExportPreview> PreviewExportAsync(Guid reportId, string format, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating export preview for report {ReportId} in format {Format}", reportId, format);

                var provider = GetExportProvider(format);
                var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);

                var preview = await provider.PreviewAsync(exportData, options, cancellationToken);

                _logger.LogDebug("Generated export preview for report {ReportId}: {PageCount} pages, {FileSize} bytes estimated",
                    reportId, preview.EstimatedPageCount, preview.EstimatedFileSize);

                return preview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating export preview for report {ReportId}", reportId);
                return new ExportPreview
                {
                    Format = format,
                    Warnings = new List<string> { $"Preview generation failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Exports multiple reports to a single archive
        /// </summary>
        public async Task<byte[]> ExportMultipleReportsAsync(List<Guid> reportIds, string format, ExportOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Exporting {Count} reports to {Format} archive", reportIds.Count, format);

                var archiveBuilder = new ExportArchiveBuilder();

                foreach (var reportId in reportIds)
                {
                    try
                    {
                        var exportData = await PrepareExportDataAsync(reportId, options, cancellationToken);
                        var provider = GetExportProvider(format);
                        var fileData = await provider.ExportAsync(exportData, options, cancellationToken);

                        var report = await _context.Reports.FindAsync(reportId);
                        var fileName = $"{report?.Name ?? reportId.ToString()}.{GetFileExtension(format)}";

                        archiveBuilder.AddFile(fileName, fileData);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to export report {ReportId} in batch export", reportId);
                        // Continue with other reports
                    }
                }

                var archive = archiveBuilder.BuildZipArchive();

                _logger.LogInformation("Successfully created archive with {FileCount} files ({Size} bytes)",
                    archiveBuilder.FileCount, archive.Length);

                return archive;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating multi-report export archive");
                throw;
            }
        }

        /// <summary>
        /// Gets export history for a report
        /// </summary>
        public async Task<List<ExportHistoryEntry>> GetExportHistoryAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting export history for report {ReportId}", reportId);

                // In a full implementation, this would query an ExportHistory table
                // For now, return a placeholder implementation
                var history = new List<ExportHistoryEntry>();

                _logger.LogDebug("Retrieved {Count} export history entries for report {ReportId}",
                    history.Count, reportId);

                return await Task.FromResult(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting export history for report {ReportId}", reportId);
                return new List<ExportHistoryEntry>();
            }
        }

        /// <summary>
        /// Schedules a report export for later processing
        /// </summary>
        public async Task<Guid> ScheduleExportAsync(Guid reportId, string format, ExportOptions options, DateTime scheduledFor, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Scheduling export for report {ReportId} in format {Format} at {ScheduledFor}",
                    reportId, format, scheduledFor);

                var jobId = Guid.NewGuid();

                // In a full implementation, this would create an export job in the database
                // and use a background service to process it

                _logger.LogInformation("Scheduled export job {JobId} for report {ReportId}", jobId, reportId);

                return await Task.FromResult(jobId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling export for report {ReportId}", reportId);
                throw;
            }
        }

        #region Private Helper Methods

        private IExportProvider GetExportProvider(string format)
        {
            if (!_exportProviders.TryGetValue(format, out var provider))
            {
                throw new NotSupportedException($"Export format '{format}' is not supported");
            }
            return provider;
        }

        private async Task<ExportData> PrepareExportDataAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken)
        {
            // Get the report
            var report = await _context.Reports
                .Include(r => r.Template)
                .Include(r => r.Client)
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report == null)
            {
                throw new InvalidOperationException($"Report with ID {reportId} not found");
            }

            // Get the current version
            var currentVersion = await _versioningService.GetCurrentVersionAsync(reportId, cancellationToken);

            // Get component data
            var componentData = currentVersion.GetComponentData<ComponentResult>() ?? new ComponentResult();

            // Get report data
            var reportData = currentVersion.GetReportData();

            return new ExportData
            {
                Report = report,
                Version = currentVersion,
                ComponentData = componentData,
                ReportData = reportData,
                Options = options
            };
        }

        private async Task LogExportHistoryAsync(
            Guid reportId,
            string format,
            ExportOptions options,
            long fileSize,
            string status,
            CancellationToken cancellationToken,
            string? errorMessage = null)
        {
            try
            {
                // In a full implementation, this would save to an ExportHistory table
                _logger.LogInformation("Export {Status}: Report {ReportId}, Format {Format}, Size {Size} bytes",
                    status, reportId, format, fileSize);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log export history for report {ReportId}", reportId);
            }
        }

        private string GetFileExtension(string format)
        {
            return format.ToLower() switch
            {
                "pdf" => "pdf",
                "word" => "docx",
                "powerpoint" => "pptx",
                "excel" => "xlsx",
                "html" => "html",
                _ => "bin"
            };
        }

        #endregion
    }

    /// <summary>
    /// Data prepared for export
    /// </summary>
    public class ExportData
    {
        public Report Report { get; set; } = null!;
        public ReportVersion Version { get; set; } = null!;
        public ComponentResult ComponentData { get; set; } = null!;
        public Dictionary<string, object> ReportData { get; set; } = new Dictionary<string, object>();
        public ExportOptions Options { get; set; } = new ExportOptions();
    }

    /// <summary>
    /// Interface for export providers
    /// </summary>
    public interface IExportProvider
    {
        string Format { get; }
        Task<byte[]> ExportAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken = default);
        Task<ExportCapabilities> GetCapabilitiesAsync();
        Task<ExportPreview> PreviewAsync(ExportData data, ExportOptions options, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Helper class for building export archives
    /// </summary>
    public class ExportArchiveBuilder
    {
        private readonly List<(string fileName, byte[] data)> _files = new List<(string, byte[])>();

        public int FileCount => _files.Count;

        public void AddFile(string fileName, byte[] data)
        {
            _files.Add((fileName, data));
        }

        public byte[] BuildZipArchive()
        {
            // In a full implementation, this would use System.IO.Compression.ZipArchive
            // For now, return a placeholder
            using var memoryStream = new MemoryStream();

            // Placeholder implementation - would create actual ZIP archive
            var archiveContent = $"Archive with {_files.Count} files";
            var archiveBytes = Encoding.UTF8.GetBytes(archiveContent);

            return archiveBytes;
        }
    }
}
