using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for Report Rendering Engine entities
    /// Provides optimized data access patterns for the enhanced domain model
    /// </summary>
    public class ReportRenderingEngineRepository
    {
        private readonly ApplicationDbContext _context;

        public ReportRenderingEngineRepository(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region Template Repository Methods

        /// <summary>
        /// Gets templates with optimized loading for browsing scenarios
        /// </summary>
        public async Task<List<Template>> GetPublicTemplatesAsync(
            string? category = null,
            string? searchTerm = null,
            int skip = 0,
            int take = 20,
            CancellationToken cancellationToken = default)
        {
            var query = _context.Templates
                .Where(t => t.IsPublic && t.IsActive);

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(t => t.Category == category);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(t => 
                    t.Name.Contains(searchTerm) || 
                    t.Description.Contains(searchTerm) ||
                    t.Tags.Contains(searchTerm));
            }

            return await query
                .OrderByDescending(t => t.UsageCount)
                .ThenBy(t => t.Name)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets template with full details including sections and fields
        /// </summary>
        public async Task<Template?> GetTemplateWithDetailsAsync(
            Guid templateId,
            CancellationToken cancellationToken = default)
        {
            return await _context.Templates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);
        }

        /// <summary>
        /// Gets template usage statistics
        /// </summary>
        public async Task<Dictionary<string, int>> GetTemplateUsageStatsAsync(
            CancellationToken cancellationToken = default)
        {
            return await _context.Templates
                .Where(t => t.IsActive)
                .GroupBy(t => t.Category)
                .Select(g => new { Category = g.Key, Count = g.Sum(t => t.UsageCount) })
                .ToDictionaryAsync(x => x.Category, x => x.Count, cancellationToken);
        }

        #endregion

        #region Report Repository Methods

        /// <summary>
        /// Gets report with current version and component definitions
        /// </summary>
        public async Task<Report?> GetReportWithCurrentVersionAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            return await _context.Reports
                .Include(r => r.Template)
                .Include(r => r.Versions.Where(v => v.IsCurrent))
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
        }

        /// <summary>
        /// Gets reports with pagination and filtering optimized for dashboard scenarios
        /// </summary>
        public async Task<(List<Report> Reports, int TotalCount)> GetReportsPagedAsync(
            Guid? clientId = null,
            string? status = null,
            string? category = null,
            DateTime? createdAfter = null,
            int page = 1,
            int pageSize = 20,
            CancellationToken cancellationToken = default)
        {
            var query = _context.Reports
                .Include(r => r.Template)
                .AsQueryable();

            if (clientId.HasValue)
            {
                query = query.Where(r => r.ClientId == clientId.Value);
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(r => r.Status == status);
            }

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(r => r.Category == category);
            }

            if (createdAfter.HasValue)
            {
                query = query.Where(r => r.CreationTime >= createdAfter.Value);
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var reports = await query
                .OrderByDescending(r => r.LastModificationTime ?? r.CreationTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (reports, totalCount);
        }

        /// <summary>
        /// Gets report statistics for analytics
        /// </summary>
        public async Task<Dictionary<string, object>> GetReportStatisticsAsync(
            Guid? clientId = null,
            CancellationToken cancellationToken = default)
        {
            var query = _context.Reports.AsQueryable();

            if (clientId.HasValue)
            {
                query = query.Where(r => r.ClientId == clientId.Value);
            }

            var stats = await query
                .GroupBy(r => 1)
                .Select(g => new
                {
                    TotalReports = g.Count(),
                    TemplateBasedReports = g.Count(r => r.TemplateId.HasValue),
                    CustomReports = g.Count(r => !r.TemplateId.HasValue),
                    ReportsThisMonth = g.Count(r => r.CreationTime >= DateTime.UtcNow.AddMonths(-1)),
                    DraftReports = g.Count(r => r.Status == "Draft"),
                    PublishedReports = g.Count(r => r.Status == "Published"),
                    ArchivedReports = g.Count(r => r.Status == "Archived")
                })
                .FirstOrDefaultAsync(cancellationToken);

            if (stats == null)
            {
                return new Dictionary<string, object>();
            }

            return new Dictionary<string, object>
            {
                ["totalReports"] = stats.TotalReports,
                ["templateBasedReports"] = stats.TemplateBasedReports,
                ["customReports"] = stats.CustomReports,
                ["reportsThisMonth"] = stats.ReportsThisMonth,
                ["reportsByStatus"] = new Dictionary<string, int>
                {
                    ["Draft"] = stats.DraftReports,
                    ["Published"] = stats.PublishedReports,
                    ["Archived"] = stats.ArchivedReports
                }
            };
        }

        #endregion

        #region Version Repository Methods

        /// <summary>
        /// Gets all versions for a report with component counts
        /// </summary>
        public async Task<List<ReportVersion>> GetReportVersionsAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            return await _context.ReportVersions
                .Where(v => v.ReportId == reportId)
                .OrderByDescending(v => v.VersionNumber)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets version with all component definitions
        /// </summary>
        public async Task<ReportVersion?> GetVersionWithComponentsAsync(
            Guid reportId,
            int versionNumber,
            CancellationToken cancellationToken = default)
        {
            return await _context.ReportVersions
                .FirstOrDefaultAsync(v => v.ReportId == reportId && v.VersionNumber == versionNumber, cancellationToken);
        }

        /// <summary>
        /// Gets version storage statistics
        /// </summary>
        public async Task<Dictionary<int, long>> GetVersionStorageStatsAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            return await _context.ReportVersions
                .Where(v => v.ReportId == reportId)
                .Select(v => new { v.VersionNumber, TotalSize = v.ComponentDataSize + v.JsonDataSize })
                .ToDictionaryAsync(x => x.VersionNumber, x => x.TotalSize, cancellationToken);
        }

        /// <summary>
        /// Cleans up old versions based on retention policy
        /// </summary>
        public async Task<int> CleanupOldVersionsAsync(
            Guid reportId,
            int maxVersionsToKeep = 10,
            CancellationToken cancellationToken = default)
        {
            var versionsToDelete = await _context.ReportVersions
                .Where(v => v.ReportId == reportId && !v.IsCurrent)
                .OrderByDescending(v => v.VersionNumber)
                .Skip(maxVersionsToKeep - 1) // Keep one less since current version is always kept
                .ToListAsync(cancellationToken);

            if (versionsToDelete.Any())
            {
                _context.ReportVersions.RemoveRange(versionsToDelete);
                await _context.SaveChangesAsync(cancellationToken);
            }

            return versionsToDelete.Count;
        }

        #endregion

        // NOTE: Component Definition Repository Methods removed as ComponentDefinition entity is deprecated
        // If component functionality is needed in the future, these methods can be restored
        // with a new component entity design

        #region Bulk Operations
        
        // NOTE: ComponentDefinition bulk operations removed due to deprecated entity

        #endregion
    }
}
