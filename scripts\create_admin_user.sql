-- Create admin user directly in the database
-- This bypasses the seeding issue and <NAME_EMAIL> user

SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;

USE [FY.WB.CSHeroSQL];

-- First, check if the user already exists
IF NOT EXISTS (SELECT 1 FROM [AspNetUsers] WHERE [Email] = '<EMAIL>')
BEGIN
    -- Generate a new GUID for the admin user
    DECLARE @AdminUserId UNIQUEIDENTIFIER = NEWID();
    DECLARE @AdminRoleId UNIQUEIDENTIFIER = NEWID();
    
    -- Create Admin role if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM [AspNetRoles] WHERE [Name] = 'Admin')
    BEGIN
        INSERT INTO [AspNetRoles] ([Id], [Name], [NormalizedName], [ConcurrencyStamp])
        VALUES (@AdminRoleId, 'Admin', 'ADMIN', NEWID());
    END
    ELSE
    BEGIN
        SELECT @AdminRoleId = [Id] FROM [AspNetRoles] WHERE [Name] = 'Admin';
    END
    
    -- Create admin user
    -- Password hash for "AdminPass123!" using ASP.NET Core Identity default hasher
    INSERT INTO [AspNetUsers] (
        [Id], 
        [UserName], 
        [NormalizedUserName], 
        [Email], 
        [NormalizedEmail], 
        [EmailConfirmed], 
        [PasswordHash], 
        [SecurityStamp], 
        [ConcurrencyStamp], 
        [PhoneNumberConfirmed], 
        [TwoFactorEnabled], 
        [LockoutEnabled], 
        [AccessFailedCount],
        [IsAdmin],
        [IsDeleted],
        [CreationTime]
    )
    VALUES (
        @AdminUserId,
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        1, -- EmailConfirmed
        'AQAAAAIAAYagAAAAEHqOzKhL8Z9M7QqALDovKFepC+BfQx1+3OHOIkPXZn5wLM9+Gg9adCx/PdWi4Hxp6Q==', -- AdminPass123!
        NEWID(),
        NEWID(),
        0, -- PhoneNumberConfirmed
        0, -- TwoFactorEnabled
        1, -- LockoutEnabled
        0, -- AccessFailedCount
        1, -- IsAdmin
        0, -- IsDeleted
        GETUTCDATE() -- CreationTime
    );
    
    -- Assign admin user to Admin role
    INSERT INTO [AspNetUserRoles] ([UserId], [RoleId])
    VALUES (@AdminUserId, @AdminRoleId);
    
    PRINT 'Admin user created successfully with email: <EMAIL> and password: AdminPass123!';
END
ELSE
BEGIN
    PRINT 'Admin user already exists.';
END
