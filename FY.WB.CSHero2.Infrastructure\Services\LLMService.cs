using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Infrastructure.Services
{
    /// <summary>
    /// LLM service configuration options
    /// </summary>
    public class LLMServiceOptions
    {
        public string Endpoint { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string Model { get; set; } = "gpt-4";
        public int MaxTokens { get; set; } = 4000;
        public double Temperature { get; set; } = 0.7;
        public int TimeoutSeconds { get; set; } = 30;
        public bool EnableCaching { get; set; } = true;
        public int CacheDurationMinutes { get; set; } = 120;
    }

    /// <summary>
    /// Service for integrating with Large Language Model APIs for component generation
    /// </summary>
    public class LLMService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<LLMService> _logger;
        private readonly LLMServiceOptions _options;

        public LLMService(
            HttpClient httpClient,
            ILogger<LLMService> logger,
            IOptions<LLMServiceOptions> options)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

            ConfigureHttpClient();
        }

        /// <summary>
        /// Generates React component code using LLM
        /// </summary>
        public async Task<LLMResponse> GenerateComponentAsync(
            string prompt,
            Dictionary<string, object> context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating component with LLM");

                var request = new LLMRequest
                {
                    Model = _options.Model,
                    Messages = new List<LLMMessage>
                    {
                        new LLMMessage
                        {
                            Role = "system",
                            Content = GetSystemPrompt()
                        },
                        new LLMMessage
                        {
                            Role = "user",
                            Content = BuildUserPrompt(prompt, context)
                        }
                    },
                    MaxTokens = _options.MaxTokens,
                    Temperature = _options.Temperature
                };

                var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(TimeSpan.FromSeconds(_options.TimeoutSeconds));

                var response = await _httpClient.PostAsync(_options.Endpoint, content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
                    var llmResponse = JsonSerializer.Deserialize<LLMApiResponse>(responseJson, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    if (llmResponse?.Choices?.Count > 0)
                    {
                        var generatedContent = llmResponse.Choices[0].Message.Content;

                        _logger.LogDebug("Successfully generated component with LLM");

                        return new LLMResponse
                        {
                            Success = true,
                            Content = generatedContent,
                            TokensUsed = llmResponse.Usage?.TotalTokens ?? 0,
                            Model = llmResponse.Model ?? _options.Model
                        };
                    }
                }

                _logger.LogWarning("LLM API returned unsuccessful response: {StatusCode}", response.StatusCode);
                return new LLMResponse
                {
                    Success = false,
                    Error = $"LLM API returned {response.StatusCode}: {response.ReasonPhrase}"
                };
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("LLM request timed out");
                return new LLMResponse
                {
                    Success = false,
                    Error = "Request timed out"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling LLM API");
                return new LLMResponse
                {
                    Success = false,
                    Error = $"LLM API error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Validates component code using LLM
        /// </summary>
        public async Task<LLMValidationResponse> ValidateComponentAsync(
            string componentCode,
            string framework = "NextJS",
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Validating component with LLM");

                var prompt = $@"
Please validate the following {framework} React component code and provide feedback:

```typescript
{componentCode}
```

Check for:
1. Syntax errors
2. TypeScript type safety
3. React best practices
4. Performance issues
5. Accessibility concerns
6. Security vulnerabilities

Provide a JSON response with the following structure:
{{
  ""isValid"": boolean,
  ""errors"": [""error messages""],
  ""warnings"": [""warning messages""],
  ""suggestions"": [""improvement suggestions""],
  ""score"": number (0-100)
}}";

                var response = await GenerateComponentAsync(prompt, new Dictionary<string, object>(), cancellationToken);

                if (response.Success)
                {
                    try
                    {
                        var validationResult = JsonSerializer.Deserialize<LLMValidationResponse>(response.Content, new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        });

                        return validationResult ?? new LLMValidationResponse
                        {
                            IsValid = false,
                            Errors = new List<string> { "Failed to parse validation response" }
                        };
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse LLM validation response");
                        return new LLMValidationResponse
                        {
                            IsValid = false,
                            Errors = new List<string> { "Invalid validation response format" }
                        };
                    }
                }

                return new LLMValidationResponse
                {
                    IsValid = false,
                    Errors = new List<string> { response.Error ?? "Validation failed" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating component with LLM");
                return new LLMValidationResponse
                {
                    IsValid = false,
                    Errors = new List<string> { $"Validation error: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Optimizes component code using LLM
        /// </summary>
        public async Task<LLMResponse> OptimizeComponentAsync(
            string componentCode,
            string optimizationType = "performance",
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Optimizing component with LLM for {OptimizationType}", optimizationType);

                var prompt = $@"
Please optimize the following React component for {optimizationType}:

```typescript
{componentCode}
```

Focus on:
- {GetOptimizationFocus(optimizationType)}

Return only the optimized component code without explanations.";

                return await GenerateComponentAsync(prompt, new Dictionary<string, object>
                {
                    ["optimizationType"] = optimizationType
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing component with LLM");
                return new LLMResponse
                {
                    Success = false,
                    Error = $"Optimization error: {ex.Message}"
                };
            }
        }

        #region Private Methods

        private void ConfigureHttpClient()
        {
            if (!string.IsNullOrEmpty(_options.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_options.ApiKey}");
            }

            _httpClient.DefaultRequestHeaders.Add("User-Agent", "FY.WB.CSHero2.ReportRenderingEngine/1.0");
        }

        private string GetSystemPrompt()
        {
            return @"
You are an expert React/NextJS developer specializing in generating high-quality, production-ready components.

Guidelines:
- Generate TypeScript code with proper type definitions
- Use modern React patterns (hooks, functional components)
- Follow accessibility best practices
- Optimize for performance
- Use TailwindCSS for styling
- Include proper error handling
- Write clean, maintainable code
- Follow React and NextJS conventions

Always return valid, compilable code that follows best practices.";
        }

        private string BuildUserPrompt(string prompt, Dictionary<string, object> context)
        {
            var contextBuilder = new StringBuilder();

            if (context.Count > 0)
            {
                contextBuilder.AppendLine("Context:");
                foreach (var kvp in context)
                {
                    contextBuilder.AppendLine($"- {kvp.Key}: {JsonSerializer.Serialize(kvp.Value)}");
                }
                contextBuilder.AppendLine();
            }

            return contextBuilder.ToString() + prompt;
        }

        private string GetOptimizationFocus(string optimizationType)
        {
            return optimizationType.ToLower() switch
            {
                "performance" => "Reducing re-renders, optimizing state management, lazy loading, memoization",
                "accessibility" => "ARIA attributes, keyboard navigation, screen reader support, semantic HTML",
                "bundle-size" => "Tree shaking, dynamic imports, removing unused code, optimizing dependencies",
                "maintainability" => "Code organization, reusability, documentation, type safety",
                _ => "General code quality and best practices"
            };
        }

        #endregion
    }

    #region DTOs

    public class LLMRequest
    {
        public string Model { get; set; } = string.Empty;
        public List<LLMMessage> Messages { get; set; } = new List<LLMMessage>();
        public int MaxTokens { get; set; }
        public double Temperature { get; set; }
    }

    public class LLMMessage
    {
        public string Role { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }

    public class LLMApiResponse
    {
        public string Model { get; set; } = string.Empty;
        public List<LLMChoice> Choices { get; set; } = new List<LLMChoice>();
        public LLMUsage? Usage { get; set; }
    }

    public class LLMChoice
    {
        public LLMMessage Message { get; set; } = new LLMMessage();
        public string FinishReason { get; set; } = string.Empty;
    }

    public class LLMUsage
    {
        public int PromptTokens { get; set; }
        public int CompletionTokens { get; set; }
        public int TotalTokens { get; set; }
    }

    public class LLMResponse
    {
        public bool Success { get; set; }
        public string Content { get; set; } = string.Empty;
        public string? Error { get; set; }
        public int TokensUsed { get; set; }
        public string Model { get; set; } = string.Empty;
    }

    public class LLMValidationResponse
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Suggestions { get; set; } = new List<string>();
        public int Score { get; set; }
    }

    #endregion
}
