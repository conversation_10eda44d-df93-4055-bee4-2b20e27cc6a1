<!-- Guide Purpose: Data initialization approach, dependency management, and troubleshooting for multi-storage seeding -->
<!-- Target Audience: Developers implementing data seeders and managing data initialization -->

# Seeding Strategy Guide

## Overview

This guide provides comprehensive strategies for seeding data across the multi-storage architecture, including dependency management, error handling, and troubleshooting common issues. The seeding process must coordinate across SQL Server, Cosmos DB, and Blob Storage while maintaining data consistency and referential integrity.

## Seeding Architecture

### Dependency Order

The seeding process follows a strict dependency hierarchy to ensure referential integrity:

```mermaid
graph TD
    A[1. SQL Server Base Data] --> B[2. SQL Server Reports & Versions]
    B --> C[3. Cosmos DB Documents]
    C --> D[4. Blob Storage Components]
    D --> E[5. SQL Server Reference Updates]
    E --> F[6. Validation & Cleanup]
```

### Seeding Phases

#### Phase 1: SQL Server Foundation
- Tenant profiles and users
- Clients and templates
- Report metadata (without external references)
- Report sections and fields structure

#### Phase 2: SQL Server Reports
- Report entities with basic metadata
- Report versions (without external storage references)
- Initial audit trail data

#### Phase 3: Cosmos DB Documents
- Transform SQL data to Cosmos document format
- Create versioned report data documents
- Establish tenant partitioning

#### Phase 4: Blob Storage Components
- Generate and store component code
- Create style documents
- Upload assets and exports

#### Phase 5: Reference Linking
- Update SQL entities with external storage references
- Establish cross-storage consistency
- Validate all references

#### Phase 6: Validation & Cleanup
- Verify data integrity across all systems
- Clean up any orphaned data
- Generate seeding reports

## Implementation Patterns

### 1. Master Seeder Orchestration

```csharp
public class MasterSeeder : IMasterSeeder
{
    private readonly ISqlSeeder _sqlSeeder;
    private readonly ICosmosSeeder _cosmosSeeder;
    private readonly IBlobSeeder _blobSeeder;
    private readonly IDataIntegrityService _integrityService;
    private readonly ILogger<MasterSeeder> _logger;

    public async Task<SeedingResult> SeedAllAsync(SeedingOptions options = null)
    {
        var result = new SeedingResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting multi-storage seeding process");

            // Phase 1: SQL Foundation
            _logger.LogInformation("Phase 1: Seeding SQL Server foundation data");
            var sqlFoundationResult = await _sqlSeeder.SeedFoundationAsync();
            result.AddPhaseResult("SQL Foundation", sqlFoundationResult);

            if (!sqlFoundationResult.Success)
            {
                _logger.LogError("SQL foundation seeding failed. Aborting process.");
                return result;
            }

            // Phase 2: SQL Reports
            _logger.LogInformation("Phase 2: Seeding SQL Server reports");
            var sqlReportsResult = await _sqlSeeder.SeedReportsAsync();
            result.AddPhaseResult("SQL Reports", sqlReportsResult);

            if (!sqlReportsResult.Success)
            {
                _logger.LogError("SQL reports seeding failed. Aborting process.");
                return result;
            }

            // Phase 3: Cosmos DB Documents
            _logger.LogInformation("Phase 3: Seeding Cosmos DB documents");
            var cosmosResult = await _cosmosSeeder.SeedDocumentsAsync();
            result.AddPhaseResult("Cosmos Documents", cosmosResult);

            if (!cosmosResult.Success && options?.ContinueOnCosmosFailure != true)
            {
                _logger.LogError("Cosmos DB seeding failed. Aborting process.");
                await CompensateFailedSeedingAsync(result);
                return result;
            }

            // Phase 4: Blob Storage
            _logger.LogInformation("Phase 4: Seeding Blob Storage");
            var blobResult = await _blobSeeder.SeedComponentsAsync();
            result.AddPhaseResult("Blob Components", blobResult);

            if (!blobResult.Success && options?.ContinueOnBlobFailure != true)
            {
                _logger.LogError("Blob storage seeding failed. Aborting process.");
                await CompensateFailedSeedingAsync(result);
                return result;
            }

            // Phase 5: Reference Linking
            _logger.LogInformation("Phase 5: Linking cross-storage references");
            var linkingResult = await LinkCrossStorageReferencesAsync();
            result.AddPhaseResult("Reference Linking", linkingResult);

            // Phase 6: Validation
            _logger.LogInformation("Phase 6: Validating data integrity");
            var validationResult = await ValidateSeededDataAsync();
            result.AddPhaseResult("Validation", validationResult);

            result.Success = result.PhaseResults.All(p => p.Success);
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation(
                "Multi-storage seeding completed. Success: {Success}, Duration: {Duration}",
                result.Success, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error during seeding process");
            result.Success = false;
            result.Error = ex.Message;
            return result;
        }
    }

    private async Task CompensateFailedSeedingAsync(SeedingResult result)
    {
        _logger.LogInformation("Starting compensation for failed seeding");

        // Cleanup in reverse order
        var successfulPhases = result.PhaseResults.Where(p => p.Success).ToList();
        
        foreach (var phase in successfulPhases.Reverse())
        {
            try
            {
                await CleanupPhaseAsync(phase.PhaseName);
                _logger.LogInformation("Cleaned up phase: {PhaseName}", phase.PhaseName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup phase: {PhaseName}", phase.PhaseName);
            }
        }
    }
}

public class SeedingResult
{
    public bool Success { get; set; }
    public string Error { get; set; }
    public TimeSpan Duration { get; set; }
    public List<PhaseResult> PhaseResults { get; } = new();
    public Dictionary<string, object> Metrics { get; } = new();

    public void AddPhaseResult(string phaseName, PhaseResult result)
    {
        result.PhaseName = phaseName;
        PhaseResults.Add(result);
    }
}

public class PhaseResult
{
    public string PhaseName { get; set; }
    public bool Success { get; set; }
    public string Error { get; set; }
    public TimeSpan Duration { get; set; }
    public int ItemsProcessed { get; set; }
    public int ItemsSucceeded { get; set; }
    public int ItemsFailed { get; set; }
    public List<string> Warnings { get; } = new();
}
```

### 2. SQL Server Seeder Implementation

```csharp
public class SqlSeeder : ISqlSeeder
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<SqlSeeder> _logger;
    private readonly IConfiguration _configuration;

    public async Task<PhaseResult> SeedFoundationAsync()
    {
        var result = new PhaseResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 1. Seed tenant profiles
            var tenantProfiles = await LoadSeedData<TenantProfile>("tenant-profiles.json");
            await SeedEntitiesAsync(tenantProfiles, "TenantProfiles");

            // 2. Seed clients
            var clients = await LoadSeedData<Client>("clients.json");
            await SeedEntitiesAsync(clients, "Clients");

            // 3. Seed templates
            var templates = await LoadSeedData<Template>("templates.json");
            await SeedEntitiesAsync(templates, "Templates");

            result.Success = true;
            result.Duration = stopwatch.Elapsed;
            result.ItemsProcessed = tenantProfiles.Count + clients.Count + templates.Count;
            result.ItemsSucceeded = result.ItemsProcessed;

            _logger.LogInformation("SQL foundation seeding completed successfully");
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "SQL foundation seeding failed");
        }

        return result;
    }

    public async Task<PhaseResult> SeedReportsAsync()
    {
        var result = new PhaseResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 1. Load and validate report data
            var reports = await LoadSeedData<Report>("reports.json");
            var reportSections = await LoadSeedData<ReportSection>("report-sections.json");
            var reportFields = await LoadSeedData<ReportSectionField>("report-section-fields.json");
            var reportVersions = await LoadSeedData<ReportVersion>("report-versions.json");

            // 2. Validate relationships
            await ValidateReportRelationshipsAsync(reports, reportSections, reportFields, reportVersions);

            // 3. Seed in dependency order
            await SeedEntitiesAsync(reports, "Reports");
            await SeedEntitiesAsync(reportSections, "ReportSections");
            await SeedEntitiesAsync(reportFields, "ReportSectionFields");
            await SeedEntitiesAsync(reportVersions, "ReportVersions");

            result.Success = true;
            result.Duration = stopwatch.Elapsed;
            result.ItemsProcessed = reports.Count + reportSections.Count + reportFields.Count + reportVersions.Count;
            result.ItemsSucceeded = result.ItemsProcessed;

            _logger.LogInformation("SQL reports seeding completed successfully");
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "SQL reports seeding failed");
        }

        return result;
    }

    private async Task<List<T>> LoadSeedData<T>(string fileName)
    {
        var filePath = Path.Combine("SeedData", fileName);
        
        if (!File.Exists(filePath))
        {
            _logger.LogWarning("Seed data file not found: {FilePath}", filePath);
            return new List<T>();
        }

        var json = await File.ReadAllTextAsync(filePath);
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        return JsonSerializer.Deserialize<List<T>>(json, options) ?? new List<T>();
    }

    private async Task SeedEntitiesAsync<T>(List<T> entities, string entityName) where T : class
    {
        if (!entities.Any())
        {
            _logger.LogInformation("No {EntityName} to seed", entityName);
            return;
        }

        var existingCount = await _context.Set<T>().CountAsync();
        if (existingCount > 0)
        {
            _logger.LogInformation("{EntityName} already seeded ({ExistingCount} records)", entityName, existingCount);
            return;
        }

        _context.Set<T>().AddRange(entities);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Seeded {Count} {EntityName}", entities.Count, entityName);
    }

    private async Task ValidateReportRelationshipsAsync(
        List<Report> reports,
        List<ReportSection> sections,
        List<ReportSectionField> fields,
        List<ReportVersion> versions)
    {
        // Validate client references
        var clientIds = reports.Select(r => r.ClientId).Distinct().ToList();
        var existingClients = await _context.Clients
            .Where(c => clientIds.Contains(c.Id))
            .Select(c => c.Id)
            .ToListAsync();

        var missingClients = clientIds.Except(existingClients).ToList();
        if (missingClients.Any())
        {
            throw new InvalidOperationException($"Missing client references: {string.Join(", ", missingClients)}");
        }

        // Validate report-section relationships
        var reportIds = reports.Select(r => r.Id).ToHashSet();
        var invalidSections = sections.Where(s => !reportIds.Contains(s.ReportId)).ToList();
        if (invalidSections.Any())
        {
            throw new InvalidOperationException($"Invalid report references in sections: {invalidSections.Count} sections");
        }

        // Validate section-field relationships
        var sectionIds = sections.Select(s => s.Id).ToHashSet();
        var invalidFields = fields.Where(f => !sectionIds.Contains(f.ReportSectionId)).ToList();
        if (invalidFields.Any())
        {
            throw new InvalidOperationException($"Invalid section references in fields: {invalidFields.Count} fields");
        }

        // Validate report-version relationships
        var invalidVersions = versions.Where(v => !reportIds.Contains(v.ReportId)).ToList();
        if (invalidVersions.Any())
        {
            throw new InvalidOperationException($"Invalid report references in versions: {invalidVersions.Count} versions");
        }
    }
}
```

### 3. Cosmos DB Seeder Implementation

```csharp
public class CosmosSeeder : ICosmosSeeder
{
    private readonly ICosmosDbService _cosmosService;
    private readonly IApplicationDbContext _sqlContext;
    private readonly ILogger<CosmosSeeder> _logger;
    private readonly ICosmosDocumentValidator _validator;

    public async Task<PhaseResult> SeedDocumentsAsync()
    {
        var result = new PhaseResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 1. Get reports that need Cosmos documents
            var reportsToProcess = await GetReportsRequiringCosmosDocumentsAsync();
            result.ItemsProcessed = reportsToProcess.Count;

            _logger.LogInformation("Processing {Count} reports for Cosmos DB seeding", reportsToProcess.Count);

            // 2. Process in batches to avoid overwhelming Cosmos DB
            var batchSize = 10;
            var batches = reportsToProcess.Chunk(batchSize);

            foreach (var batch in batches)
            {
                var batchTasks = batch.Select(ProcessReportAsync);
                var batchResults = await Task.WhenAll(batchTasks);

                foreach (var batchResult in batchResults)
                {
                    if (batchResult.Success)
                        result.ItemsSucceeded++;
                    else
                    {
                        result.ItemsFailed++;
                        result.Warnings.Add(batchResult.Error);
                    }
                }

                // Small delay between batches to avoid throttling
                await Task.Delay(100);
            }

            result.Success = result.ItemsFailed == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation(
                "Cosmos DB seeding completed. Success: {Success}, Processed: {Processed}, Succeeded: {Succeeded}, Failed: {Failed}",
                result.Success, result.ItemsProcessed, result.ItemsSucceeded, result.ItemsFailed);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "Cosmos DB seeding failed");
        }

        return result;
    }

    private async Task<List<ReportWithVersions>> GetReportsRequiringCosmosDocumentsAsync()
    {
        return await _sqlContext.Reports
            .Include(r => r.Versions.Where(v => string.IsNullOrEmpty(v.DataDocumentId)))
            .Include(r => r.Sections)
                .ThenInclude(s => s.Fields)
            .Where(r => r.Versions.Any(v => string.IsNullOrEmpty(v.DataDocumentId)))
            .Select(r => new ReportWithVersions
            {
                Report = r,
                VersionsNeedingDocuments = r.Versions.Where(v => string.IsNullOrEmpty(v.DataDocumentId)).ToList()
            })
            .ToListAsync();
    }

    private async Task<ProcessingResult> ProcessReportAsync(ReportWithVersions reportData)
    {
        try
        {
            foreach (var version in reportData.VersionsNeedingDocuments)
            {
                // 1. Transform SQL data to Cosmos document
                var document = TransformToCosmosDocument(reportData.Report, version);

                // 2. Validate document
                var validationResult = _validator.ValidateDocument(document);
                if (!validationResult.IsValid)
                {
                    var errors = string.Join("; ", validationResult.Errors);
                    _logger.LogError("Document validation failed for report {ReportId} version {VersionId}: {Errors}",
                        reportData.Report.Id, version.Id, errors);
                    return new ProcessingResult { Success = false, Error = $"Validation failed: {errors}" };
                }

                // 3. Create document in Cosmos DB
                await _cosmosService.CreateDocumentAsync(document, document.TenantId);

                // 4. Update SQL version with document reference
                version.DataDocumentId = document.Id;
                version.StorageStrategy = "MultiStorage";

                _logger.LogDebug("Created Cosmos document {DocumentId} for report {ReportId} version {VersionId}",
                    document.Id, reportData.Report.Id, version.Id);
            }

            // Save SQL changes
            await _sqlContext.SaveChangesAsync();

            return new ProcessingResult { Success = true };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process report {ReportId}", reportData.Report.Id);
            return new ProcessingResult { Success = false, Error = ex.Message };
        }
    }

    private VersionedReportDataDocument TransformToCosmosDocument(Report report, ReportVersion version)
    {
        var documentId = GenerateVersionedDocumentId(report.Id, version.Id);

        return new VersionedReportDataDocument
        {
            Id = documentId,
            TenantId = report.TenantId.ToString(),
            ReportId = report.Id,
            VersionId = version.Id,
            VersionNumber = version.VersionNumber,
            IsDraft = !version.IsCurrent,

            // Denormalized metadata for performance
            ReportName = report.Name,
            ReportNumber = report.ReportNumber,
            Category = report.Category,
            Status = report.Status,
            Author = report.Author,
            ClientId = report.ClientId,
            ClientName = report.ClientName,

            // Transform sections and fields
            Sections = report.Sections?.Select(s => new ReportDataSection
            {
                Id = s.Id,
                Name = s.Title,
                Title = s.Title,
                SectionType = s.Type ?? "default",
                DisplayOrder = s.Order,
                IsRequired = false,
                Fields = s.Fields?.Select(f => new ReportDataField
                {
                    Id = f.Id,
                    Name = f.Name,
                    Label = f.Label ?? f.Name,
                    FieldType = f.Type ?? "string",
                    DefaultValue = f.Content ?? string.Empty,
                    IsRequired = f.IsRequired,
                    DisplayOrder = f.Order,
                    ValidationRules = new Dictionary<string, object>(),
                    Options = new List<string>()
                }).ToList() ?? new List<ReportDataField>()
            }).ToList() ?? new List<ReportDataSection>(),

            // Enhanced data from seed file
            ComponentDataJson = GenerateComponentData(report),
            JsonData = GenerateEnhancedData(report),

            // Metadata
            Metadata = new ReportDataMetadata
            {
                CreatedAt = version.CreationTime.ToUniversalTime(),
                UpdatedAt = (version.LastModificationTime ?? version.CreationTime).ToUniversalTime(),
                SectionCount = report.Sections?.Count ?? 0,
                FieldCount = report.Sections?.Sum(s => s.Fields?.Count ?? 0) ?? 0,
                Version = version.VersionNumber.ToString(),
                Tags = new List<string> { report.Category, report.Status, "seeded_data" }
            }
        };
    }

    private Dictionary<string, object> GenerateComponentData(Report report)
    {
        return new Dictionary<string, object>
        {
            ["layout"] = "standard",
            ["theme"] = GetThemeForCategory(report.Category),
            ["responsive"] = true,
            ["animations"] = true
        };
    }

    private Dictionary<string, object> GenerateEnhancedData(Report report)
    {
        // Generate mock dashboard metrics based on report category
        var metrics = GenerateMetricsForCategory(report.Category);
        
        return new Dictionary<string, object>
        {
            ["enhanced"] = true,
            ["dashboardMetrics"] = metrics,
            ["generatedAt"] = DateTime.UtcNow.ToString("O"),
            ["dataSource"] = "seeded"
        };
    }

    private string GetThemeForCategory(string category)
    {
        return category?.ToLower() switch
        {
            "financial" => "corporate",
            "performance" => "modern",
            "strategy" => "executive",
            "usage" => "analytics",
            _ => "default"
        };
    }

    private Dictionary<string, object> GenerateMetricsForCategory(string category)
    {
        // Generate realistic metrics based on category
        var random = new Random();
        
        return category?.ToLower() switch
        {
            "financial" => new Dictionary<string, object>
            {
                ["revenue"] = new { current = random.Next(1000000, 5000000), previousPeriod = random.Next(800000, 4500000) },
                ["profit"] = new { current = random.Next(100000, 800000), previousPeriod = random.Next(80000, 700000) },
                ["expenses"] = new { current = random.Next(500000, 2000000), previousPeriod = random.Next(450000, 1800000) }
            },
            "performance" => new Dictionary<string, object>
            {
                ["efficiency"] = new { current = random.Next(75, 95), previousPeriod = random.Next(70, 90) },
                ["quality"] = new { current = random.Next(85, 98), previousPeriod = random.Next(80, 95) },
                ["throughput"] = new { current = random.Next(1000, 5000), previousPeriod = random.Next(900, 4500) }
            },
            _ => new Dictionary<string, object>
            {
                ["totalItems"] = new { current = random.Next(100, 1000), previousPeriod = random.Next(90, 900) },
                ["activeItems"] = new { current = random.Next(50, 500), previousPeriod = random.Next(45, 450) }
            }
        };
    }
}

public class ReportWithVersions
{
    public Report Report { get; set; }
    public List<ReportVersion> VersionsNeedingDocuments { get; set; }
}

public class ProcessingResult
{
    public bool Success { get; set; }
    public string Error { get; set; }
}
```

### 4. Blob Storage Seeder Implementation

```csharp
public class BlobSeeder : IBlobSeeder
{
    private readonly IBlobStorageService _blobService;
    private readonly IApplicationDbContext _sqlContext;
    private readonly ILogger<BlobSeeder> _logger;
    private readonly IComponentGenerator _componentGenerator;

    public async Task<PhaseResult> SeedComponentsAsync()
    {
        var result = new PhaseResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 1. Get versions that need blob storage
            var versionsToProcess = await GetVersionsRequiringBlobStorageAsync();
            result.ItemsProcessed = versionsToProcess.Count;

            _logger.LogInformation("Processing {Count} versions for Blob Storage seeding", versionsToProcess.Count);

            // 2. Process each version
            foreach (var version in versionsToProcess)
            {
                try
                {
                    await ProcessVersionAsync(version);
                    result.ItemsSucceeded++;
                }
                catch (Exception ex)
                {
                    result.ItemsFailed++;
                    result.Warnings.Add($"Version {version.Id}: {ex.Message}");
                    _logger.LogError(ex, "Failed to process version {VersionId}", version.Id);
                }
            }

            result.Success = result.ItemsFailed == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation(
                "Blob Storage seeding completed. Success: {Success}, Processed: {Processed}, Succeeded: {Succeeded}, Failed: {Failed}",
                result.Success, result.ItemsProcessed, result.ItemsSucceeded, result.ItemsFailed);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "Blob Storage seeding failed");
        }

        return result;
    }

    private async Task<List<ReportVersionWithData>> GetVersionsRequiringBlobStorageAsync()
    {
        return await _sqlContext.ReportVersions
            .Include(v => v.Report)
                .ThenInclude(r => r.Sections)
                    .ThenInclude(s => s.Fields)
            .Where(v => string.IsNullOrEmpty(v.ComponentsBlobId))
            .Select(v => new ReportVersionWithData
            {
                Version = v,
                Report = v.Report
            })
            .ToListAsync();
    }

    private async Task ProcessVersionAsync(ReportVersionWithData versionData)
    {
        var version = versionData.Version;
        var report = versionData.Report;

        // 1. Generate components
        var components = await _componentGenerator.GenerateComponentsAsync(report, version);

        // 2. Create components blob
        var componentsBlob = new ComponentsBlob
        {
            ReportId = report.Id,
            VersionId = version.Id,
            VersionNumber = version.VersionNumber,
            TenantId = report.TenantId.ToString(),
            Components = components,
            Metadata = new ComponentsMetadata
            {
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "seeder",
                TotalComponents = components.Count,
                TotalSize = CalculateComponentsSize(components)
            }
        };

        var componentsBlobPath = await _blobService.SaveComponentsAsync(componentsBlob);

        // 3. Generate and save styles
        var stylesBlob = GenerateStylesBlob(report, version);
        var stylesBlobPath = await _blobService.SaveStylesAsync(stylesBlob);

        // 4. Update SQL version with blob references
        version.ComponentsBlobId = componentsBlobPath;
        version.StylesBlobId = stylesBlobPath;
        version.ComponentDataSize = componentsBlob.Metadata.TotalSize;
        version.StorageStrategy = "MultiStorage";

        await _sqlContext.SaveChangesAsync();

        _logger.LogDebug("Created blob storage for report {ReportId} version {VersionId}: Components={ComponentsPath}, Styles={StylesPath}",
            report.Id, version.Id, componentsBlobPath, stylesBlobPath);
    }

    private StylesBlob GenerateStylesBlob(Report report, ReportVersion version)
    {
        return new StylesBlob
        {
            ReportId = report.Id,
            VersionId = version.Id,
            StyleDocumentId = $"style-report-{version.Id:N}",
            HtmlContent = GenerateHtmlTemplate(report),
            CssStyles = GenerateCssStyles(report),
            ComponentStyles = GenerateComponentStyles(report),
            Metadata = new StylesMetadata
            {
                Framework = "TailwindCSS",
                Theme = GetThemeForCategory(report.Category),
                ColorScheme = "light",
                CreatedAt = DateTime.UtcNow
            }
        };
    }

    private string GenerateHtmlTemplate(Report report)
    {
        return $@"
<div class=""report-container max-w-6xl mx-auto p-6"">
    <header class=""report-header mb-8"">
        <h1 class=""text-3xl font-bold text-gray-900"">{report.Name}</h1>
        <p class=""text-gray-600 mt-2"">Report #{report.ReportNumber} - {report.Category}</p>
    </header>
    <main class=""report-content"">
        <!-- Dynamic sections will be inserted here -->
    </main>
    <footer class=""report-footer mt-8 pt-4 border-t border-gray-200"">
        <p class=""text-sm text-gray-500"">Generated by CSHero2 Reporting System</p>
    </footer>
</div>";
    }

    private string GenerateCssStyles(Report report)
    {
        var theme = GetThemeForCategory(report.Category);
        
        return theme switch
        {
            "corporate" => @"
.report-container { 
    font-family: 'Inter', sans-serif; 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.report-header { 
    background: rgba(255, 255, 255, 0.95); 
    border-radius: 8px; 
    padding: 2rem; 
}",
            "modern" => @"
.report-container { 
    font-family: 'Roboto', sans-serif; 
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}
.report-header { 
    background: rgba(255, 255, 255, 0.9); 
    border-radius: 12px; 
    padding: 2rem; 
}",
            _ => @"
.report-container { 
    font-family: 'Arial', sans-serif; 
    background: #f8f9fa;
}
.report-header { 
    background: white; 
    border-radius: 6px; 
    padding: 1.5rem; 
}"
        };
    }

    private Dictionary<string, ComponentStyle> GenerateComponentStyles(Report report)
    {
        return new Dictionary<string, ComponentStyle>
        {
            ["header"] = new ComponentStyle
            {
                ComponentId = "header",
                ComponentType = "header",
                CssClasses = new List<string> { "report-header", "text-center", "mb-8" },
                CustomCss = ".report-header { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }"
            },
            ["section"] = new ComponentStyle
            {
                ComponentId = "section",
                ComponentType = "section",
                CssClasses = new List<string> { "report-section", "mb-6", "p-4" },
                CustomCss = ".report-section { border-left: 4px solid #3b82f6; }"
            }
        };
    }

    private long CalculateComponentsSize(List<ComponentDefinition> components)
    {
        return components.Sum(c => Encoding.UTF8.GetByteCount(c.ComponentCode ?? string.Empty));
    }

    private string GetThemeForCategory(string category)
    {
        return category?.ToLower() switch
        {
            "financial" => "corporate",
            "performance" => "modern",
            "strategy" => "executive",
            "usage" => "analytics",
            _ => "default"
        };
    }
}

public class ReportVersionWithData
{
    public ReportVersion Version { get; set; }
    public Report Report { get; set; }
}
```

## Troubleshooting Common Issues

### 1. Tenant ID Mismatches

**Problem**: Reports created with incorrect or missing tenant IDs, causing cross-tenant data access issues.

**Symptoms**:
- CosmosDB documents created with wrong partition key
- Blob storage paths pointing to wrong tenant directories
- SQL queries returning no results due to tenant filtering

**Solution**:
```csharp
public class TenantIdValidator : ITenantIdValidator
{
    public async Task<ValidationResult> ValidateTenantConsistencyAsync(Guid reportId)
    {
        var result = new ValidationResult();

        // 1. Get report from SQL
        var report = await _sqlContext.Reports.FindAsync(reportId);
        if (report == null)
        {
            result.AddError($"Report {reportId} not found in SQL");
            return result;
        }

        // 2. Check Cosmos document tenant consistency
        var version = await _sqlContext.ReportVersions
            .FirstOrDefaultAsync(v => v.ReportId == reportId && v.IsCurrent);

        if (version?.DataDocumentId != null)
        {
            var document = await _cosmosService.GetDocumentAsync(version.DataDocumentId);
            if (document != null && document.TenantId != report.TenantId.ToString())
            {
                result.AddError($"Tenant mismatch: SQL={report.TenantId}, Cosmos={document.TenantId}");
            }
        }

        // 3. Check blob storage path consistency
        if (version?.ComponentsBlobId != null)
        {
            var expectedPrefix = $"tenants/{report.TenantId}/";
            if (!version.ComponentsBlobId.StartsWith(expectedPrefix))
            {
                result.AddError($"Blob path tenant mismatch: Expected prefix={expectedPrefix}, Actual={version.ComponentsBlobId}");
            }
        }

        return result;
    }

    public async Task<FixResult> FixTenantMismatchAsync(Guid reportId)
    {
        var result = new FixResult();

        try
        {
            var report = await _sqlContext.Reports
                .Include(r => r.Versions)
                .FirstOrDefaultAsync(r => r.Id == reportId);

            foreach (var version in report.Versions)
            {
                // Fix Cosmos document tenant
                if (!string.IsNullOrEmpty(version.DataDocumentId))
                {
                    await FixCosmosDocumentTenantAsync(version, report.TenantId.ToString());
                    result.CosmosDocumentsFixed++;
                }

                // Fix blob storage paths
                if (!string.IsNullOrEmpty(version.ComponentsBlobId))
                {
                    await FixBlobStoragePathAsync(version, report.TenantId.ToString());
                    result.BlobPathsFixed++;
                }
            }

            result.Success = true;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
        }

        return result;
    }
}
```

### 2. Document Size Validation Failures

**Problem**: Cosmos DB documents exceeding the 2MB limit.

**Symptoms**:
- CosmosException with status code 413 (Request Entity Too Large)
- Seeding failures during Cosmos DB phase
- Performance degradation due to large documents

**Solution**:
```csharp
public class DocumentSizeOptimizer : IDocumentSizeOptimizer
{
    public async Task<OptimizationResult> OptimizeDocumentSizeAsync(VersionedReportDataDocument document)
    {
        var result = new OptimizationResult();
        var originalSize = CalculateDocumentSize(document);

        if (originalSize <= 1.5 * 1024 * 1024) // 1.5MB threshold
        {
            result.OptimizationNeeded = false;
            return result;
        }

        // 1. Remove unnecessary metadata
        document.Metadata.Tags = document.Metadata.Tags?.Take(5).ToList();

        // 2. Truncate long field values
        foreach (var section in document.Sections ?? new List<ReportDataSection>())
        {
            foreach (var field in section.Fields ?? new List<ReportDataField>())
            {
                if (!string.IsNullOrEmpty(field.DefaultValue) && field.DefaultValue.Length > 1000)
                {
                    field.DefaultValue = field.DefaultValue.Substring(0, 997) + "...";
                    result.FieldsTruncated++;
                }
            }
        }

        // 3. Move large data to blob storage
        if (document.JsonData != null && CalculateJsonDataSize(document.JsonData) > 500 * 1024) // 500KB
        {
            var blobPath = await MoveLargeDataToBlobAsync(document);
            document.JsonData = new Dictionary<string, object>
            {
                ["_blobReference"] = blobPath,
                ["_dataMovedToBlob"] = true
            };
            result.DataMovedToBlob = true;
        }

        var optimizedSize = CalculateDocumentSize(document);
        result.OriginalSize = originalSize;
        result.OptimizedSize = optimizedSize;
        result.SizeReduction = originalSize - optimizedSize;
        result.OptimizationNeeded = true;

        return result;
    }

    private long CalculateDocumentSize(VersionedReportDataDocument document)
    {
        var json = JsonSerializer.Serialize(document);
        return Encoding.UTF8.GetByteCount(json);
    }
}
```

### 3. Cross-Storage Reference Corruption

**Problem**: SQL entities pointing to non-existent Cosmos documents or blob storage files.

**Symptoms**:
- Null reference exceptions when loading complete reports
- Missing data in report displays
- Inconsistent data across storage systems

**Solution**:
```csharp
public class ReferenceIntegrityService : IReferenceIntegrityService
{
    public async Task<IntegrityReport> CheckAndFixReferencesAsync()
    {
        var report = new IntegrityReport();

        // 1. Check SQL → Cosmos references
        var versionsWithCosmosRefs = await _sqlContext.ReportVersions
            .Where(v => !string.IsNullOrEmpty(v.DataDocumentId))
            .ToListAsync();

        foreach (var version in versionsWithCosmosRefs)
        {
            var documentExists = await _cosmosService.DocumentExistsAsync(version.DataDocumentId);
            if (!documentExists)
            {
                report.BrokenCosmosReferences.Add(new BrokenReference
                {
                    VersionId = version.Id,
                    ReportId = version.ReportId,
                    Reference = version.DataDocumentId,
                    Type = "CosmosDocument"
                });

                // Attempt to recreate document
                await RecreateCosmosDocumentAsync(version);
                report.CosmosReferencesFixed++;
            }
        }

        // 2. Check SQL → Blob references
        var versionsWithBlobRefs = await _sqlContext.ReportVersions
            .Where(v => !string.IsNullOrEmpty(v.ComponentsBlobId))
            .ToListAsync();

        foreach (var version in versionsWithBlobRefs)
        {
            var blobExists = await _blobService.BlobExistsAsync(version.ComponentsBlobId);
            if (!blobExists)
            {
                report.BrokenBlobReferences.Add(new BrokenReference
                {
                    VersionId = version.Id,
                    ReportId = version.ReportId,
                    Reference = version.ComponentsBlobId,
                    Type = "BlobStorage"
                });

                // Attempt to recreate blob
                await RecreateBlobAsync(version);
                report.BlobReferencesFixed++;
            }
        }

        return report;
    }

    private async Task RecreateCosmosDocumentAsync(ReportVersion version)
    {
        try
        {
            var report = await _sqlContext.Reports
                .Include(r => r.Sections)
                    .ThenInclude(s => s.Fields)
                .FirstOrDefaultAsync(r => r.Id == version.ReportId);

            if (report != null)
            {
                var document = TransformToCosmosDocument(report, version);
                await _cosmosService.CreateDocumentAsync(document, document.TenantId);
                _logger.LogInformation("Recreated Cosmos document {DocumentId} for version {VersionId}",
                    document.Id, version.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to recreate Cosmos document for version {VersionId}", version.Id);
        }
    }
}

public class IntegrityReport
{
    public List<BrokenReference> BrokenCosmosReferences { get; } = new();
    public List<BrokenReference> BrokenBlobReferences { get; } = new();
    public int CosmosReferencesFixed { get; set; }
    public int BlobReferencesFixed { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}

public class BrokenReference
{
    public Guid VersionId { get; set; }
    public Guid ReportId { get; set; }
    public string Reference { get; set; }
    public string Type { get; set; }
}
```

### 4. Seeding Performance Issues

**Problem**: Seeding process taking too long or timing out.

**Solutions**:

#### Batch Processing Optimization
```csharp
public class OptimizedSeeder : IOptimizedSeeder
{
    public async Task<SeedingResult> SeedWithOptimizationsAsync(SeedingOptions options)
    {
        // 1. Use parallel processing for independent operations
        var cosmosTask = SeedCosmosInParallelAsync(options.CosmosBatchSize);
        var blobTask = SeedBlobInParallelAsync(options.BlobBatchSize);

        await Task.WhenAll(cosmosTask, blobTask);

        // 2. Use bulk operations where possible
        await BulkUpdateSqlReferencesAsync();

        return new SeedingResult { Success = true };
    }

    private async Task SeedCosmosInParallelAsync(int batchSize)
    {
        var reports = await GetReportsForCosmosSeeding();
        var batches = reports.Chunk(batchSize);

        var semaphore = new SemaphoreSlim(5); // Limit concurrency
        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync();
            try
            {
                await ProcessCosmosBatchAsync(batch);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }
}
```

## Recovery Procedures

### 1. Partial Seeding Failure Recovery

```csharp
public class SeedingRecoveryService : ISeedingRecoveryService
{
    public async Task<RecoveryResult> RecoverFromPartialFailureAsync(SeedingResult failedResult)
    {
        var recovery = new RecoveryResult();

        // 1. Identify what was successfully seeded
        var successfulPhases = failedResult.PhaseResults.Where(p => p.Success).ToList();
        var failedPhase = failedResult.PhaseResults.FirstOrDefault(p => !p.Success);

        _logger.LogInformation("Recovering from failed phase: {FailedPhase}", failedPhase?.PhaseName);

        // 2. Resume from failed phase
        switch (failedPhase?.PhaseName)
        {
            case "Cosmos Documents":
                recovery = await RecoverCosmosPhaseAsync();
                break;
            case "Blob Components":
                recovery = await RecoverBlobPhaseAsync();
                break;
            case "Reference Linking":
                recovery = await RecoverReferenceLinkingAsync();
                break;
            default:
                recovery.Success = false;
                recovery.Message = "Unknown failed phase";
                break;
        }

        return recovery;
    }

    private async Task<RecoveryResult> RecoverCosmosPhaseAsync()
    {
        // Find reports that should have Cosmos documents but don't
        var reportsNeedingRecovery = await _sqlContext.Reports
            .Include(r => r.Versions)
            .Where(r => r.Versions.Any(v => string.IsNullOrEmpty(v.DataDocumentId)))
            .ToListAsync();

        var recovered = 0;
        foreach (var report in reportsNeedingRecovery)
        {
            try
            {
                await ProcessReportForCosmosAsync(report);
                recovered++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to recover Cosmos document for report {ReportId}", report.Id);
            }
        }

        return new RecoveryResult
        {
            Success = true,
            Message = $"Recovered {recovered} of {reportsNeedingRecovery.Count} reports",
            ItemsRecovered = recovered
        };
    }
}

public class RecoveryResult
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public int ItemsRecovered { get; set; }
    public List<string> Errors { get; } = new();
}
```

### 2. Complete Rollback Procedure

```csharp
public class SeedingRollbackService : ISeedingRollbackService
{
    public async Task<RollbackResult> RollbackSeedingAsync(string reason)
    {
        var result = new RollbackResult { Reason = reason };

        try
        {
            _logger.LogWarning("Starting seeding rollback. Reason: {Reason}", reason);

            // 1. Clear SQL references first
            await ClearSqlReferencesAsync();
            result.SqlReferencesCleared = true;

            // 2. Delete blob storage data
            var blobsDeleted = await DeleteSeededBlobsAsync();
            result.BlobsDeleted = blobsDeleted;

            // 3. Delete Cosmos documents
            var documentsDeleted = await DeleteSeededCosmosDocumentsAsync();
            result.CosmosDocumentsDeleted = documentsDeleted;

            // 4. Delete SQL seeded data (optional - usually keep for next attempt)
            if (result.DeleteSqlData)
            {
                await DeleteSeededSqlDataAsync();
                result.SqlDataDeleted = true;
            }

            result.Success = true;
            _logger.LogInformation("Seeding rollback completed successfully");
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "Seeding rollback failed");
        }

        return result;
    }

    private async Task<int> DeleteSeededCosmosDocumentsAsync()
    {
        var documentsDeleted = 0;
        var tenants = await _sqlContext.Reports.Select(r => r.TenantId.ToString()).Distinct().ToListAsync();

        foreach (var tenantId in tenants)
        {
            var documents = await _cosmosService.GetDocumentsByTenantAsync(tenantId);
            var seededDocuments = documents.Where(d => d.Metadata?.Tags?.Contains("seeded_data") == true);

            foreach (var document in seededDocuments)
            {
                try
                {
                    await _cosmosService.DeleteDocumentAsync(document.Id, tenantId);
                    documentsDeleted++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to delete Cosmos document {DocumentId}", document.Id);
                }
            }
        }

        return documentsDeleted;
    }
}

public class RollbackResult
{
    public bool Success { get; set; }
    public string Reason { get; set; }
    public string Error { get; set; }
    public bool SqlReferencesCleared { get; set; }
    public int BlobsDeleted { get; set; }
    public int CosmosDocumentsDeleted { get; set; }
    public bool SqlDataDeleted { get; set; }
    public bool DeleteSqlData { get; set; } = false; // Usually false to preserve for retry
}
```

## Best Practices Summary

1. **Always Follow Dependency Order**: SQL → Cosmos → Blob → References → Validation
2. **Implement Comprehensive Validation**: Validate at each phase before proceeding
3. **Use Batch Processing**: Process data in manageable batches to avoid timeouts
4. **Enable Detailed Logging**: Log all operations for troubleshooting
5. **Plan for Failures**: Implement compensation and recovery mechanisms
6. **Test with Real Data**: Use production-like data volumes for testing
7. **Monitor Performance**: Track seeding metrics and optimize bottlenecks
8. **Maintain Data Integrity**: Always validate cross-storage references
9. **Document Customizations**: Keep clear records of any seeding customizations
10. **Regular Integrity Checks**: Periodically validate data consistency across systems

This comprehensive seeding strategy ensures reliable, consistent, and maintainable data initialization across the multi-storage architecture.
