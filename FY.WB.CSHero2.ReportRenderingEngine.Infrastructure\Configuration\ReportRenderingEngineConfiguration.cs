using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration
{
    /// <summary>
    /// Configuration extension methods for the Report Rendering Engine
    /// </summary>
    public static class ReportRenderingEngineConfiguration
    {
        /// <summary>
        /// Adds Report Rendering Engine services to the dependency injection container
        /// </summary>
        public static IServiceCollection AddReportRenderingEngine(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Configure LLM settings
            services.Configure<LlmConfig>(configuration.GetSection("ReportRenderingEngine:LLM"));

            // Register domain services
            services.AddScoped<IDatabaseService, DatabaseService>();
            services.AddScoped<IHtmlValidator, HtmlValidator>();

            // Register application services
            services.AddScoped<ReportRenderer>();

            // Register infrastructure services
            services.AddScoped<LlmClientFactory>();

            // Register ILlmClient using factory
            services.AddScoped<ILlmClient>(serviceProvider =>
            {
                var factory = serviceProvider.GetRequiredService<LlmClientFactory>();
                var config = serviceProvider.GetRequiredService<IOptions<LlmConfig>>().Value;
                return factory.CreateClient(config);
            });

            // Register HTTP client factory for LLM API calls
            services.AddHttpClient();

            // Add memory cache for prompt caching
            services.AddMemoryCache();

            // CRITICAL FIX: Add the ReportRenderingEngine Infrastructure services
            // This includes IReportStyleService and IReportDataBlobService
            services.AddReportRenderingEngineInfrastructure(configuration);

            return services;
        }

        /// <summary>
        /// Adds Report Rendering Engine services with custom LLM configuration
        /// </summary>
        public static IServiceCollection AddReportRenderingEngine(
            this IServiceCollection services,
            Action<LlmConfig> configureLlm)
        {
            // Configure LLM settings with custom action
            services.Configure(configureLlm);

            // Register domain services
            services.AddScoped<IDatabaseService, DatabaseService>();
            services.AddScoped<IHtmlValidator, HtmlValidator>();

            // Register application services
            services.AddScoped<ReportRenderer>();

            // Register infrastructure services
            services.AddScoped<LlmClientFactory>();

            // Register ILlmClient using factory
            services.AddScoped<ILlmClient>(serviceProvider =>
            {
                var factory = serviceProvider.GetRequiredService<LlmClientFactory>();
                var config = serviceProvider.GetRequiredService<IOptions<LlmConfig>>().Value;
                return factory.CreateClient(config);
            });

            // Register HTTP client factory for LLM API calls
            services.AddHttpClient();

            // Add memory cache for prompt caching
            services.AddMemoryCache();

            return services;
        }

        /// <summary>
        /// Validates the Report Rendering Engine configuration
        /// </summary>
        public static void ValidateReportRenderingEngineConfiguration(
            this IServiceProvider serviceProvider,
            ILogger logger)
        {
            try
            {
                // Test that all required services can be resolved
                var databaseService = serviceProvider.GetRequiredService<IDatabaseService>();
                var htmlValidator = serviceProvider.GetRequiredService<IHtmlValidator>();
                var reportRenderer = serviceProvider.GetRequiredService<ReportRenderer>();
                var llmClientFactory = serviceProvider.GetRequiredService<LlmClientFactory>();

                logger.LogInformation("Report Rendering Engine configuration validation successful");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Report Rendering Engine configuration validation failed");
                throw;
            }
        }
    }
}
