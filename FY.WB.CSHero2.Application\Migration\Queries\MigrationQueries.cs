using MediatR;
using FY.WB.CSHero2.Application.Models.Migration;

namespace FY.WB.CSHero2.Application.Migration.Queries
{
    /// <summary>
    /// Query to get migration status
    /// </summary>
    public class GetMigrationStatusQuery : IRequest<MigrationStatus>
    {
        /// <summary>
        /// Whether to include detailed progress information
        /// </summary>
        public bool IncludeDetails { get; set; } = true;

        /// <summary>
        /// Whether to include recent operations history
        /// </summary>
        public bool IncludeHistory { get; set; } = true;
    }

    /// <summary>
    /// Query to get migration statistics
    /// </summary>
    public class GetMigrationStatisticsQuery : IRequest<MigrationStatistics>
    {
        /// <summary>
        /// Optional tenant ID filter
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Date range start for statistics
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Date range end for statistics
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// Query to get migration progress for a specific operation
    /// </summary>
    public class GetMigrationProgressQuery : IRequest<MigrationProgress>
    {
        /// <summary>
        /// Migration operation ID
        /// </summary>
        public Guid OperationId { get; set; }
    }

    /// <summary>
    /// Query to get reports requiring migration
    /// </summary>
    public class GetReportsRequiringMigrationQuery : IRequest<IEnumerable<ReportMigrationInfo>>
    {
        /// <summary>
        /// Optional tenant ID filter
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Maximum number of reports to return
        /// </summary>
        public int MaxResults { get; set; } = 100;

        /// <summary>
        /// Skip count for pagination
        /// </summary>
        public int Skip { get; set; } = 0;

        /// <summary>
        /// Sort order for results
        /// </summary>
        public MigrationSortOrder SortOrder { get; set; } = MigrationSortOrder.Priority;
    }

    /// <summary>
    /// Query to get migration history for a specific report
    /// </summary>
    public class GetMigrationHistoryQuery : IRequest<IEnumerable<MigrationHistoryEntry>>
    {
        /// <summary>
        /// Report ID
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Maximum number of history entries to return
        /// </summary>
        public int MaxResults { get; set; } = 50;

        /// <summary>
        /// Skip count for pagination
        /// </summary>
        public int Skip { get; set; } = 0;
    }

    /// <summary>
    /// Sort order options for migration queries
    /// </summary>
    public enum MigrationSortOrder
    {
        Priority,
        LastModified,
        ReportName,
        DataSize,
        Complexity,
        TenantId
    }
}