# Complete Cache Clearing Guide for Next.js Project

## Overview
Cache corruption can cause both TypeScript compilation errors and hydration issues. This guide provides comprehensive steps to clear all caches and reset your development environment.

## Step-by-Step Cache Clearing Process

### 1. Stop All Running Processes
```bash
# Stop the development server if running
# Press Ctrl+C in the terminal running `npm run dev`

# Kill any lingering Node.js processes (Windows)
taskkill /f /im node.exe
```

### 2. Clear Next.js Build Cache
```bash
# Navigate to your UI project directory
cd FY.WB.CSHero2.UI

# Remove Next.js build cache
rmdir /s /q .next
# Alternative: rd /s /q .next

# Remove TypeScript build cache
del tsconfig.tsbuildinfo
```

### 3. Clear Node.js Module Cache
```bash
# Remove node_modules directory
rmdir /s /q node_modules

# Remove package lock files
del package-lock.json
del yarn.lock
del pnpm-lock.yaml
```

### 4. Clear npm/yarn/pnpm Cache
```bash
# For npm users
npm cache clean --force

# For yarn users (if you use yarn)
yarn cache clean

# For pnpm users (if you use pnpm)
pnpm store prune
```

### 5. Clear Windows Temp Files (Optional but Recommended)
```bash
# Clear Windows temp directory
del /q /f /s %TEMP%\*
```

### 6. Clear Browser Cache
- Open your browser's Developer Tools (F12)
- Right-click the refresh button
- Select "Empty Cache and Hard Reload"
- Or manually clear browser cache in settings

### 7. Reinstall Dependencies
```bash
# Reinstall all dependencies
npm install

# Alternative: Use specific package manager
# yarn install
# pnpm install
```

### 8. Verify Environment
```bash
# Check Node.js version
node --version

# Check npm version
npm --version

# Verify TypeScript installation
npx tsc --version
```

## Complete PowerShell Script

Create a file called `clear-cache.ps1` in your project root:

```powershell
# Clear-Cache.ps1 - Complete cache clearing script

Write-Host "Starting cache clearing process..." -ForegroundColor Green

# Navigate to UI directory
Set-Location "FY.WB.CSHero2.UI"

# Stop any running Node processes
Write-Host "Stopping Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force

# Remove Next.js cache
Write-Host "Removing Next.js cache..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next"
    Write-Host "✓ Removed .next directory" -ForegroundColor Green
}

# Remove TypeScript cache
Write-Host "Removing TypeScript cache..." -ForegroundColor Yellow
if (Test-Path "tsconfig.tsbuildinfo") {
    Remove-Item -Force "tsconfig.tsbuildinfo"
    Write-Host "✓ Removed tsconfig.tsbuildinfo" -ForegroundColor Green
}

# Remove node_modules
Write-Host "Removing node_modules..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "✓ Removed node_modules directory" -ForegroundColor Green
}

# Remove lock files
Write-Host "Removing lock files..." -ForegroundColor Yellow
@("package-lock.json", "yarn.lock", "pnpm-lock.yaml") | ForEach-Object {
    if (Test-Path $_) {
        Remove-Item -Force $_
        Write-Host "✓ Removed $_" -ForegroundColor Green
    }
}

# Clear npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force
Write-Host "✓ npm cache cleared" -ForegroundColor Green

# Reinstall dependencies
Write-Host "Reinstalling dependencies..." -ForegroundColor Yellow
npm install
Write-Host "✓ Dependencies reinstalled" -ForegroundColor Green

Write-Host "Cache clearing complete!" -ForegroundColor Green
Write-Host "You can now run 'npm run dev' to start the development server." -ForegroundColor Cyan
```

## Manual Command Sequence (Copy & Paste)

```bash
# Navigate to UI directory
cd FY.WB.CSHero2.UI

# Stop processes and clear caches
taskkill /f /im node.exe 2>nul
rmdir /s /q .next 2>nul
del tsconfig.tsbuildinfo 2>nul
rmdir /s /q node_modules 2>nul
del package-lock.json 2>nul
del yarn.lock 2>nul
del pnpm-lock.yaml 2>nul

# Clear npm cache and reinstall
npm cache clean --force
npm install

# Verify installation
node --version
npm --version
```

## Post-Cache-Clear Verification

### 1. Check File Structure
Verify these directories/files are recreated:
- `node_modules/` (should be recreated)
- `package-lock.json` (should be recreated)
- `.next/` (will be created on next build)

### 2. Test Build
```bash
# Try building the project
npm run build

# If build succeeds, start development server
npm run dev
```

### 3. Check for Remaining Issues
- TypeScript compilation errors should be resolved if they were cache-related
- Hydration errors may still persist if they're code-related

## Troubleshooting

### If Cache Clearing Doesn't Resolve Issues

1. **Check Node.js Version Compatibility**
   ```bash
   node --version
   # Should be compatible with Next.js 14.2.29
   ```

2. **Verify Package Manager**
   - Stick to one package manager (npm recommended)
   - Don't mix npm, yarn, and pnpm

3. **Check Windows Permissions**
   - Run terminal as Administrator if needed
   - Ensure no files are locked by other processes

4. **Environment Variables**
   - Check if any environment variables are causing issues
   - Verify `.env` files are properly configured

### If Hydration Issues Persist

The hydration issues may be code-related rather than cache-related. After clearing cache, we'll need to:
1. Fix the TypeScript compilation error
2. Implement simplified hydration strategy
3. Review provider configuration

## Next Steps After Cache Clearing

1. **Test the build**: `npm run build`
2. **Start development server**: `npm run dev`
3. **Check browser console** for any remaining errors
4. **Report results** so we can proceed with code fixes if needed

## Expected Outcomes

**Success Indicators:**
- Build completes without TypeScript errors
- Development server starts without issues
- No hydration errors in browser console
- Application loads consistently

**If Issues Persist:**
- TypeScript compilation error → Code fix needed
- Hydration errors → Provider configuration fix needed
- Build failures → Dependency or configuration issue