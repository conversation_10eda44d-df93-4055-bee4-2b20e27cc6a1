// Re-export functions from api-metrics.ts with adaptations for route.ts
import { 
  getWTDMetrics as apiGetWTDMetrics,
  getYTDMetrics as apiGetYTDMetrics,
  getMTDMetrics as apiGetMTDMetrics,
  getTodayMetrics as apiGetTodayMetrics
} from '@/lib/api-metrics';

export async function getWTDMetrics() {
  return apiGetWTDMetrics();
}

export async function getYTDMetrics() {
  return apiGetYTDMetrics();
}

export async function getMTDMetrics() {
  return apiGetMTDMetrics();
}

export async function getTodayMetrics() {
  return apiGetTodayMetrics();
}
