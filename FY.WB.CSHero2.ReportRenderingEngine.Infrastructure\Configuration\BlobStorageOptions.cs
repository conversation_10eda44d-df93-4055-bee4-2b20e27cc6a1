namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration
{
    /// <summary>
    /// Configuration options for Azure Blob Storage
    /// </summary>
    public class BlobStorageOptions
    {
        /// <summary>
        /// Configuration section name
        /// </summary>
        public const string SectionName = "BlobStorage";

        /// <summary>
        /// Connection string for Azure Blob Storage
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Container name for report components (HTML/CSS)
        /// </summary>
        public string ContainerName { get; set; } = "report-components";

        /// <summary>
        /// Container name for report data (legacy support)
        /// </summary>
        public string ReportDataContainer { get; set; } = "report-data";

        /// <summary>
        /// Path pattern for rendered Next.js components
        /// </summary>
        public string ComponentPathPattern { get; set; } = "tenants/{tenantId}/reports/{reportId}/versions/v{version}/components/";

        /// <summary>
        /// Path pattern for rendered HTML content
        /// </summary>
        public string RenderedHtmlPattern { get; set; } = "tenants/{tenantId}/reports/{reportId}/versions/v{version}/rendered/";

        /// <summary>
        /// Path pattern for template components
        /// </summary>
        public string TemplateComponentPattern { get; set; } = "tenants/{tenantId}/templates/{templateId}/components/";

        /// <summary>
        /// Path pattern for style documents
        /// </summary>
        public string StyleDocumentPattern { get; set; } = "tenants/{tenantId}/styles/";

        /// <summary>
        /// Maximum retry attempts
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int RequestTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Maximum concurrent operations
        /// </summary>
        public int MaxConcurrentOperations { get; set; } = 10;

        /// <summary>
        /// Enable server-side encryption
        /// </summary>
        public bool EnableEncryption { get; set; } = true;

        /// <summary>
        /// Default content type for JSON data
        /// </summary>
        public string DefaultContentType { get; set; } = "application/json";
    }
}