using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Application.Models.MultiStorage;
using FY.WB.CSHero2.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FY.WB.CSHero2.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for Cosmos DB operations (report data - sections and fields)
    /// </summary>
    public class ReportDataRepository : IReportDataRepository
    {
        private readonly ICosmosDbService _cosmosDbService;
        private readonly ILogger<ReportDataRepository> _logger;

        public ReportDataRepository(ICosmosDbService cosmosDbService, ILogger<ReportDataRepository> logger)
        {
            _cosmosDbService = cosmosDbService;
            _logger = logger;
        }

        #region Document Operations

        public async Task<ReportData?> GetReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                
                var result = await _cosmosDbService.GetItemAsync<ReportData>(documentId, tenantId);
                
                if (result != null)
                {
                    _logger.LogDebug("Successfully retrieved report data document {DocumentId}", documentId);
                }
                else
                {
                    _logger.LogDebug("Report data document {DocumentId} not found", documentId);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                throw;
            }
        }

        public async Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report data document {DocumentId} for tenant {TenantId}", data.Id, data.TenantId);
                
                // Set metadata
                data.Metadata.CreatedAt = DateTime.UtcNow;
                data.Metadata.LastModifiedAt = DateTime.UtcNow;
                data.Metadata.Version = 1;
                
                var result = await _cosmosDbService.UpsertItemAsync(data, data.TenantId);
                
                _logger.LogInformation("Successfully created report data document {DocumentId}", result.Id);
                return result.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report data document {DocumentId} for tenant {TenantId}", data.Id, data.TenantId);
                throw;
            }
        }

        public async Task<string> UpdateReportDataAsync(ReportData data, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report data document {DocumentId} for tenant {TenantId}", data.Id, data.TenantId);
                
                // Update metadata
                data.Metadata.LastModifiedAt = DateTime.UtcNow;
                data.Metadata.Version++;
                
                var result = await _cosmosDbService.UpsertItemAsync(data, data.TenantId);
                
                _logger.LogInformation("Successfully updated report data document {DocumentId}", result.Id);
                return result.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report data document {DocumentId} for tenant {TenantId}", data.Id, data.TenantId);
                throw;
            }
        }

        public async Task DeleteReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                
                await _cosmosDbService.DeleteItemAsync(documentId, tenantId);
                
                _logger.LogInformation("Successfully deleted report data document {DocumentId}", documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                throw;
            }
        }

        #endregion

        #region Query Operations

        public async Task<IEnumerable<ReportData>> GetReportDataByReportIdAsync(string reportId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data for report {ReportId} in tenant {TenantId}", reportId, tenantId);
                
                var query = $"SELECT * FROM c WHERE c.reportId = '{reportId}' AND c.tenantId = '{tenantId}'";
                var results = await _cosmosDbService.GetItemsAsync<ReportData>(query);
                
                _logger.LogDebug("Found {Count} report data documents for report {ReportId}", results.Count(), reportId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data for report {ReportId} in tenant {TenantId}", reportId, tenantId);
                throw;
            }
        }

        public async Task<IEnumerable<ReportData>> GetReportDataByTenantAsync(string tenantId, int skip = 0, int take = 100, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data for tenant {TenantId} (skip: {Skip}, take: {Take})", tenantId, skip, take);
                
                var query = $"SELECT * FROM c WHERE c.tenantId = '{tenantId}' ORDER BY c.metadata.lastModifiedAt DESC OFFSET {skip} LIMIT {take}";
                var results = await _cosmosDbService.GetItemsAsync<ReportData>(query);
                
                _logger.LogDebug("Found {Count} report data documents for tenant {TenantId}", results.Count(), tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data for tenant {TenantId}", tenantId);
                throw;
            }
        }

        #endregion

        #region Section Operations

        public async Task<ReportSection?> GetReportSectionAsync(string documentId, string sectionId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting section {SectionId} from document {DocumentId}", sectionId, documentId);
                
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    return null;
                }
                
                var section = reportData.Sections.FirstOrDefault(s => s.Id == sectionId);
                
                if (section != null)
                {
                    _logger.LogDebug("Successfully retrieved section {SectionId}", sectionId);
                }
                else
                {
                    _logger.LogDebug("Section {SectionId} not found in document {DocumentId}", sectionId, documentId);
                }
                
                return section;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting section {SectionId} from document {DocumentId}", sectionId, documentId);
                throw;
            }
        }

        public async Task UpdateReportSectionAsync(string documentId, ReportSection section, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating section {SectionId} in document {DocumentId}", section.Id, documentId);
                
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    throw new InvalidOperationException($"Report data document {documentId} not found");
                }
                
                var existingSection = reportData.Sections.FirstOrDefault(s => s.Id == section.Id);
                if (existingSection != null)
                {
                    var index = reportData.Sections.IndexOf(existingSection);
                    reportData.Sections[index] = section;
                }
                else
                {
                    reportData.Sections.Add(section);
                }
                
                await UpdateReportDataAsync(reportData, cancellationToken);
                
                _logger.LogInformation("Successfully updated section {SectionId} in document {DocumentId}", section.Id, documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating section {SectionId} in document {DocumentId}", section.Id, documentId);
                throw;
            }
        }

        #endregion

        #region Field Operations

        public async Task<ReportSectionField?> GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting field {FieldId} from section {SectionId} in document {DocumentId}", fieldId, sectionId, documentId);
                
                var section = await GetReportSectionAsync(documentId, sectionId, tenantId, cancellationToken);
                if (section == null)
                {
                    return null;
                }
                
                var field = section.Fields.FirstOrDefault(f => f.Id == fieldId);
                
                if (field != null)
                {
                    _logger.LogDebug("Successfully retrieved field {FieldId}", fieldId);
                }
                else
                {
                    _logger.LogDebug("Field {FieldId} not found in section {SectionId}", fieldId, sectionId);
                }
                
                return field;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting field {FieldId} from section {SectionId} in document {DocumentId}", fieldId, sectionId, documentId);
                throw;
            }
        }

        public async Task UpdateReportSectionFieldAsync(string documentId, string sectionId, ReportSectionField field, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating field {FieldId} in section {SectionId} of document {DocumentId}", field.Id, sectionId, documentId);
                
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    throw new InvalidOperationException($"Report data document {documentId} not found");
                }
                
                var section = reportData.Sections.FirstOrDefault(s => s.Id == sectionId);
                if (section == null)
                {
                    throw new InvalidOperationException($"Section {sectionId} not found in document {documentId}");
                }
                
                var existingField = section.Fields.FirstOrDefault(f => f.Id == field.Id);
                if (existingField != null)
                {
                    var index = section.Fields.IndexOf(existingField);
                    section.Fields[index] = field;
                }
                else
                {
                    section.Fields.Add(field);
                }
                
                await UpdateReportDataAsync(reportData, cancellationToken);
                
                _logger.LogInformation("Successfully updated field {FieldId} in section {SectionId}", field.Id, sectionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating field {FieldId} in section {SectionId} of document {DocumentId}", field.Id, sectionId, documentId);
                throw;
            }
        }

        #endregion

        #region Utility Operations

        public async Task<bool> DocumentExistsAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                return result != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if document {DocumentId} exists for tenant {TenantId}", documentId, tenantId);
                throw;
            }
        }

        public async Task<long> GetDocumentSizeAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    return 0;
                }
                
                var json = JsonSerializer.Serialize(reportData);
                return System.Text.Encoding.UTF8.GetByteCount(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting document size for {DocumentId} in tenant {TenantId}", documentId, tenantId);
                throw;
            }
        }

        #endregion
    }
}
