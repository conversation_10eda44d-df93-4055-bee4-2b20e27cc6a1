<!-- Guide Purpose: Quick reference for common operations, troubleshooting, and gotchas in multi-storage development -->
<!-- Target Audience: Developers working on day-to-day multi-storage functionality -->

# Developer Quick Reference Guide

## Overview

This guide provides quick access to common operations, troubleshooting steps, and important gotchas when working with the multi-storage architecture. Use this as your go-to reference for daily development tasks.

## Quick Start Checklist

### Setting Up Development Environment

```bash
# 1. Verify connection strings in appsettings.json
# SQL Server
"ConnectionStrings": {
  "DefaultConnection": "Server=localhost;Database=CSHero2;Trusted_Connection=true;"
}

# Cosmos DB
"CosmosDb": {
  "ConnectionString": "AccountEndpoint=https://localhost:8081/;AccountKey=...",
  "DatabaseName": "CSHeroReports",
  "ContainerName": "Reports"
}

# Blob Storage
"BlobStorage": {
  "ConnectionString": "UseDevelopmentStorage=true",
  "ContainerName": "report-components"
}
```

### Essential Services Registration

```csharp
// In Program.cs or Startup.cs
services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));

services.AddCosmosDb(configuration);
services.AddBlobStorage(configuration);
services.AddMultiStorageServices();
```

## Common Operations

### 1. Creating a New Report

```csharp
public async Task<Guid> CreateReportAsync(CreateReportRequest request)
{
    using var transaction = await _context.Database.BeginTransactionAsync();
    try
    {
        // 1. Create SQL entities first
        var report = new Report
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            ClientId = request.ClientId,
            TenantId = request.TenantId,
            Status = "Draft",
            Author = request.Author
        };

        var version = new ReportVersion
        {
            Id = Guid.NewGuid(),
            ReportId = report.Id,
            VersionNumber = 1,
            IsCurrent = true,
            Description = "Initial version"
        };

        _context.Reports.Add(report);
        _context.ReportVersions.Add(version);
        await _context.SaveChangesAsync();

        // 2. Create Cosmos document
        var documentId = GenerateVersionedDocumentId(report.Id, version.Id);
        var cosmosDoc = CreateCosmosDocument(report, version);
        await _cosmosService.CreateDocumentAsync(cosmosDoc, report.TenantId.ToString());

        // 3. Create blob storage
        var componentsBlob = CreateComponentsBlob(report, version);
        var blobPath = await _blobService.SaveComponentsAsync(componentsBlob);

        // 4. Update SQL with references
        version.DataDocumentId = documentId;
        version.ComponentsBlobId = blobPath;
        version.StorageStrategy = "MultiStorage";
        await _context.SaveChangesAsync();

        await transaction.CommitAsync();
        return report.Id;
    }
    catch
    {
        await transaction.RollbackAsync();
        throw;
    }
}
```

### 2. Loading Complete Report Data

```csharp
public async Task<CompleteReportDto> GetCompleteReportAsync(Guid reportId)
{
    // 1. Get metadata from SQL
    var report = await _context.Reports
        .Include(r => r.Versions.Where(v => v.IsCurrent))
        .Include(r => r.Client)
        .FirstOrDefaultAsync(r => r.Id == reportId);

    if (report == null) return null;

    var currentVersion = report.Versions.FirstOrDefault();
    if (currentVersion == null) return MapMetadataOnly(report);

    // 2. Load external data in parallel
    var tasks = new List<Task>();
    VersionedReportDataDocument documentData = null;
    ComponentsBlob componentsData = null;

    if (!string.IsNullOrEmpty(currentVersion.DataDocumentId))
    {
        tasks.Add(Task.Run(async () =>
        {
            documentData = await _cosmosService.GetDocumentAsync(currentVersion.DataDocumentId);
        }));
    }

    if (!string.IsNullOrEmpty(currentVersion.ComponentsBlobId))
    {
        tasks.Add(Task.Run(async () =>
        {
            componentsData = await _blobService.GetComponentsAsync(currentVersion.ComponentsBlobId);
        }));
    }

    await Task.WhenAll(tasks);

    // 3. Combine results
    return new CompleteReportDto
    {
        Metadata = _mapper.Map<ReportDto>(report),
        DocumentData = documentData,
        Components = componentsData
    };
}
```

### 3. Updating Report Data

```csharp
public async Task UpdateReportDataAsync(Guid reportId, UpdateReportRequest request)
{
    // 1. Get current version
    var currentVersion = await _context.ReportVersions
        .Include(v => v.Report)
        .FirstOrDefaultAsync(v => v.ReportId == reportId && v.IsCurrent);

    if (currentVersion == null)
        throw new NotFoundException($"Current version not found for report {reportId}");

    // 2. Update Cosmos document
    if (!string.IsNullOrEmpty(currentVersion.DataDocumentId))
    {
        var document = await _cosmosService.GetDocumentAsync(currentVersion.DataDocumentId);
        if (document != null)
        {
            // Update document fields
            document.ReportName = request.Name;
            document.Status = request.Status;
            // ... other updates

            await _cosmosService.UpdateDocumentAsync(document, document.TenantId);
        }
    }

    // 3. Update SQL metadata
    currentVersion.Report.Name = request.Name;
    currentVersion.Report.Status = request.Status;
    await _context.SaveChangesAsync();
}
```

### 4. Creating New Version

```csharp
public async Task<Guid> CreateNewVersionAsync(Guid reportId, string description)
{
    var report = await _context.Reports
        .Include(r => r.Versions)
        .FirstOrDefaultAsync(r => r.Id == reportId);

    var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
    
    // 1. Mark current version as not current
    if (currentVersion != null)
        currentVersion.IsCurrent = false;

    // 2. Create new version
    var newVersion = new ReportVersion
    {
        Id = Guid.NewGuid(),
        ReportId = reportId,
        VersionNumber = report.Versions.Max(v => v.VersionNumber) + 1,
        IsCurrent = true,
        Description = description
    };

    _context.ReportVersions.Add(newVersion);

    // 3. Copy data from previous version
    if (currentVersion?.DataDocumentId != null)
    {
        var sourceDocument = await _cosmosService.GetDocumentAsync(currentVersion.DataDocumentId);
        var newDocument = CloneDocument(sourceDocument, newVersion);
        await _cosmosService.CreateDocumentAsync(newDocument, newDocument.TenantId);
        newVersion.DataDocumentId = newDocument.Id;
    }

    await _context.SaveChangesAsync();
    return newVersion.Id;
}
```

## Troubleshooting Quick Fixes

### 1. "Document Not Found" Errors

**Symptoms**: `CosmosException` with status code 404

**Quick Fix**:
```csharp
// Check if document ID exists in SQL but not in Cosmos
var version = await _context.ReportVersions.FindAsync(versionId);
if (!string.IsNullOrEmpty(version.DataDocumentId))
{
    var exists = await _cosmosService.DocumentExistsAsync(version.DataDocumentId);
    if (!exists)
    {
        // Recreate document from SQL data
        var report = await _context.Reports
            .Include(r => r.Sections).ThenInclude(s => s.Fields)
            .FirstOrDefaultAsync(r => r.Id == version.ReportId);
        
        var newDocument = TransformToCosmosDocument(report, version);
        await _cosmosService.CreateDocumentAsync(newDocument, newDocument.TenantId);
    }
}
```

### 2. "Blob Not Found" Errors

**Symptoms**: `RequestFailedException` with status code 404

**Quick Fix**:
```csharp
// Check and recreate missing blob
var version = await _context.ReportVersions.FindAsync(versionId);
if (!string.IsNullOrEmpty(version.ComponentsBlobId))
{
    var exists = await _blobService.BlobExistsAsync(version.ComponentsBlobId);
    if (!exists)
    {
        // Regenerate components blob
        var report = await _context.Reports
            .Include(r => r.Sections).ThenInclude(s => s.Fields)
            .FirstOrDefaultAsync(r => r.Id == version.ReportId);
        
        var components = await _componentGenerator.GenerateComponentsAsync(report, version);
        var newBlobPath = await _blobService.SaveComponentsAsync(components);
        
        version.ComponentsBlobId = newBlobPath;
        await _context.SaveChangesAsync();
    }
}
```

### 3. Tenant Isolation Violations

**Symptoms**: `UnauthorizedAccessException` or cross-tenant data access

**Quick Fix**:
```csharp
// Validate tenant access before operations
public void ValidateTenantAccess(string resourceTenantId)
{
    var currentTenantId = _currentTenant.TenantId?.ToString();
    if (string.IsNullOrEmpty(currentTenantId))
        throw new UnauthorizedAccessException("Tenant context required");
    
    if (currentTenantId != resourceTenantId)
        throw new UnauthorizedAccessException("Cross-tenant access denied");
}

// Use before any data access
ValidateTenantAccess(report.TenantId.ToString());
```

### 4. Document Size Limit Exceeded

**Symptoms**: `CosmosException` with status code 413

**Quick Fix**:
```csharp
// Check document size before saving
public async Task<bool> ValidateDocumentSizeAsync(VersionedReportDataDocument document)
{
    var json = JsonSerializer.Serialize(document);
    var sizeInBytes = Encoding.UTF8.GetByteCount(json);
    
    if (sizeInBytes > 2 * 1024 * 1024) // 2MB limit
    {
        // Move large data to blob storage
        if (document.JsonData != null)
        {
            var blobPath = await MoveLargeDataToBlobAsync(document.JsonData, document.ReportId);
            document.JsonData = new Dictionary<string, object>
            {
                ["_blobReference"] = blobPath,
                ["_dataMovedToBlob"] = true
            };
        }
        return false; // Indicates optimization was needed
    }
    return true;
}
```

## Performance Optimization Tips

### 1. Efficient Data Loading

```csharp
// ✅ Good: Load only what you need
public async Task<ReportListDto> GetReportListAsync()
{
    return await _context.Reports
        .Select(r => new ReportListDto
        {
            Id = r.Id,
            Name = r.Name,
            Status = r.Status,
            ClientName = r.ClientName, // Denormalized
            CreatedAt = r.CreationTime
        })
        .ToListAsync();
}

// ❌ Bad: Loading complete data for lists
public async Task<List<CompleteReportDto>> GetReportListAsync()
{
    var reports = await _context.Reports.ToListAsync();
    var result = new List<CompleteReportDto>();
    
    foreach (var report in reports)
    {
        result.Add(await GetCompleteReportAsync(report.Id)); // N+1 problem
    }
    
    return result;
}
```

### 2. Caching Strategies

```csharp
// Cache frequently accessed data
public async Task<ReportDto> GetReportWithCacheAsync(Guid reportId)
{
    var cacheKey = $"report:{reportId}";
    
    return await _cachingService.GetOrSetAsync(
        cacheKey,
        async () => await GetReportAsync(reportId),
        TimeSpan.FromMinutes(15)
    );
}

// Invalidate cache on updates
public async Task UpdateReportAsync(Guid reportId, UpdateReportRequest request)
{
    await UpdateReportDataAsync(reportId, request);
    
    // Invalidate related caches
    await _cachingService.InvalidateAsync($"report:{reportId}");
    await _cachingService.InvalidateAsync($"report-list:tenant:{_currentTenant.TenantId}");
}
```

### 3. Batch Operations

```csharp
// ✅ Good: Batch multiple operations
public async Task CreateMultipleReportsAsync(List<CreateReportRequest> requests)
{
    // 1. Batch SQL operations
    var reports = requests.Select(r => new Report { /* ... */ }).ToList();
    _context.Reports.AddRange(reports);
    await _context.SaveChangesAsync();
    
    // 2. Batch Cosmos operations
    var cosmosOperations = reports.Select(r => CreateCosmosDocument(r));
    await _cosmosService.BulkCreateAsync(cosmosOperations);
    
    // 3. Batch blob operations
    var blobOperations = reports.Select(r => CreateComponentsBlob(r));
    await _blobService.BulkUploadAsync(blobOperations);
}

// ❌ Bad: Individual operations in loop
public async Task CreateMultipleReportsAsync(List<CreateReportRequest> requests)
{
    foreach (var request in requests)
    {
        await CreateReportAsync(request); // Each call is a separate transaction
    }
}
```

## Common Gotchas and Solutions

### 1. Document ID Generation

**Gotcha**: Using inconsistent document ID formats

```csharp
// ✅ Correct: Use standardized format
public static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
{
    return $"rpt_{reportId:N}_v_{versionId:N}";
}

// ❌ Wrong: Inconsistent formats
var documentId = $"{reportId}-{versionId}"; // Missing prefix
var documentId = $"report_{reportId}_version_{versionId}"; // Too verbose
```

### 2. Tenant Partitioning

**Gotcha**: Forgetting to set partition key for Cosmos operations

```csharp
// ✅ Correct: Always specify partition key
await _container.ReadItemAsync<VersionedReportDataDocument>(
    documentId, 
    new PartitionKey(tenantId) // Required!
);

// ❌ Wrong: Missing partition key
await _container.ReadItemAsync<VersionedReportDataDocument>(documentId);
```

### 3. Blob Path Construction

**Gotcha**: Hardcoding blob paths instead of using generators

```csharp
// ✅ Correct: Use path generator
var blobPath = BlobPathGenerator.GenerateComponentsPath(
    tenantId, reportId, versionNumber);

// ❌ Wrong: Hardcoded paths
var blobPath = $"reports/{reportId}/components.json"; // Missing tenant isolation
```

### 4. Transaction Boundaries

**Gotcha**: Not handling cross-storage transaction failures

```csharp
// ✅ Correct: Proper transaction handling
using var transaction = await _context.Database.BeginTransactionAsync();
try
{
    // SQL operations
    await _context.SaveChangesAsync();
    
    // External operations
    await _cosmosService.CreateDocumentAsync(document);
    await _blobService.SaveAsync(blob);
    
    // Update SQL with references
    await _context.SaveChangesAsync();
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    // Cleanup external storage if needed
    throw;
}

// ❌ Wrong: No transaction management
await _context.SaveChangesAsync();
await _cosmosService.CreateDocumentAsync(document); // Could fail
await _blobService.SaveAsync(blob); // Could fail
// No way to rollback SQL changes
```

## Debugging Tools and Techniques

### 1. Logging Best Practices

```csharp
// Use structured logging with correlation IDs
public async Task<ReportDto> GetReportAsync(Guid reportId)
{
    using var activity = Activity.StartActivity("GetReport");
    activity?.SetTag("report.id", reportId.ToString());
    
    _logger.LogInformation("Getting report {ReportId}", reportId);
    
    try
    {
        var result = await LoadReportDataAsync(reportId);
        activity?.SetTag("result", "success");
        return result;
    }
    catch (Exception ex)
    {
        activity?.SetTag("error", true);
        _logger.LogError(ex, "Failed to get report {ReportId}", reportId);
        throw;
    }
}
```

### 2. Health Checks

```csharp
// Monitor storage system health
public class StorageHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var checks = await Task.WhenAll(
            CheckSqlHealthAsync(),
            CheckCosmosHealthAsync(),
            CheckBlobHealthAsync()
        );
        
        var unhealthy = checks.Where(c => !c.IsHealthy).ToList();
        
        if (!unhealthy.Any())
            return HealthCheckResult.Healthy("All storage systems operational");
            
        return HealthCheckResult.Degraded($"Issues: {string.Join(", ", unhealthy.Select(c => c.System))}");
    }
}
```

### 3. Data Validation Tools

```csharp
// Validate data consistency across storage systems
public async Task<ValidationReport> ValidateDataConsistencyAsync(Guid reportId)
{
    var report = new ValidationReport { ReportId = reportId };
    
    // Check SQL → Cosmos consistency
    var sqlReport = await _context.Reports.FindAsync(reportId);
    var cosmosDoc = await _cosmosService.GetDocumentAsync(sqlReport.DataDocumentId);
    
    if (sqlReport.Name != cosmosDoc.ReportName)
        report.AddInconsistency("Name mismatch between SQL and Cosmos");
    
    if (sqlReport.Status != cosmosDoc.Status)
        report.AddInconsistency("Status mismatch between SQL and Cosmos");
    
    // Check blob references
    var version = await _context.ReportVersions
        .FirstOrDefaultAsync(v => v.ReportId == reportId && v.IsCurrent);
    
    if (!string.IsNullOrEmpty(version.ComponentsBlobId))
    {
        var blobExists = await _blobService.BlobExistsAsync(version.ComponentsBlobId);
        if (!blobExists)
            report.AddInconsistency($"Referenced blob not found: {version.ComponentsBlobId}");
    }
    
    return report;
}
```

## Testing Patterns

### 1. Integration Test Setup

```csharp
[TestFixture]
public class MultiStorageIntegrationTests
{
    private TestServer _server;
    private HttpClient _client;
    private IServiceScope _scope;

    [SetUp]
    public async Task SetUp()
    {
        var builder = new WebApplicationBuilder();
        
        // Use test configuration
        builder.Configuration.AddJsonFile("appsettings.Test.json");
        
        // Register test services
        builder.Services.AddTestDatabase();
        builder.Services.AddTestCosmosDb();
        builder.Services.AddTestBlobStorage();
        
        _server = new TestServer(builder);
        _client = _server.CreateClient();
        _scope = _server.Services.CreateScope();
        
        // Clean test data
        await CleanupTestDataAsync();
    }

    [Test]
    public async Task CreateReport_ShouldStoreInAllSystems()
    {
        // Arrange
        var request = new CreateReportRequest
        {
            Name = "Test Report",
            ClientId = TestData.ClientId,
            TenantId = TestData.TenantId
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/reports", request);
        var reportId = await response.Content.ReadFromJsonAsync<Guid>();

        // Assert
        var reportService = _scope.ServiceProvider.GetRequiredService<IReportService>();
        var report = await reportService.GetCompleteReportAsync(reportId);
        
        Assert.That(report, Is.Not.Null);
        Assert.That(report.DocumentData, Is.Not.Null);
        Assert.That(report.Components, Is.Not.Null);
    }
}
```

### 2. Unit Test Patterns

```csharp
[TestFixture]
public class ReportServiceTests
{
    private Mock<IApplicationDbContext> _mockContext;
    private Mock<ICosmosDbService> _mockCosmosService;
    private Mock<IBlobStorageService> _mockBlobService;
    private ReportService _service;

    [SetUp]
    public void SetUp()
    {
        _mockContext = new Mock<IApplicationDbContext>();
        _mockCosmosService = new Mock<ICosmosDbService>();
        _mockBlobService = new Mock<IBlobStorageService>();
        
        _service = new ReportService(
            _mockContext.Object,
            _mockCosmosService.Object,
            _mockBlobService.Object
        );
    }

    [Test]
    public async Task GetReport_WhenCosmosDocumentMissing_ShouldReturnMetadataOnly()
    {
        // Arrange
        var reportId = Guid.NewGuid();
        var report = new Report { Id = reportId, Name = "Test Report" };
        
        _mockContext.Setup(c => c.Reports.FindAsync(reportId))
            .ReturnsAsync(report);
        
        _mockCosmosService.Setup(c => c.GetDocumentAsync(It.IsAny<string>()))
            .ReturnsAsync((VersionedReportDataDocument)null);

        // Act
        var result = await _service.GetReportAsync(reportId);

        // Assert
        Assert.That(result.Metadata, Is.Not.Null);
        Assert.That(result.DocumentData, Is.Null);
    }
}
```

## Configuration Examples

### 1. Development Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CSHero2_Dev;Trusted_Connection=true;"
  },
  "CosmosDb": {
    "ConnectionString": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************",
    "DatabaseName": "CSHeroReports_Dev",
    "ContainerName": "Reports"
  },
  "BlobStorage": {
    "ConnectionString": "UseDevelopmentStorage=true",
    "ContainerName": "report-components-dev"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Azure.Core": "Warning"
    }
  }
}
```

### 2. Production Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-sql-server;Database=CSHero2;User Id=app_user;Password=***;"
  },
  "CosmosDb": {
    "ConnectionString": "AccountEndpoint=https://prod-cosmos.documents.azure.com:443/;AccountKey=***",
    "DatabaseName": "CSHeroReports",
    "ContainerName": "Reports"
  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=prodstorage;AccountKey=***",
    "ContainerName": "report-components"
  },
  "MultiStorage": {
    "EnableRetries": true,
    "MaxRetryAttempts": 3,
    "RetryDelayMs": 1000
  }
}
```

## Useful Code Snippets

### 1. Document ID Utilities

```csharp
public static class DocumentIdHelper
{
    public static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
        => $"rpt_{reportId:N}_v_{versionId:N}";
    
    public static string GenerateDraftDocumentId(Guid reportId)
        => $"rpt_{reportId:N}_draft";
    
    public static (Guid reportId, Guid versionId) ParseVersionedDocumentId(string documentId)
    {
        var parts = documentId.Split('_');
        if (parts.Length != 4 || parts[0] != "rpt" || parts[2] != "v")
            throw new ArgumentException("Invalid document ID format");
        
        return (Guid.Parse(parts[1]), Guid.Parse(parts[3]));
    }
}
```

### 2. Blob Path Utilities

```csharp
public static class BlobPathHelper
{
    public static string GenerateComponentsPath(string tenantId, Guid reportId, int versionNumber)
        => $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/components.json";
    
    public static string GenerateStylesPath(string tenantId, Guid reportId, int versionNumber)
        => $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/styles.json";
    
    public static bool IsValidTenantPath(string blobPath, string expectedTenantId)
        => blobPath.StartsWith($"tenants/{expectedTenantId}/");
}
```

### 3. Error Handling Utilities

```csharp
public static class StorageErrorHandler
{
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        int maxRetries = 3,
        TimeSpan? delay = null)
    {
        var retryDelay = delay ?? TimeSpan.FromSeconds(1);
        
        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (IsTransientError(ex) && attempt < maxRetries)
            {
                await Task.Delay(retryDelay);
                retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2); // Exponential backoff
            }
        }
        
        throw new InvalidOperationException("Max retries exceeded");
    }
    
    private static bool IsTransientError(Exception ex)
    {
        return ex is CosmosException cosmos && cosmos.StatusCode == HttpStatusCode.TooManyRequests
            || ex is RequestFailedException blob && blob.Status == 429
            || ex is SqlException sql && sql.IsTransient
            || ex is HttpRequestException
            || ex is TaskCanceledException;
    }
}
```

This quick reference guide provides the essential information developers need for efficient day-to-day work with the multi-storage architecture.
