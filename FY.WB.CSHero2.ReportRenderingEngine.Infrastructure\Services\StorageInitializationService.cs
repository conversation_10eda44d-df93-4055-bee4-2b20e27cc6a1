using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Service for initializing and validating storage systems
    /// </summary>
    public class StorageInitializationService
    {
        private readonly CosmosClient _cosmosClient;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly CosmosDbOptions _cosmosOptions;
        private readonly BlobStorageOptions _blobOptions;
        private readonly ILogger<StorageInitializationService> _logger;

        public StorageInitializationService(
            CosmosClient cosmosClient,
            BlobServiceClient blobServiceClient,
            IOptions<CosmosDbOptions> cosmosOptions,
            IOptions<BlobStorageOptions> blobOptions,
            ILogger<StorageInitializationService> logger)
        {
            _cosmosClient = cosmosClient ?? throw new ArgumentNullException(nameof(cosmosClient));
            _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
            _cosmosOptions = cosmosOptions?.Value ?? throw new ArgumentNullException(nameof(cosmosOptions));
            _blobOptions = blobOptions?.Value ?? throw new ArgumentNullException(nameof(blobOptions));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Initializes all storage systems and creates required containers/databases
        /// </summary>
        public async Task InitializeAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting storage initialization...");

            try
            {
                await InitializeCosmosDbAsync(cancellationToken);
                await InitializeBlobStorageAsync(cancellationToken);

                _logger.LogInformation("Storage initialization completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Storage initialization failed");
                throw;
            }
        }

        /// <summary>
        /// Validates that all storage systems are accessible and properly configured
        /// </summary>
        public async Task<StorageHealthStatus> ValidateAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting storage validation...");

            var status = new StorageHealthStatus();

            try
            {
                // Validate CosmosDB
                status.CosmosDbStatus = await ValidateCosmosDbAsync(cancellationToken);

                // Validate Blob Storage
                status.BlobStorageStatus = await ValidateBlobStorageAsync(cancellationToken);

                status.OverallStatus = status.CosmosDbStatus.IsHealthy && status.BlobStorageStatus.IsHealthy
                    ? HealthStatus.Healthy
                    : HealthStatus.Unhealthy;

                _logger.LogInformation("Storage validation completed. Overall status: {Status}", status.OverallStatus);
                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Storage validation failed");
                status.OverallStatus = HealthStatus.Unhealthy;
                status.ErrorMessage = ex.Message;
                return status;
            }
        }

        private async Task InitializeCosmosDbAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Initializing CosmosDB database: {DatabaseName}", _cosmosOptions.DatabaseName);

            // Create database if it doesn't exist
            var databaseResponse = await _cosmosClient.CreateDatabaseIfNotExistsAsync(
                _cosmosOptions.DatabaseName,
                throughput: 400, // Shared throughput for cost optimization
                cancellationToken: cancellationToken);

            var database = databaseResponse.Database;
            _logger.LogInformation("CosmosDB database {DatabaseName} is ready", _cosmosOptions.DatabaseName);

            // Create report data container (for sections and fields as JSON)
            // Note: Report styles (HTML/CSS) are now stored in Azure Blob Storage
            _logger.LogInformation("Creating container: {ContainerName}", _cosmosOptions.Containers.ReportData);

            var containerProperties = new ContainerProperties
            {
                Id = _cosmosOptions.Containers.ReportData,
                PartitionKeyPath = "/TenantId",
                DefaultTimeToLive = -1 // No TTL by default
            };

            // Add indexing policy for better query performance
            containerProperties.IndexingPolicy.IncludedPaths.Clear();
            containerProperties.IndexingPolicy.IncludedPaths.Add(new IncludedPath { Path = "/*" });

            // Optimize indexing for report data (sections and fields)
            containerProperties.IndexingPolicy.ExcludedPaths.Add(new ExcludedPath { Path = "/largeContent/*" });

            await database.CreateContainerIfNotExistsAsync(
                containerProperties,
                throughput: null, // Use shared database throughput
                cancellationToken: cancellationToken);

            _logger.LogInformation("CosmosDB container {ContainerName} is ready", _cosmosOptions.Containers.ReportData);
        }

        private async Task InitializeBlobStorageAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Initializing Blob Storage container: {ContainerName}", _blobOptions.ContainerName);

            var containerClient = _blobServiceClient.GetBlobContainerClient(_blobOptions.ContainerName);
            
            await containerClient.CreateIfNotExistsAsync(
                Azure.Storage.Blobs.Models.PublicAccessType.None,
                cancellationToken: cancellationToken);

            _logger.LogInformation("Blob Storage container {ContainerName} is ready", _blobOptions.ContainerName);
        }

        private async Task<ComponentHealthStatus> ValidateCosmosDbAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogDebug("Validating CosmosDB connection...");

                // Test database access
                var database = _cosmosClient.GetDatabase(_cosmosOptions.DatabaseName);
                await database.ReadAsync(cancellationToken: cancellationToken);

                // Test container access (report data container for sections and fields)
                var container = database.GetContainer(_cosmosOptions.Containers.ReportData);
                await container.ReadContainerAsync(cancellationToken: cancellationToken);

                _logger.LogDebug("CosmosDB validation successful");
                return new ComponentHealthStatus
                {
                    IsHealthy = true,
                    ComponentName = "CosmosDB",
                    Message = "Connection and container access verified"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CosmosDB validation failed");
                return new ComponentHealthStatus
                {
                    IsHealthy = false,
                    ComponentName = "CosmosDB",
                    Message = $"Validation failed: {ex.Message}"
                };
            }
        }

        private async Task<ComponentHealthStatus> ValidateBlobStorageAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogDebug("Validating Blob Storage connection...");

                // Test service access
                await _blobServiceClient.GetPropertiesAsync(cancellationToken: cancellationToken);

                // Test container access
                var containerClient = _blobServiceClient.GetBlobContainerClient(_blobOptions.ContainerName);
                await containerClient.GetPropertiesAsync(cancellationToken: cancellationToken);

                _logger.LogDebug("Blob Storage validation successful");
                return new ComponentHealthStatus
                {
                    IsHealthy = true,
                    ComponentName = "BlobStorage",
                    Message = "Connection and container access verified"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Blob Storage validation failed");
                return new ComponentHealthStatus
                {
                    IsHealthy = false,
                    ComponentName = "BlobStorage",
                    Message = $"Validation failed: {ex.Message}"
                };
            }
        }
    }

    /// <summary>
    /// Overall storage health status
    /// </summary>
    public class StorageHealthStatus
    {
        public HealthStatus OverallStatus { get; set; } = HealthStatus.Unknown;
        public ComponentHealthStatus CosmosDbStatus { get; set; } = new();
        public ComponentHealthStatus BlobStorageStatus { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Individual component health status
    /// </summary>
    public class ComponentHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string ComponentName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Health status enumeration
    /// </summary>
    public enum HealthStatus
    {
        Unknown,
        Healthy,
        Unhealthy
    }
}