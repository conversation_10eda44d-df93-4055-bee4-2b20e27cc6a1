using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Cosmos DB document for storing report data (sections and fields)
    /// </summary>
    public class ReportData
    {
        /// <summary>
        /// Document ID in Cosmos DB
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Partition key for Cosmos DB (tenant ID)
        /// </summary>
        [JsonPropertyName("partitionKey")]
        public string PartitionKey { get; set; } = string.Empty;

        /// <summary>
        /// Report ID from SQL database
        /// </summary>
        [JsonPropertyName("reportId")]
        public string ReportId { get; set; } = string.Empty;

        /// <summary>
        /// Report version ID from SQL database
        /// </summary>
        [JsonPropertyName("versionId")]
        public string VersionId { get; set; } = string.Empty;

        /// <summary>
        /// Version number for tracking
        /// </summary>
        [JsonPropertyName("versionNumber")]
        public int VersionNumber { get; set; }

        /// <summary>
        /// Tenant ID for partitioning
        /// </summary>
        [Json<PERSON>ropertyName("tenantId")]
        public string TenantId { get; set; } = string.Empty;

        /// <summary>
        /// Report sections containing structured data
        /// </summary>
        [JsonPropertyName("sections")]
        public List<ReportSection> Sections { get; set; } = new List<ReportSection>();

        /// <summary>
        /// Document metadata
        /// </summary>
        [JsonPropertyName("metadata")]
        public ReportDataMetadata Metadata { get; set; } = new ReportDataMetadata();

        /// <summary>
        /// When the document was created
        /// </summary>
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the document was last modified
        /// </summary>
        [JsonPropertyName("lastModified")]
        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Creates a document ID for a report data document
        /// </summary>
        public static string CreateDocumentId(Guid reportId, Guid versionId)
        {
            return $"report-data-{reportId}-{versionId}";
        }

        /// <summary>
        /// Creates a document ID for the current version
        /// </summary>
        public static string CreateDocumentId(Guid reportId)
        {
            return $"report-data-{reportId}-current";
        }
    }

    /// <summary>
    /// Report section containing fields and content
    /// </summary>
    public class ReportSection
    {
        /// <summary>
        /// Unique section identifier
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Section name/identifier
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Section type (cover, text, chart, table, list, timeline, etc.)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Display order within the report
        /// </summary>
        [JsonPropertyName("order")]
        public int Order { get; set; }

        /// <summary>
        /// Section data as flexible object
        /// </summary>
        [JsonPropertyName("data")]
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Section-level metadata
        /// </summary>
        [JsonPropertyName("metadata")]
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Metadata for the report data document
    /// </summary>
    public class ReportDataMetadata
    {
        /// <summary>
        /// Report title
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Report description
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Who created the document
        /// </summary>
        [JsonPropertyName("createdBy")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Who last modified the document
        /// </summary>
        [JsonPropertyName("lastModifiedBy")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// Document version for optimistic concurrency
        /// </summary>
        [JsonPropertyName("version")]
        public int Version { get; set; } = 1;

        /// <summary>
        /// Additional metadata
        /// </summary>
        [JsonPropertyName("additionalData")]
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }
}
