<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Save Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; border: none; }
        .btn-secondary { background-color: #6c757d; color: white; border: none; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Report Save Test Tool</h1>
        
        <div class="test-section info">
            <h3>Test Configuration</h3>
            <p><strong>Frontend:</strong> http://localhost:3000</p>
            <p><strong>Backend:</strong> http://localhost:5056</p>
            <p><strong>Current Issues:</strong></p>
            <ul>
                <li>ReportNumber column too short (truncated at 'RPT-20250610-57f8d3e')</li>
                <li>Missing TenantId and IsDeleted columns in ReportSections table</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Quick Report Save Test</h3>
            <button class="btn-primary" onclick="testReportSave()">Test Report Save</button>
            <button class="btn-secondary" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults" class="log"></div>
        </div>

        <div class="test-section">
            <h3>Manual Test Data</h3>
            <p>Copy this fetch command to browser console:</p>
            <pre id="fetchCommand"></pre>
            <button class="btn-secondary" onclick="copyToClipboard()">Copy to Clipboard</button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testResults').innerHTML = '';
        }

        async function testReportSave() {
            log('Starting report save test...', 'info');
            
            const testData = {
                reportName: "Test Report " + Date.now(),
                description: "Automated test report",
                clientId: "A1B2C3D4-E5F6-7890-1234-567890ABCDEF",
                clientName: "Data Analytics Solutions",
                status: "Draft",
                author: "Test User",
                startDate: "2025-06-10",
                endDate: "2025-06-10",
                content: {
                    template: "default",
                    sections: [
                        {
                            id: "section-" + Date.now() + "-1",
                            "section-title": "Test Section 1",
                            type: "content-section",
                            content: { content: "Test content 1" }
                        },
                        {
                            id: "section-" + Date.now() + "-2",
                            "section-title": "Test Section 2", 
                            type: "content-section",
                            content: { content: "Test content 2" }
                        }
                    ]
                },
                style: {}
            };

            try {
                log('Sending request to frontend API...', 'info');
                
                const response = await fetch("http://localhost:3000/api/v1/reports", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Accept": "*/*"
                    },
                    credentials: "include",
                    body: JSON.stringify(testData)
                });

                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                log(`Response body: ${responseText}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    log('✅ Report saved successfully!', 'success');
                } else {
                    log('❌ Report save failed', 'error');
                }

            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        function updateFetchCommand() {
            const testData = {
                reportName: "Manual Test Report",
                description: "Manual test",
                clientId: "A1B2C3D4-E5F6-7890-1234-567890ABCDEF",
                clientName: "Data Analytics Solutions",
                status: "Draft",
                author: "Manual User",
                startDate: "2025-06-10",
                endDate: "2025-06-10",
                content: {
                    template: "default",
                    sections: [
                        {
                            id: "section-manual-1",
                            "section-title": "Manual Section 1",
                            type: "content-section",
                            content: { content: "Manual test content" }
                        }
                    ]
                },
                style: {}
            };

            const fetchCommand = `fetch("http://localhost:3000/api/v1/reports", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Accept": "*/*"
  },
  credentials: "include",
  body: '${JSON.stringify(testData)}'
});`;

            document.getElementById('fetchCommand').textContent = fetchCommand;
        }

        function copyToClipboard() {
            const text = document.getElementById('fetchCommand').textContent;
            navigator.clipboard.writeText(text).then(() => {
                log('✅ Copied to clipboard!', 'success');
            });
        }

        // Initialize
        updateFetchCommand();
        log('Test tool loaded. Ready to test report saving.', 'info');
    </script>
</body>
</html>
