# CosmosDB Data Seeding Fix - Option A Implementation Plan

## Executive Summary

**Problem**: CosmosDB seeding process fails due to partition key mismatch between infrastructure setup (`/id`) and seeding logic (`TenantId`).

**Solution**: Implement Option A - Fix Partition Key Strategy to align with multi-tenant architecture using `/tenantId` partition key.

**Timeline**: 3-5 days
**Priority**: Critical

---

## Root Cause Analysis

### 🔍 **Primary Issue Identified**

**Partition Key Mismatch**:
- **Infrastructure Setup**: Container created with partition key `/id` (lines 134, 156 in `setup-azure-infrastructure.ps1`)
- **Seeding Logic**: Uses `report.TenantId?.ToString() ?? "default"` as partition key (line 64 in `CosmosSeeder.cs`)
- **Result**: Documents cannot be inserted due to partition key incompatibility

### 📊 **Evidence from Code Analysis**

#### Infrastructure Script (`scripts/setup-azure-infrastructure.ps1`)
```powershell
# Lines 134, 156 - PROBLEM
--partition-key-path "/id"
```

#### CosmosSeeder (`FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`)
```csharp
// Line 64 - PROBLEM
await _cosmosDbService.UpsertItemAsync(document, report.TenantId?.ToString() ?? "default");
```

#### Configuration (`FY.WB.CSHero2/appsettings.json`)
```json
"CosmosDb": {
  "ConnectionString": "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;...",
  "DatabaseName": "CSHeroReports",
  "ContainerName": "Reports"
}
```

---

## 🛠️ **Option A: Fix Partition Key Strategy - Detailed Implementation**

### **Phase 1: Infrastructure Updates** (Day 1)

#### 1.1 Update Azure Infrastructure Script
**File**: `scripts/setup-azure-infrastructure.ps1`

**Changes Required**:
```powershell
# BEFORE (Lines 134, 156)
--partition-key-path "/id"

# AFTER
--partition-key-path "/tenantId"
```

**Complete Fix**:
```powershell
# Line 134 - Serverless container creation
az cosmosdb sql container create `
    --account-name $CosmosAccountName `
    --database-name $DatabaseName `
    --resource-group $ResourceGroupName `
    --name $ContainerName `
    --partition-key-path "/tenantId" `
    --idx @$indexingPolicyPath

# Line 156 - Provisioned container creation  
az cosmosdb sql container create `
    --account-name $CosmosAccountName `
    --database-name $DatabaseName `
    --resource-group $ResourceGroupName `
    --name $ContainerName `
    --partition-key-path "/tenantId"
```

#### 1.2 Container Recreation Strategy
**Option 1: Delete and Recreate (Recommended for Development)**
```powershell
# Add to script before container creation
Write-Host "Checking if container exists with wrong partition key..."
$existingContainer = az cosmosdb sql container show --account-name $CosmosAccountName --database-name $DatabaseName --name $ContainerName --resource-group $ResourceGroupName --query "resource.partitionKey.paths[0]" -o tsv 2>$null

if ($existingContainer -eq "/id") {
    Write-Host "Deleting container with incorrect partition key..." -ForegroundColor Yellow
    az cosmosdb sql container delete --account-name $CosmosAccountName --database-name $DatabaseName --name $ContainerName --resource-group $ResourceGroupName --yes
    Write-Host "Container deleted. Will recreate with correct partition key." -ForegroundColor Green
}
```

### **Phase 2: Document Structure Updates** (Day 2)

#### 2.1 Update ReportDataDocument Model
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Current Document Structure** (Lines 204-219):
```csharp
public class ReportDataDocument
{
    public string Id { get; set; } = string.Empty;
    public Guid ReportId { get; set; }
    public Guid VersionId { get; set; }
    public Guid TenantId { get; set; }
    // ... other properties
}
```

**Enhanced Document Structure**:
```csharp
public class ReportDataDocument
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("tenantId")]
    public string TenantId { get; set; } = string.Empty; // Changed to string for partition key
    
    [JsonPropertyName("reportId")]
    public Guid ReportId { get; set; }
    
    [JsonPropertyName("versionId")]
    public Guid VersionId { get; set; }
    
    [JsonPropertyName("reportName")]
    public string ReportName { get; set; } = string.Empty;
    
    [JsonPropertyName("reportNumber")]
    public string ReportNumber { get; set; } = string.Empty;
    
    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty;
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;
    
    [JsonPropertyName("author")]
    public string Author { get; set; } = string.Empty;
    
    [JsonPropertyName("clientId")]
    public Guid ClientId { get; set; }
    
    [JsonPropertyName("clientName")]
    public string ClientName { get; set; } = string.Empty;
    
    [JsonPropertyName("sections")]
    public List<ReportDataSection> Sections { get; set; } = new();
    
    [JsonPropertyName("metadata")]
    public ReportDataMetadata Metadata { get; set; } = new();
}
```

#### 2.2 Update Document Creation Logic
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Current CreateReportDocumentAsync** (Lines 123-183):
```csharp
// BEFORE
return new ReportDataDocument
{
    Id = GenerateDocumentId(report.Id),
    ReportId = report.Id,
    VersionId = versionId,
    TenantId = report.TenantId ?? Guid.Empty, // Problem: Guid type
    // ... rest
};
```

**Fixed CreateReportDocumentAsync**:
```csharp
private async Task<ReportDataDocument> CreateReportDocumentAsync(ApplicationDbContext context, Report report, CancellationToken cancellationToken)
{
    // Get report versions for this report
    var reportVersions = await context.ReportVersions
        .IgnoreQueryFilters()
        .Where(rv => rv.ReportId == report.Id)
        .ToListAsync(cancellationToken);

    // Get the latest version or create a default one
    var latestVersion = reportVersions.OrderByDescending(rv => rv.VersionNumber).FirstOrDefault();
    var versionId = latestVersion?.Id ?? Guid.NewGuid();

    // Ensure TenantId is not null/empty
    var tenantId = report.TenantId?.ToString() ?? "default";
    if (string.IsNullOrEmpty(tenantId) || tenantId == "00000000-0000-0000-0000-000000000000")
    {
        tenantId = "default";
        _logger.LogWarning("Report {ReportId} has null/empty TenantId, using 'default'", report.Id);
    }

    // Transform sections and fields into document format
    var sections = report.Sections?.Select(section => new ReportDataSection
    {
        Id = section.Id,
        Name = section.Title,
        Title = section.Title,
        Description = string.Empty,
        SectionType = section.Type,
        DisplayOrder = section.Order,
        IsRequired = false,
        Fields = section.Fields?.Select(field => new ReportDataField
        {
            Id = field.Id,
            Name = field.Name,
            Label = field.Name,
            FieldType = field.Type,
            DefaultValue = field.Content ?? string.Empty,
            IsRequired = false,
            DisplayOrder = field.Order,
            ValidationRules = new Dictionary<string, object>(),
            Options = new List<string>()
        }).ToList() ?? new List<ReportDataField>()
    }).ToList() ?? new List<ReportDataSection>();

    return new ReportDataDocument
    {
        Id = GenerateDocumentId(report.Id),
        TenantId = tenantId, // Fixed: Use string tenantId as partition key
        ReportId = report.Id,
        VersionId = versionId,
        ReportName = report.Name,
        ReportNumber = report.ReportNumber,
        Category = report.Category,
        Status = report.Status,
        Author = report.Author,
        ClientId = report.ClientId,
        ClientName = report.ClientName,
        Sections = sections,
        Metadata = new ReportDataMetadata
        {
            CreatedAt = report.CreationTime,
            UpdatedAt = report.LastModificationTime ?? report.CreationTime,
            SectionCount = sections.Count,
            FieldCount = sections.Sum(s => s.Fields.Count),
            Version = latestVersion?.VersionNumber.ToString() ?? "1",
            Tags = new List<string> { report.Category, report.Status }
        }
    };
}
```

### **Phase 3: Seeding Logic Updates** (Day 2-3)

#### 3.1 Fix UpsertItemAsync Call
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Current SeedAsync Method** (Line 64):
```csharp
// BEFORE - PROBLEM
await _cosmosDbService.UpsertItemAsync(document, report.TenantId?.ToString() ?? "default");
```

**Fixed SeedAsync Method**:
```csharp
public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
{
    _logger.LogInformation("Starting Cosmos DB seeding...");

    try
    {
        // Get existing document IDs to avoid duplicates
        var existingDocumentIds = await GetExistingDocumentIdsAsync(cancellationToken);
        _logger.LogInformation("Found {ExistingCount} existing documents in Cosmos DB", existingDocumentIds.Count);

        // Get all reports with their sections and fields from SQL
        var reports = await GetReportsWithDataAsync(context, cancellationToken);
        _logger.LogInformation("Found {ReportCount} reports to process for Cosmos DB seeding", reports.Count);

        var createdCount = 0;
        var skippedCount = 0;
        var errorCount = 0;

        foreach (var report in reports)
        {
            try
            {
                var documentId = GenerateDocumentId(report.Id);
                
                if (existingDocumentIds.Contains(documentId))
                {
                    _logger.LogDebug("Skipping report {ReportId} - document already exists", report.Id);
                    skippedCount++;
                    continue;
                }

                // Create document from report data
                var document = await CreateReportDocumentAsync(context, report, cancellationToken);
                
                // Validate partition key
                if (string.IsNullOrEmpty(document.TenantId))
                {
                    _logger.LogError("Cannot seed report {ReportId} - TenantId is null or empty", report.Id);
                    errorCount++;
                    continue;
                }
                
                // Save to Cosmos DB using TenantId as partition key
                await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
                
                // Update SQL entity with document ID
                await UpdateReportWithDocumentIdAsync(context, report.Id, documentId, cancellationToken);
                
                createdCount++;
                _logger.LogDebug("Created Cosmos document for report {ReportId} with partition key {PartitionKey}", 
                    report.Id, document.TenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding report {ReportId} to Cosmos DB", report.Id);
                errorCount++;
                // Continue with other reports instead of failing completely
            }
        }

        _logger.LogInformation("Cosmos DB seeding completed: {Created} created, {Skipped} skipped, {Errors} errors", 
            createdCount, skippedCount, errorCount);
            
        if (errorCount > 0)
        {
            _logger.LogWarning("Cosmos DB seeding completed with {ErrorCount} errors. Check logs for details.", errorCount);
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Critical error during Cosmos DB seeding");
        throw;
    }
}
```

#### 3.2 Update Query Logic for Partition Key
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Current GetExistingDocumentIdsAsync** (Lines 83-98):
```csharp
// BEFORE
var query = "SELECT c.id FROM c WHERE STARTSWITH(c.id, 'report-data-')";
```

**Fixed GetExistingDocumentIdsAsync**:
```csharp
public async Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default)
{
    try
    {
        // Query all documents across all partitions to get their IDs
        var query = "SELECT c.id, c.tenantId FROM c WHERE STARTSWITH(c.id, 'report-data-')";
        var documents = await _cosmosDbService.GetItemsAsync<DocumentIdResult>(query);
        
        var documentIds = documents.Select(d => d.Id).ToList();
        _logger.LogDebug("Found {Count} existing documents across all partitions", documentIds.Count);
        
        return documentIds;
    }
    catch (Exception ex)
    {
        _logger.LogWarning(ex, "Error getting existing document IDs from Cosmos DB, assuming empty");
        return new List<string>();
    }
}

// Updated helper class
public class DocumentIdResult
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("tenantId")]
    public string TenantId { get; set; } = string.Empty;
}
```

### **Phase 4: Enhanced Error Handling & Validation** (Day 3-4)

#### 4.1 Add Comprehensive Error Handling
```csharp
private async Task<bool> ValidateCosmosDbConnection()
{
    try
    {
        // Test connection by querying container metadata
        var query = "SELECT VALUE COUNT(1) FROM c";
        await _cosmosDbService.GetItemsAsync<int>(query);
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Cosmos DB connection validation failed");
        return false;
    }
}

private async Task<bool> ValidatePartitionKeyConfiguration()
{
    try
    {
        // This would require additional CosmosDbService methods to check container metadata
        // For now, we'll validate by attempting a test operation
        var testDocument = new { id = "test-validation", tenantId = "test" };
        // Implementation depends on available CosmosDbService methods
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Partition key validation failed: {Error}", ex.Message);
        return false;
    }
}
```

#### 4.2 Add Tenant Data Validation
```csharp
private async Task<List<Report>> ValidateReportsForSeeding(ApplicationDbContext context, CancellationToken cancellationToken)
{
    var reports = await GetReportsWithDataAsync(context, cancellationToken);
    var validReports = new List<Report>();
    var invalidCount = 0;

    foreach (var report in reports)
    {
        // Validate required fields
        if (report.TenantId == null || report.TenantId == Guid.Empty)
        {
            _logger.LogWarning("Report {ReportId} has invalid TenantId, will use 'default'", report.Id);
        }

        if (string.IsNullOrEmpty(report.Name))
        {
            _logger.LogError("Report {ReportId} has empty name, skipping", report.Id);
            invalidCount++;
            continue;
        }

        validReports.Add(report);
    }

    if (invalidCount > 0)
    {
        _logger.LogWarning("Skipped {InvalidCount} reports due to validation failures", invalidCount);
    }

    return validReports;
}
```

### **Phase 5: Testing & Validation** (Day 4-5)

#### 5.1 Integration Test Strategy
```csharp
// Test file: CosmosDbSeedingIntegrationTests.cs
[Test]
public async Task SeedAsync_WithValidData_ShouldCreateDocumentsWithCorrectPartitionKey()
{
    // Arrange
    var reports = CreateTestReports();
    
    // Act
    await _cosmosSeeder.SeedAsync(_context, CancellationToken.None);
    
    // Assert
    var existingDocs = await _cosmosSeeder.GetExistingDocumentIdsAsync();
    Assert.That(existingDocs.Count, Is.EqualTo(reports.Count));
    
    // Verify partition key usage
    foreach (var report in reports)
    {
        var document = await _cosmosDbService.GetItemAsync<ReportDataDocument>(
            GenerateDocumentId(report.Id), 
            report.TenantId.ToString());
        Assert.That(document, Is.Not.Null);
        Assert.That(document.TenantId, Is.EqualTo(report.TenantId.ToString()));
    }
}
```

#### 5.2 Validation Checklist
- [ ] Container created with `/tenantId` partition key
- [ ] Documents inserted successfully with correct partition key
- [ ] Cross-storage references maintained (SQL → Cosmos)
- [ ] Multi-tenant isolation verified
- [ ] Error handling tested with invalid data
- [ ] Performance acceptable with full dataset

---

## 🚀 **Deployment Strategy**

### **Development Environment**
1. **Day 1**: Update infrastructure script and recreate container
2. **Day 2**: Deploy seeding logic updates
3. **Day 3**: Run comprehensive testing
4. **Day 4**: Performance validation and optimization
5. **Day 5**: Documentation and handover

### **Production Environment**
1. **Backup existing data** (if any)
2. **Schedule maintenance window**
3. **Deploy infrastructure changes**
4. **Deploy application updates**
5. **Validate seeding process**
6. **Monitor for issues**

---

## 📊 **Success Metrics**

| Metric | Target | Validation Method |
|--------|--------|-------------------|
| Document Insertion Success Rate | 100% | Count documents vs reports |
| Partition Key Consistency | 100% | Validate all documents use tenantId |
| Cross-Storage Reference Integrity | 100% | Verify SQL.DataDocumentId → Cosmos.Id |
| Multi-Tenant Isolation | 100% | Query documents by partition |
| Seeding Performance | < 30 seconds | Time full seeding process |
| Error Rate | 0% | Monitor logs for exceptions |

---

## 🔧 **Rollback Strategy**

### **If Issues Occur**
1. **Immediate**: Revert to previous infrastructure script
2. **Short-term**: Recreate container with `/id` partition key
3. **Application**: Deploy previous seeding logic version
4. **Data**: Restore from backup if necessary

### **Rollback Commands**
```powershell
# Recreate container with old partition key
az cosmosdb sql container delete --account-name $CosmosAccountName --database-name $DatabaseName --name $ContainerName --resource-group $ResourceGroupName --yes

az cosmosdb sql container create `
    --account-name $CosmosAccountName `
    --database-name $DatabaseName `
    --resource-group $ResourceGroupName `
    --name $ContainerName `
    --partition-key-path "/id"
```

---

## 📝 **Implementation Checklist**

### **Infrastructure Updates**
- [ ] Update `setup-azure-infrastructure.ps1` partition key to `/tenantId`
- [ ] Add container validation and recreation logic
- [ ] Test infrastructure script in development environment
- [ ] Verify container created with correct partition key

### **Code Updates**
- [ ] Update `ReportDataDocument` model with proper JSON attributes
- [ ] Fix `CreateReportDocumentAsync` to use string TenantId
- [ ] Update `SeedAsync` method with enhanced error handling
- [ ] Fix `GetExistingDocumentIdsAsync` for cross-partition queries
- [ ] Add validation methods for connection and partition key

### **Testing**
- [ ] Unit tests for document creation logic
- [ ] Integration tests for full seeding process
- [ ] Performance tests with full dataset
- [ ] Multi-tenant isolation tests
- [ ] Error handling tests with invalid data

### **Documentation**
- [ ] Update seeding architecture documentation
- [ ] Create deployment runbook
- [ ] Document rollback procedures
- [ ] Update troubleshooting guide

---

## 🎯 **Next Steps**

1. **Approve this implementation plan**
2. **Schedule development work (3-5 days)**
3. **Coordinate with infrastructure team for container recreation**
4. **Plan testing strategy with QA team**
5. **Schedule deployment window for production**

This comprehensive fix addresses the root cause of the CosmosDB seeding failure and establishes a robust, multi-tenant-aware document storage strategy that aligns with the overall application architecture.