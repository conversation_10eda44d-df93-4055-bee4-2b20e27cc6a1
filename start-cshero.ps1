# CS-Hero Application Startup Script
# This script kills any existing backend and frontend processes and starts them

Write-Host "CS-Hero Application Startup Script" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Function to check if a process is running
function Test-ProcessRunning {
    param (
        [string]$ProcessName
    )
    
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue)
}

# Function to kill process by port
function Kill-ProcessByPort {
    param (
        [int]$Port
    )
    
    $processInfo = netstat -ano | findstr :$Port
    if ($processInfo) {
        $processId = ($processInfo -split ' ')[-1]
        if ($processId -match '^\d+$') {
            Write-Host "  Killing process with ID: $processId using port $Port" -ForegroundColor Gray
            Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
            return $true
        }
    }
    return $false
}

# Kill existing processes
Write-Host "Stopping existing processes..." -ForegroundColor Yellow

# Kill dotnet processes that might be running the backend
$dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*FY.WB.CSHero2*" }
if ($dotnetProcesses) {
    Write-Host "Stopping backend processes..." -ForegroundColor Yellow
    $dotnetProcesses | ForEach-Object {
        Write-Host "  Stopping process with ID: $($_.Id)" -ForegroundColor Gray
        Stop-Process -Id $_.Id -Force
    }
} else {
    Write-Host "No backend processes found running." -ForegroundColor Gray
}

# Kill processes using the backend ports
Write-Host "Checking for processes using backend ports..." -ForegroundColor Yellow
$killedHttps = Kill-ProcessByPort -Port 7104
$killedHttp = Kill-ProcessByPort -Port 5104
if (-not ($killedHttps -or $killedHttp)) {
    Write-Host "  No processes found using backend ports." -ForegroundColor Gray
}

# Kill node processes that might be running the frontend
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*next*" }
if ($nodeProcesses) {
    Write-Host "Stopping frontend processes..." -ForegroundColor Yellow
    $nodeProcesses | ForEach-Object {
        Write-Host "  Stopping process with ID: $($_.Id)" -ForegroundColor Gray
        Stop-Process -Id $_.Id -Force
    }
} else {
    Write-Host "No frontend processes found running." -ForegroundColor Gray
}

# Kill processes using the frontend port
Write-Host "Checking for processes using frontend port..." -ForegroundColor Yellow
$killedFrontend = Kill-ProcessByPort -Port 3000
if (-not $killedFrontend) {
    Write-Host "  No processes found using frontend port." -ForegroundColor Gray
}

# Wait a moment to ensure processes are fully terminated
Start-Sleep -Seconds 2

# Start the backend directly (avoiding PowerShell script execution issues)
Write-Host "Starting backend..." -ForegroundColor Green
$backendPath = Join-Path $PSScriptRoot "FY.WB.CSHero2"

# Start the backend in a new window using cmd.exe to avoid execution policy issues
$backendCmd = "cd /d $backendPath && set DOTNET_SKIP_MIGRATIONS=true && dotnet run"
Start-Process -FilePath "cmd.exe" -ArgumentList "/k", $backendCmd -WindowStyle Normal

# Wait for backend to initialize
Write-Host "Waiting for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start the frontend using cmd.exe to avoid execution policy issues
Write-Host "Starting frontend..." -ForegroundColor Green
$frontendPath = Join-Path $PSScriptRoot "FY.WB.CSHero2.UI"
$frontendCmd = "cd /d $frontendPath && npm run dev"
Start-Process -FilePath "cmd.exe" -ArgumentList "/k", $frontendCmd -WindowStyle Normal

Write-Host "CS-Hero application started successfully!" -ForegroundColor Cyan
Write-Host "Backend running at: http://localhost:5000" -ForegroundColor Green
Write-Host "Frontend running at: http://localhost:3000" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop all processes" -ForegroundColor Yellow