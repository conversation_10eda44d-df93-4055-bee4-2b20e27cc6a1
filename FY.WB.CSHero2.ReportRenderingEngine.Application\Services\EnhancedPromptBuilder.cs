using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Enhanced prompt builder for React component generation with LLM integration
    /// </summary>
    public class EnhancedPromptBuilder
    {
        private readonly ILogger<EnhancedPromptBuilder> _logger;

        public EnhancedPromptBuilder(ILogger<EnhancedPromptBuilder> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Builds a comprehensive prompt for React component generation
        /// </summary>
        public async Task<string> BuildComponentGenerationPromptAsync(
            ComponentGenerationContext context,
            Dictionary<string, object> data,
            Template? template = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Building component generation prompt for section {SectionId}", context.SectionId);

                var promptBuilder = new StringBuilder();

                // Add component requirements
                promptBuilder.AppendLine(BuildComponentRequirements(context));
                promptBuilder.AppendLine();

                // Add data structure information
                if (data.Any())
                {
                    promptBuilder.AppendLine(BuildDataStructureSection(data));
                    promptBuilder.AppendLine();
                }

                // Add template-specific guidance
                if (template != null)
                {
                    promptBuilder.AppendLine(BuildTemplateGuidance(template, context));
                    promptBuilder.AppendLine();
                }

                // Add framework-specific instructions
                promptBuilder.AppendLine(BuildFrameworkInstructions(context));
                promptBuilder.AppendLine();

                // Add styling instructions
                promptBuilder.AppendLine(BuildStylingInstructions(context));
                promptBuilder.AppendLine();

                // Add accessibility requirements
                if (context.IncludeAccessibility)
                {
                    promptBuilder.AppendLine(BuildAccessibilityRequirements());
                    promptBuilder.AppendLine();
                }

                // Add performance optimization requirements
                if (context.OptimizeForPerformance)
                {
                    promptBuilder.AppendLine(BuildPerformanceRequirements());
                    promptBuilder.AppendLine();
                }

                // Add responsive design requirements
                if (context.IncludeResponsiveDesign)
                {
                    promptBuilder.AppendLine(BuildResponsiveDesignRequirements());
                    promptBuilder.AppendLine();
                }

                // Add validation and testing guidance
                promptBuilder.AppendLine(BuildValidationGuidance(context));

                var result = promptBuilder.ToString();
                _logger.LogDebug("Generated prompt with {Length} characters for section {SectionId}", 
                    result.Length, context.SectionId);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building component generation prompt for section {SectionId}", context.SectionId);
                throw;
            }
        }

        /// <summary>
        /// Builds a prompt for component validation
        /// </summary>
        public async Task<string> BuildValidationPromptAsync(
            string componentCode,
            ComponentGenerationContext context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Building validation prompt for component");

                var promptBuilder = new StringBuilder();

                promptBuilder.AppendLine("Please perform a comprehensive validation of this React component:");
                promptBuilder.AppendLine();
                promptBuilder.AppendLine("**Validation Criteria:**");
                promptBuilder.AppendLine("1. **Syntax & Type Safety**: Check for TypeScript errors, proper type definitions");
                promptBuilder.AppendLine("2. **React Best Practices**: Hooks usage, component patterns, state management");
                promptBuilder.AppendLine("3. **Performance**: Re-render optimization, memory leaks, expensive operations");
                promptBuilder.AppendLine("4. **Accessibility**: ARIA attributes, semantic HTML, keyboard navigation");
                promptBuilder.AppendLine("5. **Security**: XSS prevention, input validation, secure coding practices");
                promptBuilder.AppendLine("6. **Maintainability**: Code organization, readability, documentation");
                promptBuilder.AppendLine($"7. **{context.Framework} Conventions**: Framework-specific best practices");
                promptBuilder.AppendLine();

                promptBuilder.AppendLine("**Component Context:**");
                promptBuilder.AppendLine($"- Framework: {context.Framework}");
                promptBuilder.AppendLine($"- Style Framework: {context.StyleFramework}");
                promptBuilder.AppendLine($"- TypeScript: {context.UseTypeScript}");
                promptBuilder.AppendLine($"- Component Type: {context.ComponentType}");
                promptBuilder.AppendLine($"- Section: {context.SectionName} ({context.SectionId})");
                promptBuilder.AppendLine();

                promptBuilder.AppendLine("**Expected Output Format:**");
                promptBuilder.AppendLine("Provide a detailed JSON response with validation results, scores, errors, warnings, and suggestions.");

                var result = promptBuilder.ToString();
                _logger.LogDebug("Generated validation prompt with {Length} characters", result.Length);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building validation prompt");
                throw;
            }
        }

        /// <summary>
        /// Builds a prompt for component optimization
        /// </summary>
        public async Task<string> BuildOptimizationPromptAsync(
            string componentCode,
            string optimizationType,
            ComponentGenerationContext context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Building optimization prompt for {OptimizationType}", optimizationType);

                var promptBuilder = new StringBuilder();

                promptBuilder.AppendLine($"Please optimize this React component for **{optimizationType}**:");
                promptBuilder.AppendLine();

                // Add optimization-specific guidance
                promptBuilder.AppendLine(GetOptimizationGuidance(optimizationType));
                promptBuilder.AppendLine();

                promptBuilder.AppendLine("**Optimization Context:**");
                promptBuilder.AppendLine($"- Framework: {context.Framework}");
                promptBuilder.AppendLine($"- Style Framework: {context.StyleFramework}");
                promptBuilder.AppendLine($"- Component Type: {context.ComponentType}");
                promptBuilder.AppendLine($"- Target: {context.BrowserTarget} browsers");
                promptBuilder.AppendLine($"- Bundle Optimization: {context.BundleOptimization}");
                promptBuilder.AppendLine();

                promptBuilder.AppendLine("**Requirements:**");
                promptBuilder.AppendLine("- Maintain the original functionality and API");
                promptBuilder.AppendLine("- Add comments explaining optimizations");
                promptBuilder.AppendLine("- Ensure the code remains readable and maintainable");
                promptBuilder.AppendLine("- Follow React and modern JavaScript best practices");
                promptBuilder.AppendLine("- Return only the optimized component code");

                var result = promptBuilder.ToString();
                _logger.LogDebug("Generated optimization prompt with {Length} characters", result.Length);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building optimization prompt for {OptimizationType}", optimizationType);
                throw;
            }
        }

        #region Private Helper Methods

        private string BuildComponentRequirements(ComponentGenerationContext context)
        {
            var requirements = new StringBuilder();
            requirements.AppendLine("**Component Requirements:**");
            requirements.AppendLine($"- Component Name: {ToPascalCase(context.SectionName)}");
            requirements.AppendLine($"- Section ID: {context.SectionId}");
            requirements.AppendLine($"- Component Type: {context.ComponentType}");
            requirements.AppendLine($"- Framework: {context.Framework}");
            requirements.AppendLine($"- Language: {(context.UseTypeScript ? "TypeScript" : "JavaScript")}");
            requirements.AppendLine($"- Styling: {context.StyleFramework}");
            requirements.AppendLine($"- Theme: {context.Theme}");

            if (context.RequiredDependencies.Any())
            {
                requirements.AppendLine($"- Required Dependencies: {string.Join(", ", context.RequiredDependencies)}");
            }

            return requirements.ToString();
        }

        private string BuildDataStructureSection(Dictionary<string, object> data)
        {
            var dataSection = new StringBuilder();
            dataSection.AppendLine("**Data Structure:**");
            
            try
            {
                var jsonOptions = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var dataJson = JsonSerializer.Serialize(data, jsonOptions);
                dataSection.AppendLine("```json");
                dataSection.AppendLine(dataJson);
                dataSection.AppendLine("```");
            }
            catch (Exception)
            {
                dataSection.AppendLine("Complex data structure provided - analyze and create appropriate props interface.");
            }

            return dataSection.ToString();
        }

        private string BuildTemplateGuidance(Template template, ComponentGenerationContext context)
        {
            var guidance = new StringBuilder();
            guidance.AppendLine("**Template Guidance:**");
            guidance.AppendLine($"- Template: {template.Name}");
            guidance.AppendLine($"- Category: {template.Category}");
            guidance.AppendLine($"- Description: {template.Description}");
            
            if (!string.IsNullOrEmpty(template.DefaultStyleJson))
            {
                guidance.AppendLine("- Apply template's default styling patterns");
            }

            return guidance.ToString();
        }

        private string BuildFrameworkInstructions(ComponentGenerationContext context)
        {
            var instructions = new StringBuilder();
            instructions.AppendLine($"**{context.Framework} Instructions:**");

            switch (context.Framework.ToLower())
            {
                case "nextjs":
                    instructions.AppendLine("- Use Next.js conventions and patterns");
                    instructions.AppendLine("- Consider server-side rendering implications");
                    instructions.AppendLine("- Use Next.js optimized components when appropriate");
                    instructions.AppendLine("- Follow Next.js file and folder conventions");
                    break;
                case "react":
                    instructions.AppendLine("- Use modern React patterns (hooks, functional components)");
                    instructions.AppendLine("- Follow React best practices");
                    instructions.AppendLine("- Use React.memo for performance optimization");
                    break;
                default:
                    instructions.AppendLine("- Follow framework-specific best practices");
                    break;
            }

            if (context.UseTypeScript)
            {
                instructions.AppendLine("- Include proper TypeScript type definitions");
                instructions.AppendLine("- Use interfaces for props and state");
                instructions.AppendLine("- Ensure type safety throughout the component");
            }

            return instructions.ToString();
        }

        private string BuildStylingInstructions(ComponentGenerationContext context)
        {
            var instructions = new StringBuilder();
            instructions.AppendLine($"**{context.StyleFramework} Styling Instructions:**");

            switch (context.StyleFramework.ToLower())
            {
                case "tailwindcss":
                    instructions.AppendLine("- Use Tailwind CSS utility classes");
                    instructions.AppendLine("- Follow Tailwind CSS best practices");
                    instructions.AppendLine("- Use responsive design utilities");
                    instructions.AppendLine("- Apply consistent spacing and typography");
                    break;
                case "styled-components":
                    instructions.AppendLine("- Use styled-components for styling");
                    instructions.AppendLine("- Create reusable styled components");
                    instructions.AppendLine("- Use theme-based styling");
                    break;
                case "css-modules":
                    instructions.AppendLine("- Use CSS Modules for styling");
                    instructions.AppendLine("- Create modular, scoped styles");
                    break;
                default:
                    instructions.AppendLine("- Use appropriate styling methodology");
                    break;
            }

            if (context.CustomStyles.Any())
            {
                instructions.AppendLine("- Apply custom styles as specified");
                foreach (var style in context.CustomStyles)
                {
                    instructions.AppendLine($"  - {style.Key}: {style.Value}");
                }
            }

            return instructions.ToString();
        }

        private string BuildAccessibilityRequirements()
        {
            return @"**Accessibility Requirements:**
- Include appropriate ARIA attributes
- Use semantic HTML elements
- Ensure keyboard navigation support
- Provide screen reader support
- Include focus management
- Use proper heading hierarchy
- Ensure sufficient color contrast
- Add alt text for images";
        }

        private string BuildPerformanceRequirements()
        {
            return @"**Performance Requirements:**
- Use React.memo to prevent unnecessary re-renders
- Implement useCallback for event handlers
- Use useMemo for expensive calculations
- Avoid inline object/function creation in render
- Implement lazy loading where appropriate
- Optimize bundle size
- Use efficient data structures";
        }

        private string BuildResponsiveDesignRequirements()
        {
            return @"**Responsive Design Requirements:**
- Design for mobile-first approach
- Use responsive breakpoints
- Ensure touch-friendly interactions
- Optimize for different screen sizes
- Use flexible layouts (flexbox/grid)
- Consider device orientation changes
- Test across different viewports";
        }

        private string BuildValidationGuidance(ComponentGenerationContext context)
        {
            var guidance = new StringBuilder();
            guidance.AppendLine("**Validation & Testing Guidance:**");
            guidance.AppendLine("- Include prop validation");
            guidance.AppendLine("- Add error boundaries where appropriate");
            guidance.AppendLine("- Handle loading and error states");
            guidance.AppendLine("- Include default props");
            guidance.AppendLine("- Add JSDoc comments for documentation");
            
            if (context.IncludeDocumentation)
            {
                guidance.AppendLine("- Include comprehensive component documentation");
                guidance.AppendLine("- Document props, usage examples, and edge cases");
            }

            return guidance.ToString();
        }

        private string GetOptimizationGuidance(string optimizationType)
        {
            return optimizationType.ToLower() switch
            {
                "performance" => @"**Performance Optimization Focus:**
- Reduce component re-renders using React.memo
- Optimize state management and context usage
- Implement useCallback and useMemo appropriately
- Avoid expensive operations in render
- Use lazy loading for heavy components
- Optimize event handlers and side effects",

                "accessibility" => @"**Accessibility Optimization Focus:**
- Add comprehensive ARIA attributes
- Ensure keyboard navigation works perfectly
- Improve screen reader support
- Use semantic HTML elements
- Implement focus management
- Add proper labels and descriptions
- Ensure color contrast compliance",

                "bundle-size" => @"**Bundle Size Optimization Focus:**
- Remove unused imports and dependencies
- Use dynamic imports for code splitting
- Optimize third-party library usage
- Remove dead code and unused variables
- Use tree shaking effectively
- Minimize component footprint",

                "maintainability" => @"**Maintainability Optimization Focus:**
- Improve code organization and structure
- Add comprehensive documentation
- Enhance type safety and interfaces
- Create reusable component patterns
- Simplify complex logic
- Improve error handling",

                "security" => @"**Security Optimization Focus:**
- Implement input validation and sanitization
- Prevent XSS vulnerabilities
- Secure data handling and storage
- Validate props and external data
- Use secure coding practices
- Implement proper error handling",

                _ => @"**General Optimization Focus:**
- Improve overall code quality
- Follow best practices and conventions
- Enhance performance and maintainability
- Ensure accessibility compliance
- Optimize for production use"
            };
        }

        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return "Component";

            var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    result.Append(char.ToUpper(word[0]));
                    if (word.Length > 1)
                    {
                        result.Append(word.Substring(1).ToLower());
                    }
                }
            }

            return result.Length > 0 ? result.ToString() : "Component";
        }

        #endregion
    }
}
