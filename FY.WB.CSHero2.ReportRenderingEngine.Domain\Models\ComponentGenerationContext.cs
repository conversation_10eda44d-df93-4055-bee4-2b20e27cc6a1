using System;
using System.Collections.Generic;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Context information for React component generation
    /// </summary>
    public class ComponentGenerationContext
    {
        /// <summary>
        /// The target framework for component generation
        /// </summary>
        public string Framework { get; set; } = "NextJS";

        /// <summary>
        /// The styling framework to use
        /// </summary>
        public string StyleFramework { get; set; } = "TailwindCSS";

        /// <summary>
        /// Whether to generate TypeScript code
        /// </summary>
        public bool UseTypeScript { get; set; } = true;

        /// <summary>
        /// Whether to include accessibility features
        /// </summary>
        public bool IncludeAccessibility { get; set; } = true;

        /// <summary>
        /// Whether to include responsive design
        /// </summary>
        public bool IncludeResponsiveDesign { get; set; } = true;

        /// <summary>
        /// Whether to optimize for performance
        /// </summary>
        public bool OptimizeForPerformance { get; set; } = true;

        /// <summary>
        /// The theme to apply to the component
        /// </summary>
        public string Theme { get; set; } = "Default";

        /// <summary>
        /// Data structure for the component
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Section ID for the component
        /// </summary>
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// Section name for the component
        /// </summary>
        public string SectionName { get; set; } = string.Empty;

        /// <summary>
        /// Component type (chart, table, text, etc.)
        /// </summary>
        public string ComponentType { get; set; } = "custom";

        /// <summary>
        /// Additional metadata for component generation
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Required dependencies for the component
        /// </summary>
        public List<string> RequiredDependencies { get; set; } = new List<string>();

        /// <summary>
        /// Custom styling options
        /// </summary>
        public Dictionary<string, string> CustomStyles { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Validation rules for the component
        /// </summary>
        public Dictionary<string, object> ValidationRules { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Whether to include default props
        /// </summary>
        public bool IncludeDefaultProps { get; set; } = true;

        /// <summary>
        /// Whether to include prop types validation
        /// </summary>
        public bool IncludePropTypes { get; set; } = true;

        /// <summary>
        /// Whether to include JSDoc comments
        /// </summary>
        public bool IncludeDocumentation { get; set; } = true;

        /// <summary>
        /// Target browser compatibility
        /// </summary>
        public string BrowserTarget { get; set; } = "modern";

        /// <summary>
        /// Bundle size optimization level
        /// </summary>
        public string BundleOptimization { get; set; } = "standard";

        /// <summary>
        /// Creates a copy of the context with modified properties
        /// </summary>
        public ComponentGenerationContext Clone()
        {
            return new ComponentGenerationContext
            {
                Framework = Framework,
                StyleFramework = StyleFramework,
                UseTypeScript = UseTypeScript,
                IncludeAccessibility = IncludeAccessibility,
                IncludeResponsiveDesign = IncludeResponsiveDesign,
                OptimizeForPerformance = OptimizeForPerformance,
                Theme = Theme,
                Data = new Dictionary<string, object>(Data),
                SectionId = SectionId,
                SectionName = SectionName,
                ComponentType = ComponentType,
                Metadata = new Dictionary<string, object>(Metadata),
                RequiredDependencies = new List<string>(RequiredDependencies),
                CustomStyles = new Dictionary<string, string>(CustomStyles),
                ValidationRules = new Dictionary<string, object>(ValidationRules),
                IncludeDefaultProps = IncludeDefaultProps,
                IncludePropTypes = IncludePropTypes,
                IncludeDocumentation = IncludeDocumentation,
                BrowserTarget = BrowserTarget,
                BundleOptimization = BundleOptimization
            };
        }
    }
}
