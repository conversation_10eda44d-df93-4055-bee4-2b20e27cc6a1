# Storage Implementation Guides

This directory contains comprehensive guides for implementing and maintaining the multi-storage architecture in the CSHero2 reporting system. The guides are designed to provide both high-level understanding and practical implementation details for developers working with SQL Server, Cosmos DB, and Blob Storage coordination.

## Guide Overview

### [01 - Storage Architecture Overview](./01-storage-architecture-overview.md)
**Purpose**: High-level design patterns and principles for the multi-storage architecture  
**Target Audience**: Developers implementing or maintaining the multi-storage report system

**Key Topics**:
- Storage system responsibilities and optimal use cases
- Multi-storage coordination patterns
- Data flow orchestration
- Consistency strategies and compensation patterns
- Performance optimization strategies
- Multi-tenant isolation
- Cost optimization
- Error handling and recovery
- Monitoring and observability

### [02 - Entity Storage Mapping](./02-entity-storage-mapping.md)
**Purpose**: Detailed mapping of which entities go where and why, with cross-reference patterns  
**Target Audience**: Developers implementing data access layers and understanding entity relationships

**Key Topics**:
- Storage decision matrix for different data types
- SQL Server entity mapping (Reports, Versions, Sections, Fields)
- Cosmos DB document structure and partitioning
- Blob Storage hierarchical organization
- Cross-storage reference patterns
- Data transformation patterns
- Multi-tenant isolation strategies
- Performance optimization patterns

### [03 - Implementation Standards](./03-implementation-standards.md)
**Purpose**: Coding patterns, naming conventions, error handling, and validation for multi-storage implementation  
**Target Audience**: Developers implementing storage services and data access layers

**Key Topics**:
- Naming conventions for documents, blobs, and services
- Repository pattern implementation
- Multi-storage orchestration patterns
- Storage-specific exception handling
- Custom exception types
- Compensation patterns for failed operations
- Document and blob content validation
- Data integrity patterns
- Performance optimization standards
- Security standards
- Testing standards

### [04 - Seeding Strategy Guide](./04-seeding-strategy-guide.md)
**Purpose**: Data initialization approach, dependency management, and troubleshooting for multi-storage seeding  
**Target Audience**: Developers implementing data seeders and managing data initialization

**Key Topics**:
- Seeding architecture and dependency order
- Master seeder orchestration
- SQL Server, Cosmos DB, and Blob Storage seeder implementations
- Troubleshooting common issues (tenant mismatches, size validation, reference corruption)
- Seeding performance optimization
- Recovery procedures for partial failures
- Complete rollback procedures
- Best practices for reliable seeding

### [05 - Developer Quick Reference](./05-developer-quick-reference.md)
**Purpose**: Quick reference for common operations, troubleshooting, and gotchas in multi-storage development  
**Target Audience**: Developers working on day-to-day multi-storage functionality

**Key Topics**:
- Quick start checklist and environment setup
- Common operations (creating reports, loading data, updating, versioning)
- Troubleshooting quick fixes
- Performance optimization tips
- Common gotchas and solutions
- Debugging tools and techniques
- Testing patterns
- Configuration examples
- Useful code snippets and utilities

## Implementation Workflow

When implementing multi-storage functionality, follow this recommended workflow:

1. **Start with Architecture Overview** - Understand the overall design patterns and principles
2. **Review Entity Storage Mapping** - Understand where each type of data should be stored and why
3. **Follow Implementation Standards** - Use the established patterns and conventions
4. **Implement Seeding Strategy** - Set up data initialization following the dependency order
5. **Use Quick Reference** - Reference common operations and troubleshooting during development

## Key Concepts

### Multi-Storage Coordination
The system coordinates three storage technologies:
- **SQL Server**: Structured metadata, relationships, audit trails
- **Cosmos DB**: Large JSON documents, flexible schema, global distribution
- **Blob Storage**: Large files, components, assets, cost-effective archival

### Data Flow Pattern
```
SQL Server (Metadata) → Cosmos DB (Document Data) → Blob Storage (Components/Assets)
                     ↓
              Cross-Storage References
```

### Consistency Model
- **SQL Server**: Immediate consistency
- **Cross-Storage**: Eventually consistent with compensation patterns
- **Error Recovery**: Automated cleanup and retry mechanisms

### Tenant Isolation
- **SQL Server**: Global query filters
- **Cosmos DB**: Partition key strategy
- **Blob Storage**: Path-based isolation

## Expected Cosmos DB Document Format

The guides reference this standard document structure:

```json
{
  "id": "rpt_{reportId}_v_{versionId}",
  "tenantId": "tenant-guid",
  "reportId": "report-guid",
  "versionId": "version-guid",
  "versionNumber": 1,
  "sections": [
    {
      "id": "section-guid",
      "title": "Section Title",
      "type": "text|chart|table",
      "order": 0,
      "fields": [
        {
          "id": "field-guid",
          "name": "fieldName",
          "type": "string|number|date|json",
          "content": "field content",
          "order": 0
        }
      ]
    }
  ],
  "metadata": {
    "createdAt": "2025-06-02T10:30:00Z",
    "createdBy": "user-guid",
    "lastModifiedAt": "2025-06-02T11:45:00Z",
    "lastModifiedBy": "user-guid"
  }
}
```

## Common Issues and Solutions

### Seeder Issues
- **Cosmos DB Seeder**: Document size validation, tenant partitioning, batch processing
- **Blob Seeder**: Path generation, component creation, style management
- **Cross-References**: Maintaining consistency across storage systems

### Performance Issues
- Use parallel data loading for external storage systems
- Implement multi-level caching strategies
- Optimize batch operations to avoid N+1 problems
- Monitor and optimize storage costs

### Data Integrity
- Validate cross-storage references regularly
- Implement compensation patterns for failed operations
- Use health checks to monitor all storage systems
- Plan for eventual consistency scenarios

## Development Best Practices

1. **Always Follow Dependency Order**: SQL → Cosmos → Blob → References → Validation
2. **Implement Comprehensive Validation**: Validate at each phase before proceeding
3. **Use Established Patterns**: Follow the repository and orchestration patterns
4. **Plan for Failures**: Implement compensation and recovery mechanisms
5. **Monitor Everything**: Track performance, errors, and costs across all systems
6. **Maintain Security**: Implement proper tenant isolation and access controls
7. **Test Thoroughly**: Use integration tests with real storage systems
8. **Document Changes**: Keep clear records of any customizations or deviations

## Getting Started

1. Review the [Storage Architecture Overview](./01-storage-architecture-overview.md) to understand the overall design
2. Set up your development environment using the [Developer Quick Reference](./05-developer-quick-reference.md)
3. Implement your first multi-storage operation following the [Implementation Standards](./03-implementation-standards.md)
4. Use the [Entity Storage Mapping](./02-entity-storage-mapping.md) to understand data placement decisions
5. Set up data seeding using the [Seeding Strategy Guide](./04-seeding-strategy-guide.md)

These guides provide a complete foundation for successful multi-storage implementation while maintaining consistency, performance, and reliability across the entire system.
