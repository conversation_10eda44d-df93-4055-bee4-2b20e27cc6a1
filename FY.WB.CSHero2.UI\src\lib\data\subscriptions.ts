// Re-export functions from api.ts with adaptations for route.ts
import { 
  getSubscriptions as apiGetSubscriptions,
  getSubscriptionById as apiGetSubscriptionById,
  getSubscriptionByTypeAndBillingCycle as apiGetSubscriptionByTypeAndBillingCycle,
  getPopularSubscriptions as apiGetPopularSubscriptions
} from '@/lib/api';

// Adapt getSubscriptions to match the signature used in route.ts
export async function getSubscriptions(params?: {
  type?: 'basic' | 'professional' | 'enterprise';
  billingCycle?: 'monthly' | 'quarterly' | 'annual';
  sortBy?: string;
  order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}) {
  // Convert the parameters to match the api.ts function
  const apiParams: Parameters<typeof apiGetSubscriptions>[0] = {
    limit: params?.limit,
    page: params?.page,
    sortBy: params?.sortBy,
    sortOrder: params?.order,
  };
  
  // Add subscription-specific parameters
  if (params?.type) {
    apiParams.type = params.type;
  }
  
  if (params?.billingCycle) {
    apiParams.billingCycle = params.billingCycle;
  }
  
  return apiGetSubscriptions(apiParams);
}

export async function getSubscriptionById(id: string) {
  return apiGetSubscriptionById(id);
}

export async function getSubscriptionByTypeAndBillingCycle(
  type: 'basic' | 'professional' | 'enterprise',
  billingCycle: 'monthly' | 'quarterly' | 'annual'
) {
  return apiGetSubscriptionByTypeAndBillingCycle(type, billingCycle);
}

export async function getPopularSubscriptions() {
  return apiGetPopularSubscriptions();
}
