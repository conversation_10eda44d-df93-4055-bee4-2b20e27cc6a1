using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Domain.Interfaces
{
    /// <summary>
    /// Service interface for exporting reports to various formats
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// Exports a report to PDF format
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportToPdfAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports a report to PowerPoint format
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PowerPoint file as byte array</returns>
        Task<byte[]> ExportToPowerPointAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports a report to Word format
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Word document as byte array</returns>
        Task<byte[]> ExportToWordAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports a report to Excel format
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportToExcelAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports a report to HTML format (static)
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>HTML content as string</returns>
        Task<string> ExportToHtmlAsync(Guid reportId, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a list of supported export formats
        /// </summary>
        /// <returns>List of supported format names</returns>
        Task<List<string>> GetSupportedFormatsAsync();

        /// <summary>
        /// Gets export capabilities for a specific format
        /// </summary>
        /// <param name="format">Format name</param>
        /// <returns>Export capabilities</returns>
        Task<ExportCapabilities> GetExportCapabilitiesAsync(string format);

        /// <summary>
        /// Previews an export without generating the full file
        /// </summary>
        /// <param name="reportId">Report to preview</param>
        /// <param name="format">Export format</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export preview</returns>
        Task<ExportPreview> PreviewExportAsync(Guid reportId, string format, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports multiple reports to a single archive
        /// </summary>
        /// <param name="reportIds">Reports to export</param>
        /// <param name="format">Export format for each report</param>
        /// <param name="options">Export options</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>ZIP archive as byte array</returns>
        Task<byte[]> ExportMultipleReportsAsync(List<Guid> reportIds, string format, ExportOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets export history for a report
        /// </summary>
        /// <param name="reportId">Report identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of export history entries</returns>
        Task<List<ExportHistoryEntry>> GetExportHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Schedules a report export for later processing
        /// </summary>
        /// <param name="reportId">Report to export</param>
        /// <param name="format">Export format</param>
        /// <param name="options">Export options</param>
        /// <param name="scheduledFor">When to process the export</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export job identifier</returns>
        Task<Guid> ScheduleExportAsync(Guid reportId, string format, ExportOptions options, DateTime scheduledFor, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Options for exporting reports
    /// </summary>
    public class ExportOptions
    {
        /// <summary>
        /// Include cover page
        /// </summary>
        public bool IncludeCoverPage { get; set; } = true;

        /// <summary>
        /// Include table of contents
        /// </summary>
        public bool IncludeTableOfContents { get; set; } = true;

        /// <summary>
        /// Include page numbers
        /// </summary>
        public bool IncludePageNumbers { get; set; } = true;

        /// <summary>
        /// Include headers and footers
        /// </summary>
        public bool IncludeHeadersFooters { get; set; } = true;

        /// <summary>
        /// Paper size (A4, Letter, etc.)
        /// </summary>
        public string PaperSize { get; set; } = "A4";

        /// <summary>
        /// Page orientation (Portrait, Landscape)
        /// </summary>
        public string Orientation { get; set; } = "Portrait";

        /// <summary>
        /// Quality setting (High, Medium, Low)
        /// </summary>
        public string Quality { get; set; } = "High";

        /// <summary>
        /// Include charts and images
        /// </summary>
        public bool IncludeImages { get; set; } = true;

        /// <summary>
        /// Compress images
        /// </summary>
        public bool CompressImages { get; set; } = false;

        /// <summary>
        /// Custom styling options
        /// </summary>
        public Dictionary<string, object> CustomStyling { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Sections to include (empty means all sections)
        /// </summary>
        public List<string> IncludeSections { get; set; } = new List<string>();

        /// <summary>
        /// Sections to exclude
        /// </summary>
        public List<string> ExcludeSections { get; set; } = new List<string>();

        /// <summary>
        /// Watermark text
        /// </summary>
        public string? Watermark { get; set; }

        /// <summary>
        /// Password protection
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// Custom metadata
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Template for export formatting
        /// </summary>
        public string? ExportTemplate { get; set; }

        /// <summary>
        /// Language/locale for export
        /// </summary>
        public string Locale { get; set; } = "en-US";
    }

    /// <summary>
    /// Export capabilities for a specific format
    /// </summary>
    public class ExportCapabilities
    {
        public string Format { get; set; } = string.Empty;
        public bool SupportsImages { get; set; }
        public bool SupportsCharts { get; set; }
        public bool SupportsInteractivity { get; set; }
        public bool SupportsPasswordProtection { get; set; }
        public bool SupportsWatermarks { get; set; }
        public bool SupportsCustomStyling { get; set; }
        public List<string> SupportedPaperSizes { get; set; } = new List<string>();
        public List<string> SupportedOrientations { get; set; } = new List<string>();
        public List<string> SupportedQualityLevels { get; set; } = new List<string>();
        public long MaxFileSize { get; set; } // in bytes
        public Dictionary<string, object> AdditionalCapabilities { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Export preview information
    /// </summary>
    public class ExportPreview
    {
        public string Format { get; set; } = string.Empty;
        public int EstimatedPageCount { get; set; }
        public long EstimatedFileSize { get; set; }
        public TimeSpan EstimatedProcessingTime { get; set; }
        public List<string> IncludedSections { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public string PreviewImageUrl { get; set; } = string.Empty;
        public Dictionary<string, object> AdditionalInfo { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Export history entry
    /// </summary>
    public class ExportHistoryEntry
    {
        public Guid Id { get; set; }
        public Guid ReportId { get; set; }
        public string Format { get; set; } = string.Empty;
        public DateTime ExportedAt { get; set; }
        public Guid ExportedBy { get; set; }
        public long FileSize { get; set; }
        public string Status { get; set; } = string.Empty; // Success, Failed, Processing
        public string? ErrorMessage { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public ExportOptions Options { get; set; } = new ExportOptions();
        public string? DownloadUrl { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// Export job status
    /// </summary>
    public class ExportJob
    {
        public Guid Id { get; set; }
        public Guid ReportId { get; set; }
        public string Format { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Scheduled, Processing, Completed, Failed
        public DateTime ScheduledFor { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? ErrorMessage { get; set; }
        public int Progress { get; set; } // 0-100
        public string? ResultUrl { get; set; }
        public ExportOptions Options { get; set; } = new ExportOptions();
    }
}
