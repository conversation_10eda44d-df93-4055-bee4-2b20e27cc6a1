using System;
using System.Collections.Generic;
using FY.WB.CSHero2.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Enhanced metadata for version creation
    /// </summary>
    public class VersionMetadata
    {
        /// <summary>
        /// Description of changes in this version
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Branch name if this is a branch version
        /// </summary>
        public string? BranchName { get; set; }

        /// <summary>
        /// Source version number if this is a branch or merge
        /// </summary>
        public int? SourceVersionNumber { get; set; }

        /// <summary>
        /// Whether this version is a branch
        /// </summary>
        public bool IsBranch { get; set; } = false;

        /// <summary>
        /// Whether this version is a merge
        /// </summary>
        public bool IsMerge { get; set; } = false;

        /// <summary>
        /// Tags associated with this version
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// Custom metadata properties
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Change summary from previous version
        /// </summary>
        public ChangeSummary? ChangeSummary { get; set; }

        /// <summary>
        /// Release notes for this version
        /// </summary>
        public string? ReleaseNotes { get; set; }

        /// <summary>
        /// Priority level of this version
        /// </summary>
        public string Priority { get; set; } = "Normal"; // Low, Normal, High, Critical

        /// <summary>
        /// Version type classification
        /// </summary>
        public string VersionType { get; set; } = "Minor"; // Major, Minor, Patch, Hotfix

        /// <summary>
        /// Approval status for this version
        /// </summary>
        public string ApprovalStatus { get; set; } = "Draft"; // Draft, Pending, Approved, Rejected

        /// <summary>
        /// User who approved this version
        /// </summary>
        public Guid? ApprovedBy { get; set; }

        /// <summary>
        /// When this version was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }
    }

    /// <summary>
    /// Detailed comparison between two versions
    /// </summary>
    public class VersionComparison
    {
        /// <summary>
        /// Report ID being compared
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// First version number
        /// </summary>
        public int Version1 { get; set; }

        /// <summary>
        /// Second version number
        /// </summary>
        public int Version2 { get; set; }

        /// <summary>
        /// When the comparison was performed
        /// </summary>
        public DateTime ComparedAt { get; set; }

        /// <summary>
        /// Changes in metadata between versions
        /// </summary>
        public List<MetadataChange> MetadataChanges { get; set; } = new List<MetadataChange>();

        /// <summary>
        /// Changes in component data between versions
        /// </summary>
        public List<ComponentChange> ComponentChanges { get; set; } = new List<ComponentChange>();

        /// <summary>
        /// Changes in report data between versions
        /// </summary>
        public List<DataChange> DataChanges { get; set; } = new List<DataChange>();

        /// <summary>
        /// Overall summary of changes
        /// </summary>
        public ChangeSummary Summary { get; set; } = new ChangeSummary();

        /// <summary>
        /// Compatibility assessment between versions
        /// </summary>
        public CompatibilityAssessment Compatibility { get; set; } = new CompatibilityAssessment();
    }

    /// <summary>
    /// Change in metadata properties
    /// </summary>
    public class MetadataChange
    {
        /// <summary>
        /// Property that changed
        /// </summary>
        public string Property { get; set; } = string.Empty;

        /// <summary>
        /// Old value
        /// </summary>
        public string? OldValue { get; set; }

        /// <summary>
        /// New value
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// Type of change (Added, Removed, Modified)
        /// </summary>
        public string ChangeType { get; set; } = string.Empty;

        /// <summary>
        /// Impact level of this change
        /// </summary>
        public string Impact { get; set; } = "Low"; // Low, Medium, High, Critical
    }

    /// <summary>
    /// Change in component data
    /// </summary>
    public class ComponentChange
    {
        /// <summary>
        /// Section ID that changed
        /// </summary>
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// Section name
        /// </summary>
        public string SectionName { get; set; } = string.Empty;

        /// <summary>
        /// Type of change (Added, Removed, Modified, TypesModified)
        /// </summary>
        public string ChangeType { get; set; } = string.Empty;

        /// <summary>
        /// Description of the change
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Impact level of this change
        /// </summary>
        public string Impact { get; set; } = "Low"; // Low, Medium, High, Critical

        /// <summary>
        /// Whether this is a breaking change
        /// </summary>
        public bool IsBreaking { get; set; } = false;

        /// <summary>
        /// Old component code (for modifications)
        /// </summary>
        public string? OldComponentCode { get; set; }

        /// <summary>
        /// New component code (for modifications)
        /// </summary>
        public string? NewComponentCode { get; set; }

        /// <summary>
        /// Code diff summary
        /// </summary>
        public CodeDiff? CodeDiff { get; set; }
    }

    /// <summary>
    /// Change in report data
    /// </summary>
    public class DataChange
    {
        /// <summary>
        /// Field name that changed
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Type of change (Added, Removed, Modified)
        /// </summary>
        public string ChangeType { get; set; } = string.Empty;

        /// <summary>
        /// Old value
        /// </summary>
        public string? OldValue { get; set; }

        /// <summary>
        /// New value
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// Data type of the field
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// Impact level of this change
        /// </summary>
        public string Impact { get; set; } = "Low"; // Low, Medium, High, Critical
    }

    /// <summary>
    /// Summary of changes between versions
    /// </summary>
    public class ChangeSummary
    {
        /// <summary>
        /// Total number of changes
        /// </summary>
        public int TotalChanges { get; set; }

        /// <summary>
        /// Number of metadata changes
        /// </summary>
        public int MetadataChanges { get; set; }

        /// <summary>
        /// Number of component changes
        /// </summary>
        public int ComponentChanges { get; set; }

        /// <summary>
        /// Number of data changes
        /// </summary>
        public int DataChanges { get; set; }

        /// <summary>
        /// Whether there are breaking changes
        /// </summary>
        public bool HasBreakingChanges { get; set; }

        /// <summary>
        /// Types of changes present
        /// </summary>
        public List<string> ChangeTypes { get; set; } = new List<string>();

        /// <summary>
        /// Overall impact level
        /// </summary>
        public string OverallImpact { get; set; } = "Low"; // Low, Medium, High, Critical

        /// <summary>
        /// Recommended action
        /// </summary>
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Code difference information
    /// </summary>
    public class CodeDiff
    {
        /// <summary>
        /// Number of lines added
        /// </summary>
        public int LinesAdded { get; set; }

        /// <summary>
        /// Number of lines removed
        /// </summary>
        public int LinesRemoved { get; set; }

        /// <summary>
        /// Number of lines modified
        /// </summary>
        public int LinesModified { get; set; }

        /// <summary>
        /// Percentage of code changed
        /// </summary>
        public double ChangePercentage { get; set; }

        /// <summary>
        /// Detailed diff information
        /// </summary>
        public List<DiffLine> DiffLines { get; set; } = new List<DiffLine>();
    }

    /// <summary>
    /// Individual line difference
    /// </summary>
    public class DiffLine
    {
        /// <summary>
        /// Line number in old version
        /// </summary>
        public int? OldLineNumber { get; set; }

        /// <summary>
        /// Line number in new version
        /// </summary>
        public int? NewLineNumber { get; set; }

        /// <summary>
        /// Type of change (Added, Removed, Modified, Unchanged)
        /// </summary>
        public string ChangeType { get; set; } = string.Empty;

        /// <summary>
        /// Line content
        /// </summary>
        public string Content { get; set; } = string.Empty;
    }

    /// <summary>
    /// Compatibility assessment between versions
    /// </summary>
    public class CompatibilityAssessment
    {
        /// <summary>
        /// Whether versions are backward compatible
        /// </summary>
        public bool IsBackwardCompatible { get; set; } = true;

        /// <summary>
        /// Whether versions are forward compatible
        /// </summary>
        public bool IsForwardCompatible { get; set; } = true;

        /// <summary>
        /// Compatibility issues found
        /// </summary>
        public List<CompatibilityIssue> Issues { get; set; } = new List<CompatibilityIssue>();

        /// <summary>
        /// Migration requirements
        /// </summary>
        public List<string> MigrationRequirements { get; set; } = new List<string>();

        /// <summary>
        /// Overall compatibility score (0-100)
        /// </summary>
        public int CompatibilityScore { get; set; } = 100;
    }

    /// <summary>
    /// Compatibility issue between versions
    /// </summary>
    public class CompatibilityIssue
    {
        /// <summary>
        /// Issue type
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Issue description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Severity level
        /// </summary>
        public string Severity { get; set; } = "Low"; // Low, Medium, High, Critical

        /// <summary>
        /// Affected component or section
        /// </summary>
        public string AffectedComponent { get; set; } = string.Empty;

        /// <summary>
        /// Suggested resolution
        /// </summary>
        public string SuggestedResolution { get; set; } = string.Empty;
    }

    /// <summary>
    /// Enhanced version history entry
    /// </summary>
    public class VersionHistoryEntry
    {
        /// <summary>
        /// Version number
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// Version description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// When the version was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Who created the version
        /// </summary>
        public Guid CreatedBy { get; set; }

        /// <summary>
        /// Whether this is the current version
        /// </summary>
        public bool IsCurrent { get; set; }

        /// <summary>
        /// Number of components in this version
        /// </summary>
        public int ComponentCount { get; set; }

        /// <summary>
        /// Total data size for this version
        /// </summary>
        public long DataSize { get; set; }

        /// <summary>
        /// Changes from the previous version
        /// </summary>
        public ChangeSummary? ChangesFromPrevious { get; set; }

        /// <summary>
        /// Version metadata
        /// </summary>
        public VersionMetadata? Metadata { get; set; }

        /// <summary>
        /// Tags associated with this version
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// Branch information if this is a branch
        /// </summary>
        public BranchInfo? BranchInfo { get; set; }
    }

    /// <summary>
    /// Branch information
    /// </summary>
    public class BranchInfo
    {
        /// <summary>
        /// Branch name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Source version this branch was created from
        /// </summary>
        public int SourceVersion { get; set; }

        /// <summary>
        /// Whether this branch has been merged
        /// </summary>
        public bool IsMerged { get; set; } = false;

        /// <summary>
        /// Version this branch was merged into
        /// </summary>
        public int? MergedIntoVersion { get; set; }

        /// <summary>
        /// When this branch was merged
        /// </summary>
        public DateTime? MergedAt { get; set; }
    }

    // Additional models for detailed version comparison and rollback analysis

    /// <summary>
    /// Detailed version comparison with enhanced analysis
    /// </summary>
    public class DetailedVersionComparison
    {
        public Guid ReportId { get; set; }
        public int Version1 { get; set; }
        public int Version2 { get; set; }
        public DateTime ComparedAt { get; set; }
        public VersionInfo Version1Info { get; set; } = new VersionInfo();
        public VersionInfo Version2Info { get; set; } = new VersionInfo();
        public List<DetailedComponentComparison> ComponentComparisons { get; set; } = new List<DetailedComponentComparison>();
        public List<DetailedDataComparison> DataComparisons { get; set; } = new List<DetailedDataComparison>();
        public ImpactAnalysis ImpactAnalysis { get; set; } = new ImpactAnalysis();
        public List<string> MigrationRecommendations { get; set; } = new List<string>();
        public int CompatibilityScore { get; set; } = 100;
    }

    /// <summary>
    /// Version information summary
    /// </summary>
    public class VersionInfo
    {
        public int VersionNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public bool IsCurrent { get; set; }
        public int ComponentCount { get; set; }
        public long DataSize { get; set; }
    }

    /// <summary>
    /// Detailed component comparison
    /// </summary>
    public class DetailedComponentComparison
    {
        public string SectionId { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public string ChangeType { get; set; } = string.Empty; // Added, Removed, Modified, Unchanged
        public SectionComponent? OldComponent { get; set; }
        public SectionComponent? NewComponent { get; set; }
        public CodeDiff? CodeDiff { get; set; }
    }

    /// <summary>
    /// Detailed data comparison
    /// </summary>
    public class DetailedDataComparison
    {
        public string FieldName { get; set; } = string.Empty;
        public string ChangeType { get; set; } = string.Empty; // Added, Removed, Modified, Unchanged
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public string? ValueDiff { get; set; }
    }

    /// <summary>
    /// Impact analysis of version changes
    /// </summary>
    public class ImpactAnalysis
    {
        public int ComponentsAffected { get; set; }
        public int ComponentsAdded { get; set; }
        public int ComponentsRemoved { get; set; }
        public int ComponentsModified { get; set; }
        public int DataFieldsAffected { get; set; }
        public int DataFieldsAdded { get; set; }
        public int DataFieldsRemoved { get; set; }
        public int DataFieldsModified { get; set; }
        public string OverallImpactLevel { get; set; } = "Low"; // None, Low, Medium, High, Critical
    }

    /// <summary>
    /// Rollback impact analysis
    /// </summary>
    public class RollbackImpactAnalysis
    {
        public Guid ReportId { get; set; }
        public int CurrentVersion { get; set; }
        public int TargetVersion { get; set; }
        public DateTime AnalyzedAt { get; set; }
        public DataLossAnalysis DataLoss { get; set; } = new DataLossAnalysis();
        public List<ComponentRollbackChange> ComponentChanges { get; set; } = new List<ComponentRollbackChange>();
        public FeatureImpactAnalysis FeatureImpact { get; set; } = new FeatureImpactAnalysis();
        public RiskAssessment RiskAssessment { get; set; } = new RiskAssessment();
        public List<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Data loss analysis for rollback
    /// </summary>
    public class DataLossAnalysis
    {
        public List<string> LostDataFields { get; set; } = new List<string>();
        public List<string> LostComponents { get; set; } = new List<string>();
        public string EstimatedDataLoss { get; set; } = "None"; // None, Low, Medium, High, Critical
        public List<string> RecommendedActions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Component rollback change information
    /// </summary>
    public class ComponentRollbackChange
    {
        public string SectionId { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public string ChangeType { get; set; } = string.Empty; // WillBeAdded, WillBeRemoved, WillBeReverted, NoChange
        public string Impact { get; set; } = "Low"; // None, Low, Medium, High, Critical
    }

    /// <summary>
    /// Feature impact analysis for rollback
    /// </summary>
    public class FeatureImpactAnalysis
    {
        public int VersionsToRollback { get; set; }
        public string EstimatedFeatureLoss { get; set; } = "Minimal"; // Minimal, Low, Medium, High, Severe
        public List<string> AffectedFeatures { get; set; } = new List<string>();
    }

    /// <summary>
    /// Risk assessment for rollback
    /// </summary>
    public class RiskAssessment
    {
        public int RiskScore { get; set; }
        public string RiskLevel { get; set; } = "Low"; // Low, Medium, High, Critical
        public List<string> RiskFactors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Rollback options
    /// </summary>
    public class RollbackOptions
    {
        public bool CreateBackup { get; set; } = true;
        public bool ForceRollback { get; set; } = false;
        public string Reason { get; set; } = string.Empty;
        public bool NotifyUsers { get; set; } = true;
        public bool ValidateBeforeRollback { get; set; } = true;
    }

    /// <summary>
    /// Rollback result
    /// </summary>
    public class RollbackResult
    {
        public Guid ReportId { get; set; }
        public int TargetVersion { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime CompletedAt { get; set; }
        public Guid? BackupVersionId { get; set; }
        public RollbackValidation? ValidationResult { get; set; }
        public TimeSpan Duration => CompletedAt - StartedAt;
    }

    /// <summary>
    /// Rollback validation result
    /// </summary>
    public class RollbackValidation
    {
        public bool IsValid { get; set; } = true;
        public List<string> ValidationErrors { get; set; } = new List<string>();
        public List<string> ValidationWarnings { get; set; } = new List<string>();
    }
}
