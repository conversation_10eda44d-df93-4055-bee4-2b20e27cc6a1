# Simplified CosmosSeeder Data Format Alignment Plan

## Executive Summary

This is a simplified implementation plan for a development/testing environment. The focus is on making direct changes to align the CosmosSeeder with the expected JSON format without backward compatibility, migrations, or complex rollback strategies.

## Scope Limitations

- **Development Environment Only** - No production considerations
- **No Backward Compatibility** - Direct breaking changes allowed
- **No Migrations** - Direct model updates
- **Manual Testing Only** - Verify by running project and checking CosmosDB
- **Isolated Seed Data** - Easy removal when testing completed

## Implementation Tasks

### Task 1: Update VersionedReportDataDocument Model
**File:** `FY.WB.CSHero2.Infrastructure/Models/CosmosDocumentModels.cs`

**Direct Changes Required:**

1. **Update ReportDataSection class:**
   ```csharp
   public class ReportDataSection
   {
       [JsonPropertyName("id")]
       public Guid Id { get; set; }
       
       [JsonPropertyName("title")]
       public string Title { get; set; } = string.Empty;
       
       [JsonPropertyName("type")]
       public string Type { get; set; } = string.Empty;        // RENAMED FROM SectionType
       
       [JsonPropertyName("order")]
       public int Order { get; set; }                          // RENAMED FROM DisplayOrder
       
       [JsonPropertyName("fields")]
       public List<ReportDataField> Fields { get; set; } = new();
   }
   ```

2. **Update ReportDataField class:**
   ```csharp
   public class ReportDataField
   {
       [JsonPropertyName("id")]
       public Guid Id { get; set; }
       
       [JsonPropertyName("name")]
       public string Name { get; set; } = string.Empty;
       
       [JsonPropertyName("type")]
       public string Type { get; set; } = string.Empty;        // RENAMED FROM FieldType
       
       [JsonPropertyName("content")]
       public string Content { get; set; } = string.Empty;     // RENAMED FROM DefaultValue
       
       [JsonPropertyName("order")]
       public int Order { get; set; }                          // RENAMED FROM DisplayOrder
   }
   ```

3. **Update ReportDataMetadata class:**
   ```csharp
   public class ReportDataMetadata
   {
       [JsonPropertyName("createdAt")]
       public DateTime CreatedAt { get; set; }
       
       [JsonPropertyName("createdBy")]
       public string CreatedBy { get; set; } = string.Empty;    // NEW FIELD
       
       [JsonPropertyName("lastModifiedAt")]
       public DateTime LastModifiedAt { get; set; }             // RENAMED FROM UpdatedAt
       
       [JsonPropertyName("lastModifiedBy")]
       public string LastModifiedBy { get; set; } = string.Empty; // NEW FIELD
   }
   ```

### Task 2: Update CosmosSeeder Transformation Logic
**File:** `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Method:** `CreateVersionedReportDocumentAsync` (lines 290-311)

**Direct Changes:**

1. **Update sections transformation:**
   ```csharp
   var sections = report.Sections?.Select(s => new ReportDataSection
   {
       Id = s.Id,
       Title = s.Title,
       Type = s.Type ?? "default",             // UPDATED PROPERTY NAME
       Order = s.Order,                        // UPDATED PROPERTY NAME
       Fields = s.Fields?.Select(f => new ReportDataField
       {
           Id = f.Id,
           Name = f.Name,
           Type = f.Type,                      // UPDATED PROPERTY NAME
           Content = f.Content ?? string.Empty, // UPDATED PROPERTY NAME
           Order = f.Order                     // UPDATED PROPERTY NAME
       }).ToList() ?? new List<ReportDataField>()
   }).ToList() ?? new List<ReportDataSection>();
   ```

2. **Update metadata creation (lines 335-343):**
   ```csharp
   Metadata = new ReportDataMetadata
   {
       CreatedAt = version.CreationTime.ToUniversalTime(),
       CreatedBy = report.Author ?? "system",                    // NEW FIELD
       LastModifiedAt = (version.LastModificationTime ?? version.CreationTime).ToUniversalTime(), // RENAMED
       LastModifiedBy = report.Author ?? "system"                // NEW FIELD
   }
   ```

### Task 3: Update Validation Methods
**File:** `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Methods to Update:**
- `ValidateDocumentForCosmosDb` (lines 465-549)
- `ValidateDocumentBeforeUpsert` (lines 425-463)

**Changes:**
- Update property name references in validation logic
- Update error messages to reflect new property names

### Task 4: Verification Steps

1. **Run the application**
2. **Check CosmosDB for generated documents**
3. **Verify document structure matches expected format:**
   ```json
   {
     "id": "rpt_..._v_...",
     "reportId": "guid",
     "versionId": "guid", 
     "versionNumber": 1,
     "sections": [
       {
         "id": "guid",
         "title": "Section Title",
         "type": "text",
         "order": 0,
         "fields": [
           {
             "id": "guid",
             "name": "field-name",
             "type": "string",
             "content": "field content",
             "order": 0
           }
         ]
       }
     ],
     "metadata": {
       "createdAt": "2025-06-02T10:30:00Z",
       "createdBy": "user-guid",
       "lastModifiedAt": "2025-06-02T11:45:00Z",
       "lastModifiedBy": "user-guid"
     }
   }
   ```

## Implementation Order

1. **First:** Update `CosmosDocumentModels.cs` - Update all model classes
2. **Second:** Update `CosmosSeeder.cs` - Update transformation logic
3. **Third:** Update validation methods in `CosmosSeeder.cs`
4. **Fourth:** Test by running application and checking CosmosDB

## Expected Outcome

After implementation:
- Generated documents will exactly match the expected JSON format
- Property names will align with specification
- Metadata will include user tracking fields
- Manual testing will confirm correct structure in CosmosDB

## Files to Modify

1. `FY.WB.CSHero2.Infrastructure/Models/CosmosDocumentModels.cs`
2. `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

## Testing Approach

- **Manual Testing Only**
- Run application
- Check CosmosDB documents
- Verify JSON structure matches expected format
- No automated tests required for this development phase

This simplified approach focuses on direct implementation without complexity, perfect for a development/testing environment where the seed data can be easily removed after testing is complete.