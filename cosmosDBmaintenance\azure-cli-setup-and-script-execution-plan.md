# Azure CLI Setup and CosmosDB Container Creation Plan

## Overview
This document provides a complete guide to install Azure CLI and execute the `create-reports-container.ps1` script to create the missing Reports container in your CosmosDB database.

## Current Situation
- **Script Location**: `.\cosmosDBmaintenance\create-reports-container.ps1`
- **Issue**: Azure CLI works in regular PowerShell but not in VS Code PowerShell Terminal
- **Target**: Create "Reports" container in ReportRenderingEngine database with `/tenantId` partition key

## VS Code PowerShell Terminal Issue
The Azure CLI commands work in a regular PowerShell window but not in VS Code's integrated PowerShell terminal. This is a common issue with PATH environment variables not being updated in VS Code's terminal session.

## Solution Options

### Option 1: Use Regular PowerShell Terminal (Recommended)
Since Azure CLI works in regular PowerShell, execute the script outside of VS Code:

1. **Open Regular PowerShell** (not VS Code terminal):
   - Press `Win + R`, type `powershell`, press Enter
   - Or search "PowerShell" in Start Menu

2. **Navigate to Project Directory**:
   ```powershell
   cd "c:\Users\<USER>\Documents\GitHub\ChildrensVillage\FY.WB.CSHero2"
   ```

3. **Execute the Script**:
   ```powershell
   .\cosmosDBmaintenance\create-reports-container.ps1
   ```

### Option 2: Fix VS Code PowerShell Terminal PATH

#### Method A: Restart VS Code
1. Close VS Code completely
2. Reopen VS Code
3. Open a new PowerShell terminal
4. Test: `az --version`

#### Method B: Refresh Environment Variables in VS Code Terminal
```powershell
# Refresh environment variables
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# Test Azure CLI
az --version
```

#### Method C: Add Azure CLI to PATH Manually
```powershell
# Add Azure CLI to current session PATH
$env:Path += ";C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin"

# Test Azure CLI
az --version
```

### Option 3: Use Full Path to Azure CLI
If Azure CLI is installed but not in PATH for VS Code terminal:
```powershell
# Use full path to az.exe
& "C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.exe" --version
```

## Prerequisites Verification

### Check if Azure CLI is Already Installed
In regular PowerShell terminal:
```powershell
az --version
```

If this works, Azure CLI is installed and the issue is just with VS Code's terminal environment.

## Recommended Execution Steps

### Step 1: Use Regular PowerShell Terminal
1. **Open Regular PowerShell** (outside VS Code)
2. **Navigate to project**:
   ```powershell
   cd "c:\Users\<USER>\Documents\GitHub\ChildrensVillage\FY.WB.CSHero2"
   ```

### Step 2: Verify Azure CLI
```powershell
az --version
```

### Step 3: Login to Azure (if needed)
```powershell
az login
```
This will:
- Open your default browser
- Prompt for Azure credentials
- Authenticate your session

### Step 4: Verify Access to Resources
```powershell
# Check if you can access the resource group
az group show --name "rg_CSHero"

# Check if you can access the CosmosDB account
az cosmosdb show --name "cshero-cosmosdb" --resource-group "rg_CSHero"
```

### Step 5: Execute the Container Creation Script

#### Basic Execution (Uses Default Parameters)
```powershell
.\cosmosDBmaintenance\create-reports-container.ps1
```

#### With Custom Parameters (if needed)
```powershell
.\cosmosDBmaintenance\create-reports-container.ps1 -ResourceGroup "rg_CSHero" -CosmosAccount "cshero-cosmosdb" -DatabaseName "ReportRenderingEngine"
```

## Expected Results

### Successful Execution Output
```
=== Creating Reports Container ===
Resource Group: rg_CSHero
Cosmos Account: cshero-cosmosdb
Database: ReportRenderingEngine

Logged in as: <EMAIL>

Creating container: Reports
✅ Reports container created successfully!
Database: ReportRenderingEngine
Container: Reports (partitioned by /tenantId)

This matches the application configuration in appsettings.json
```

## Configuration Alignment

The script creates a container that matches your application configuration:

**appsettings.json Configuration:**
```json
"CosmosDb": {
  "DatabaseName": "ReportRenderingEngine",
  "ContainerName": "Reports"
}
```

**Container Specifications:**
- **Name**: Reports
- **Database**: ReportRenderingEngine
- **Partition Key**: `/tenantId`
- **Account**: cshero-cosmosdb
- **Resource Group**: rg_CSHero

## Troubleshooting Guide

### Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| "az is not recognized" in VS Code | PATH not updated in VS Code terminal | Use regular PowerShell terminal or restart VS Code |
| "az is not recognized" everywhere | Azure CLI not installed | Install Azure CLI |
| "Please login to Azure CLI" | Not authenticated | Run `az login` |
| "Insufficient permissions" | Missing CosmosDB permissions | Contact Azure admin for Contributor role |
| "Resource group not found" | Wrong resource group name | Verify resource group exists |
| "Container already exists" | Container was previously created | This is normal - script handles gracefully |

### VS Code Terminal Specific Issues

| Issue | Solution |
|-------|----------|
| Azure CLI works in regular PowerShell but not VS Code | Use regular PowerShell terminal |
| PATH environment variable not updated | Restart VS Code or refresh PATH manually |
| Terminal session doesn't see new installations | Close and reopen VS Code |

### Permission Requirements
You need one of these roles on the resource group:
- **Contributor**
- **CosmosDB Account Contributor**
- **DocumentDB Account Contributor**

### Network Requirements
- Internet access to Azure endpoints
- No corporate firewall blocking Azure CLI

## Alternative Approaches

### If Azure CLI Installation Fails
You can create the container using:

1. **Azure Portal** (Web Interface):
   - Navigate to Azure Portal → CosmosDB → cshero-cosmosdb
   - Go to Data Explorer → ReportRenderingEngine database
   - Click "New Container"
   - Set Container ID: "Reports"
   - Set Partition Key: "/tenantId"

2. **Azure PowerShell Module**:
   ```powershell
   Install-Module -Name Az -AllowClobber -Scope CurrentUser
   Connect-AzAccount
   ```

## Security Considerations

### Connection String Security
Your `appsettings.json` contains production connection strings. Consider:
- Using Azure Key Vault for production
- Environment-specific configuration files
- Rotating access keys regularly

### Best Practices
- Use managed identities when possible
- Implement least-privilege access
- Monitor CosmosDB access logs

## Next Steps After Container Creation

1. **Verify Container Creation**:
   ```powershell
   az cosmosdb sql container show --account-name "cshero-cosmosdb" --resource-group "rg_CSHero" --database-name "ReportRenderingEngine" --name "Reports"
   ```

2. **Test Application Connectivity**:
   - Run your application
   - Verify it can connect to the new container
   - Check application logs for any errors

3. **Data Migration** (if needed):
   - Review existing data migration scripts
   - Plan data seeding if required

## Summary

**Immediate Action Required:**
1. Install Azure CLI
2. Restart PowerShell
3. Run `az login`
4. Execute the script: `.\cosmosDBmaintenance\create-reports-container.ps1`

**Expected Outcome:**
- Reports container created in ReportRenderingEngine database
- Container partitioned by `/tenantId`
- Application configuration alignment achieved
- Ready for application deployment and testing

## Support Resources

- **Azure CLI Documentation**: https://docs.microsoft.com/en-us/cli/azure/
- **CosmosDB CLI Reference**: https://docs.microsoft.com/en-us/cli/azure/cosmosdb
- **Partition Key Best Practices**: https://docs.microsoft.com/en-us/azure/cosmos-db/partitioning-overview