using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for SQL database operations (metadata and style selections)
    /// </summary>
    public class ReportMetadataRepository : IReportMetadataRepository
    {
        private readonly IApplicationDbContext _context;
        private readonly ILogger<ReportMetadataRepository> _logger;

        public ReportMetadataRepository(IApplicationDbContext context, ILogger<ReportMetadataRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Report Operations

        public async Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report {ReportId}", reportId);
                
                return await _context.Reports
                    .Include(r => r.Client)
                    .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<Report> CreateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report {ReportId}", report.Id);
                
                _context.Reports.Add(report);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully created report {ReportId}", report.Id);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report {ReportId}", report.Id);
                throw;
            }
        }

        public async Task<Report> UpdateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report {ReportId}", report.Id);
                
                _context.Reports.Update(report);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully updated report {ReportId}", report.Id);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report {ReportId}", report.Id);
                throw;
            }
        }

        public async Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report {ReportId}", reportId);
                
                var report = await _context.Reports.FindAsync(new object[] { reportId }, cancellationToken);
                if (report != null)
                {
                    _context.Reports.Remove(report);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Report {ReportId} not found for deletion", reportId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<IEnumerable<Report>> GetReportsAsync(Guid? clientId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting reports for client {ClientId}", clientId);
                
                var query = _context.Reports.Include(r => r.Client).AsQueryable();
                
                if (clientId.HasValue)
                {
                    query = query.Where(r => r.ClientId == clientId.Value);
                }
                
                return await query.ToListAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports for client {ClientId}", clientId);
                throw;
            }
        }

        #endregion

        #region Report Version Operations

        public async Task<ReportVersion?> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report version {VersionId}", versionId);
                
                return await _context.ReportVersions
                    .Include(rv => rv.Report)
                    .FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report version {VersionId}", versionId);
                throw;
            }
        }

        public async Task<ReportVersion?> GetCurrentReportVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting current report version for report {ReportId}", reportId);
                
                return await _context.ReportVersions
                    .Include(rv => rv.Report)
                    .Where(rv => rv.ReportId == reportId)
                    .OrderByDescending(rv => rv.VersionNumber)
                    .FirstOrDefaultAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current report version for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<ReportVersion> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report version {VersionId} for report {ReportId}", version.Id, version.ReportId);
                
                _context.ReportVersions.Add(version);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully created report version {VersionId}", version.Id);
                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report version {VersionId}", version.Id);
                throw;
            }
        }

        public async Task<ReportVersion> UpdateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report version {VersionId}", version.Id);
                
                _context.ReportVersions.Update(version);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully updated report version {VersionId}", version.Id);
                return version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report version {VersionId}", version.Id);
                throw;
            }
        }

        public async Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report version {VersionId}", versionId);
                
                var version = await _context.ReportVersions.FindAsync(new object[] { versionId }, cancellationToken);
                if (version != null)
                {
                    _context.ReportVersions.Remove(version);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted report version {VersionId}", versionId);
                }
                else
                {
                    _logger.LogWarning("Report version {VersionId} not found for deletion", versionId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report version {VersionId}", versionId);
                throw;
            }
        }

        public async Task<IEnumerable<ReportVersion>> GetReportVersionsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report versions for report {ReportId}", reportId);
                
                return await _context.ReportVersions
                    .Include(rv => rv.Report)
                    .Where(rv => rv.ReportId == reportId)
                    .OrderByDescending(rv => rv.VersionNumber)
                    .ToListAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report versions for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Report Style Operations

        public async Task<ReportStyle?> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report style for report {ReportId}", reportId);
                
                return await _context.ReportStyles
                    .Include(rs => rs.Report)
                    .FirstOrDefaultAsync(rs => rs.ReportId == reportId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report style for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<ReportStyle> CreateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report style {StyleId} for report {ReportId}", style.Id, style.ReportId);
                
                _context.ReportStyles.Add(style);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully created report style {StyleId}", style.Id);
                return style;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report style {StyleId}", style.Id);
                throw;
            }
        }

        public async Task<ReportStyle> UpdateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report style {StyleId}", style.Id);
                
                _context.ReportStyles.Update(style);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Successfully updated report style {StyleId}", style.Id);
                return style;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report style {StyleId}", style.Id);
                throw;
            }
        }

        public async Task DeleteReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report style for report {ReportId}", reportId);
                
                var style = await _context.ReportStyles.FirstOrDefaultAsync(rs => rs.ReportId == reportId, cancellationToken);
                if (style != null)
                {
                    _context.ReportStyles.Remove(style);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Successfully deleted report style for report {ReportId}", reportId);
                }
                else
                {
                    _logger.LogWarning("Report style for report {ReportId} not found for deletion", reportId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report style for report {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await _context.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving changes to database");
                throw;
            }
        }

        #endregion
    }
}
