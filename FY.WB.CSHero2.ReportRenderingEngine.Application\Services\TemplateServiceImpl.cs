using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Implementation of template service for managing report templates
    /// </summary>
    public class TemplateServiceImpl : ITemplateService
    {
        private readonly ILogger<TemplateServiceImpl> _logger;
        private readonly ApplicationDbContext _context;

        public TemplateServiceImpl(
            ILogger<TemplateServiceImpl> logger,
            ApplicationDbContext context)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// Gets a template by ID
        /// </summary>
        public async Task<Template> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting template with ID: {TemplateId}", templateId);

            var template = await _context.Templates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

            if (template == null)
            {
                throw new InvalidOperationException($"Template with ID {templateId} not found");
            }

            return template;
        }

        /// <summary>
        /// Gets public templates available to all users
        /// </summary>
        public async Task<List<Template>> GetPublicTemplatesAsync(string? category = null, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting public templates for category: {Category}", category);

            var query = _context.Templates
                .Where(t => t.IsPublic && t.IsActive);

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(t => t.Category == category);
            }

            return await query
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets templates owned by the current user
        /// </summary>
        public async Task<List<Template>> GetUserTemplatesAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting user templates");

            // Note: In a real implementation, this would filter by current user
            // For now, returning all non-public templates
            return await _context.Templates
                .Where(t => !t.IsPublic && t.IsActive)
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Clones a template to create a new report
        /// </summary>
        public async Task<Report> CloneTemplateAsync(Guid templateId, string reportName, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Cloning template {TemplateId} to create report: {ReportName}",
                templateId, reportName);

            var template = await GetTemplateAsync(templateId, cancellationToken);

            // Create new report from template
            var report = new Report(
                Guid.NewGuid(),
                string.Empty, // ReportNumber - would be generated by business logic
                Guid.Empty, // ClientId - would be set by caller
                string.Empty, // ClientName - would be set by caller
                reportName,
                template.Category,
                0, // SlideCount - initial value
                "Draft",
                string.Empty) // Author - would be set by caller
            {
                TemplateId = template.Id,
                ReportType = "Template-based"
            };

            _context.Reports.Add(report);

            // Increment template usage count
            template.IncrementUsage();
            _context.Templates.Update(template);

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully cloned template {TemplateId} to report {ReportId}",
                template.Id, report.Id);

            return report;
        }

        /// <summary>
        /// Creates a new template
        /// </summary>
        public async Task<Template> CreateTemplateAsync(CreateTemplateRequest request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Creating new template: {TemplateName}", request.Name);

            var template = new Template(
                Guid.NewGuid(),
                request.Name,
                request.Description,
                request.Category,
                request.ThumbnailUrl,
                request.Tags)
            {
                IsPublic = request.IsPublic,
                IsActive = true,
                Version = "1.0.0",
                UsageCount = 0,
                EstimatedCompletionTimeMinutes = request.EstimatedCompletionTimeMinutes
            };

            // Set default styling
            if (!string.IsNullOrEmpty(request.DefaultStyleJson))
            {
                template.DefaultStyleJson = request.DefaultStyleJson;
            }

            _context.Templates.Add(template);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created template {TemplateId}: {TemplateName}",
                template.Id, template.Name);

            return template;
        }

        /// <summary>
        /// Updates an existing template
        /// </summary>
        public async Task<Template> UpdateTemplateAsync(Guid templateId, UpdateTemplateRequest request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Updating template {TemplateId}", templateId);

            var template = await GetTemplateAsync(templateId, cancellationToken);

            // Update properties
            if (!string.IsNullOrEmpty(request.Name))
                template.Name = request.Name;

            if (!string.IsNullOrEmpty(request.Description))
                template.Description = request.Description;

            if (!string.IsNullOrEmpty(request.Category))
                template.Category = request.Category;

            if (request.IsPublic.HasValue)
                template.IsPublic = request.IsPublic.Value;

            if (request.IsActive.HasValue)
                template.IsActive = request.IsActive.Value;

            if (request.EstimatedCompletionTimeMinutes.HasValue)
                template.EstimatedCompletionTimeMinutes = request.EstimatedCompletionTimeMinutes.Value;

            // Update tags if provided
            if (request.Tags?.Any() == true)
            {
                template.SetTags(request.Tags);
            }

            // Update default styling if provided
            if (!string.IsNullOrEmpty(request.DefaultStyleJson))
            {
                template.DefaultStyleJson = request.DefaultStyleJson;
            }

            // Update thumbnail URL if provided
            if (!string.IsNullOrEmpty(request.ThumbnailUrl))
            {
                template.ThumbnailUrl = request.ThumbnailUrl;
            }

            // LastModificationTime will be set by EF Core interceptors
            // Increment version
            var currentVersion = System.Version.Parse(template.Version);
            template.Version = $"{currentVersion.Major}.{currentVersion.Minor}.{currentVersion.Build + 1}";

            _context.Templates.Update(template);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully updated template {TemplateId}", templateId);

            return template;
        }

        /// <summary>
        /// Deletes a template
        /// </summary>
        public async Task DeleteTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Deleting template {TemplateId}", templateId);

            var template = await GetTemplateAsync(templateId, cancellationToken);

            // Check if template is being used by any reports
            var reportsUsingTemplate = await _context.Reports
                .Where(r => r.TemplateId == templateId)
                .CountAsync(cancellationToken);

            if (reportsUsingTemplate > 0)
            {
                throw new InvalidOperationException(
                    $"Cannot delete template {templateId} because it is being used by {reportsUsingTemplate} report(s)");
            }

            _context.Templates.Remove(template);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully deleted template {TemplateId}", templateId);
        }

        /// <summary>
        /// Gets templates by category with pagination
        /// </summary>
        public async Task<(List<Template> Templates, int TotalCount)> GetTemplatesByCategoryAsync(
            string category,
            int page = 1,
            int pageSize = 20,
            CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting templates for category: {Category}, page {Page}, size {PageSize}",
                category, page, pageSize);

            var query = _context.Templates
                .Where(t => t.IsActive && t.Category == category);

            var totalCount = await query.CountAsync(cancellationToken);

            var templates = await query
                .OrderBy(t => t.Name)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (templates, totalCount);
        }

        /// <summary>
        /// Searches templates by name or description
        /// </summary>
        public async Task<List<Template>> SearchTemplatesAsync(string searchTerm, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Searching templates with term: {SearchTerm}", searchTerm);

            return await _context.Templates
                .Where(t => t.IsActive &&
                    (t.Name.Contains(searchTerm) ||
                     t.Description.Contains(searchTerm) ||
                     t.Category.Contains(searchTerm)))
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Gets popular templates based on usage count
        /// </summary>
        public async Task<List<Template>> GetPopularTemplatesAsync(int count = 10, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting {Count} popular templates", count);

            return await _context.Templates
                .Where(t => t.IsActive && t.IsPublic)
                .OrderByDescending(t => t.UsageCount)
                .ThenBy(t => t.Name)
                .Take(count)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Increments the usage count for a template when it's cloned
        /// </summary>
        public async Task IncrementUsageCountAsync(Guid templateId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Incrementing usage count for template {TemplateId}", templateId);

            var template = await GetTemplateAsync(templateId, cancellationToken);
            template.IncrementUsage();

            _context.Templates.Update(template);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Successfully incremented usage count for template {TemplateId}", templateId);
        }
    }
}
