using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ReportStorageMetadata entity
    /// </summary>
    public class ReportStorageMetadataConfiguration : IEntityTypeConfiguration<ReportStorageMetadata>
    {
        public void Configure(EntityTypeBuilder<ReportStorageMetadata> builder)
        {
            // Table configuration
            builder.ToTable("ReportStorageMetadata");

            // Primary key
            builder.HasKey(rsm => rsm.Id);

            // Properties
            builder.Property(rsm => rsm.ReportId)
                .IsRequired();

            builder.Property(rsm => rsm.StorageStrategy)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("SQL");


            builder.Property(rsm => rsm.SqlStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.CosmosStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.BlobStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.TotalStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.AccessCount)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.LastAccessDate)
                .IsRequired(false);

            builder.Property(rsm => rsm.PerformanceMetrics)
                .IsRequired(false)
                .HasColumnType("nvarchar(max)");

            builder.Property(rsm => rsm.OptimizationMetadata)
                .IsRequired(false)
                .HasColumnType("nvarchar(max)");

            // Missing properties that were in the entity but not configured
            builder.Property(rsm => rsm.DataStorageType)
                .IsRequired(false)
                .HasMaxLength(100);

            builder.Property(rsm => rsm.ComponentStorageType)
                .IsRequired(false)
                .HasMaxLength(100);

            builder.Property(rsm => rsm.DataDocumentId)
                .IsRequired(false)
                .HasMaxLength(255);

            builder.Property(rsm => rsm.LastSyncedAt)
                .IsRequired(false);

            // Indexes
            builder.HasIndex(rsm => rsm.ReportId)
                .IsUnique()
                .HasDatabaseName("IX_ReportStorageMetadata_ReportId");

            builder.HasIndex(rsm => rsm.StorageStrategy)
                .HasDatabaseName("IX_ReportStorageMetadata_StorageStrategy");


            builder.HasIndex(rsm => rsm.TotalStorageSize)
                .HasDatabaseName("IX_ReportStorageMetadata_TotalStorageSize");

            builder.HasIndex(rsm => rsm.LastAccessDate)
                .HasDatabaseName("IX_ReportStorageMetadata_LastAccessDate");

            // Relationships
            builder.HasOne(rsm => rsm.Report)
                .WithOne() // No navigation property on Report side yet
                .HasForeignKey<ReportStorageMetadata>(rsm => rsm.ReportId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
