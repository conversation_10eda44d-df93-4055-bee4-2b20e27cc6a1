using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Common.Interfaces;
using System.Net;
using MultiStorageReportData = FY.WB.CSHero2.Application.Models.MultiStorage.ReportData;
using MultiStorageReportSection = FY.WB.CSHero2.Application.Models.MultiStorage.ReportSection;
using MultiStorageReportSectionField = FY.WB.CSHero2.Application.Models.MultiStorage.ReportSectionField;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// Repository for managing report data in Azure Cosmos DB
    /// Implements CRUD operations for report documents, sections, and fields
    /// </summary>
    public class ReportDataRepository : IReportDataRepository
    {
        private readonly CosmosClient _cosmosClient;
        private readonly Container _container;
        private readonly ILogger<ReportDataRepository> _logger;
        private readonly string _databaseName;
        private readonly string _containerName;

        public ReportDataRepository(
            CosmosClient cosmosClient,
            IConfiguration configuration,
            ILogger<ReportDataRepository> logger)
        {
            _cosmosClient = cosmosClient;
            _logger = logger;
            _databaseName = configuration["CosmosDb:DatabaseName"] ?? "CSHeroReports";
            _containerName = configuration["CosmosDb:ContainerName"] ?? "Reports";
            _container = _cosmosClient.GetContainer(_databaseName, _containerName);
        }

        #region Document-level operations

        public async Task<MultiStorageReportData?> GetReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Retrieving report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);

                var response = await _container.ReadItemAsync<MultiStorageReportData>(
                    documentId,
                    new PartitionKey(documentId),
                    cancellationToken: cancellationToken);

                var reportData = response.Resource;
                
                // Verify tenant access
                if (reportData.TenantId != tenantId)
                {
                    _logger.LogWarning("Tenant {TenantId} attempted to access report data {DocumentId} belonging to tenant {OwnerTenantId}",
                        tenantId, documentId, reportData.TenantId);
                    return null;
                }

                _logger.LogDebug("Successfully retrieved report data document {DocumentId}", documentId);
                return reportData;
            }
            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Report data document {DocumentId} not found for tenant {TenantId}", documentId, tenantId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                throw;
            }
        }

        public async Task<string> CreateReportDataAsync(MultiStorageReportData data, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report data document for report {ReportId}, version {VersionId}", 
                    data.ReportId, data.VersionId);

                // Ensure metadata is set
                data.Metadata.CreatedAt = DateTime.UtcNow;
                data.Metadata.LastModifiedAt = DateTime.UtcNow;

                var response = await _container.CreateItemAsync(
                    data,
                    new PartitionKey(data.Id),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully created report data document {DocumentId} for report {ReportId}", 
                    data.Id, data.ReportId);
                
                return data.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report data document for report {ReportId}, version {VersionId}", 
                    data.ReportId, data.VersionId);
                throw;
            }
        }

        public async Task<string> UpdateReportDataAsync(MultiStorageReportData data, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report data document {DocumentId}", data.Id);

                // Update metadata
                data.Metadata.LastModifiedAt = DateTime.UtcNow;
                data.Metadata.Version++;

                var response = await _container.UpsertItemAsync(
                    data,
                    new PartitionKey(data.Id),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully updated report data document {DocumentId}", data.Id);
                return data.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report data document {DocumentId}", data.Id);
                throw;
            }
        }

        public async Task DeleteReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);

                // First verify the document belongs to the tenant
                var existingData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (existingData == null)
                {
                    _logger.LogWarning("Cannot delete report data document {DocumentId} - not found or access denied for tenant {TenantId}", 
                        documentId, tenantId);
                    return;
                }

                await _container.DeleteItemAsync<MultiStorageReportData>(
                    documentId,
                    new PartitionKey(documentId),
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Successfully deleted report data document {DocumentId}", documentId);
            }
            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Report data document {DocumentId} not found for deletion", documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report data document {DocumentId}", documentId);
                throw;
            }
        }

        #endregion

        #region Query operations

        public async Task<IEnumerable<MultiStorageReportData>> GetReportDataByReportIdAsync(string reportId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Querying report data for report {ReportId} and tenant {TenantId}", reportId, tenantId);

                var query = new QueryDefinition(
                    "SELECT * FROM c WHERE c.reportId = @reportId AND c.tenantId = @tenantId")
                    .WithParameter("@reportId", reportId)
                    .WithParameter("@tenantId", tenantId);

                var results = new List<MultiStorageReportData>();
                using var iterator = _container.GetItemQueryIterator<MultiStorageReportData>(query);

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }

                _logger.LogDebug("Found {Count} report data documents for report {ReportId}", results.Count, reportId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying report data for report {ReportId} and tenant {TenantId}", reportId, tenantId);
                throw;
            }
        }

        public async Task<IEnumerable<MultiStorageReportData>> GetReportDataByTenantAsync(string tenantId, int skip = 0, int take = 100, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Querying report data for tenant {TenantId} (skip: {Skip}, take: {Take})", tenantId, skip, take);

                var query = new QueryDefinition(
                    "SELECT * FROM c WHERE c.tenantId = @tenantId ORDER BY c.metadata.lastModifiedAt DESC OFFSET @skip LIMIT @take")
                    .WithParameter("@tenantId", tenantId)
                    .WithParameter("@skip", skip)
                    .WithParameter("@take", take);

                var results = new List<MultiStorageReportData>();
                using var iterator = _container.GetItemQueryIterator<MultiStorageReportData>(query);

                while (iterator.HasMoreResults)
                {
                    var response = await iterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }

                _logger.LogDebug("Found {Count} report data documents for tenant {TenantId}", results.Count, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying report data for tenant {TenantId}", tenantId);
                throw;
            }
        }

        #endregion

        #region Section operations

        public async Task<MultiStorageReportSection?> GetReportSectionAsync(string documentId, string sectionId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    return null;
                }

                return reportData.Sections.FirstOrDefault(s => s.Id == sectionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving section {SectionId} from document {DocumentId}", sectionId, documentId);
                throw;
            }
        }

        public async Task UpdateReportSectionAsync(string documentId, MultiStorageReportSection section, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    throw new InvalidOperationException($"Report data document {documentId} not found");
                }

                var existingSection = reportData.Sections.FirstOrDefault(s => s.Id == section.Id);
                if (existingSection != null)
                {
                    var index = reportData.Sections.IndexOf(existingSection);
                    reportData.Sections[index] = section;
                }
                else
                {
                    reportData.Sections.Add(section);
                }

                await UpdateReportDataAsync(reportData, cancellationToken);
                _logger.LogDebug("Updated section {SectionId} in document {DocumentId}", section.Id, documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating section {SectionId} in document {DocumentId}", section.Id, documentId);
                throw;
            }
        }

        #endregion

        #region Field operations

        public async Task<MultiStorageReportSectionField?> GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var section = await GetReportSectionAsync(documentId, sectionId, tenantId, cancellationToken);
                if (section == null)
                {
                    return null;
                }

                return section.Fields.FirstOrDefault(f => f.Id == fieldId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving field {FieldId} from section {SectionId} in document {DocumentId}", 
                    fieldId, sectionId, documentId);
                throw;
            }
        }

        public async Task UpdateReportSectionFieldAsync(string documentId, string sectionId, MultiStorageReportSectionField field, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var reportData = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (reportData == null)
                {
                    throw new InvalidOperationException($"Report data document {documentId} not found");
                }

                var section = reportData.Sections.FirstOrDefault(s => s.Id == sectionId);
                if (section == null)
                {
                    throw new InvalidOperationException($"Section {sectionId} not found in document {documentId}");
                }

                var existingField = section.Fields.FirstOrDefault(f => f.Id == field.Id);
                if (existingField != null)
                {
                    var index = section.Fields.IndexOf(existingField);
                    section.Fields[index] = field;
                }
                else
                {
                    section.Fields.Add(field);
                }

                await UpdateReportDataAsync(reportData, cancellationToken);
                _logger.LogDebug("Updated field {FieldId} in section {SectionId} of document {DocumentId}", 
                    field.Id, sectionId, documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating field {FieldId} in section {SectionId} of document {DocumentId}", 
                    field.Id, sectionId, documentId);
                throw;
            }
        }

        #endregion

        #region Utility operations

        public async Task<bool> DocumentExistsAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var data = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                return data != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if document {DocumentId} exists for tenant {TenantId}", documentId, tenantId);
                return false;
            }
        }

        public async Task<long> GetDocumentSizeAsync(string documentId, string tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                var data = await GetReportDataAsync(documentId, tenantId, cancellationToken);
                if (data == null)
                {
                    return 0;
                }

                // Estimate size based on JSON serialization
                var json = System.Text.Json.JsonSerializer.Serialize(data);
                return System.Text.Encoding.UTF8.GetByteCount(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating size for document {DocumentId}", documentId);
                return 0;
            }
        }

        #endregion
    }
}