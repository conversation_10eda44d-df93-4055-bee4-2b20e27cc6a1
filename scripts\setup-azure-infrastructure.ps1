# Azure Infrastructure Setup Script for Phase 3 Multi-Storage Architecture
# This script sets up Azure Cosmos DB and Blob Storage resources
#
# Features:
# - Supports both serverless and provisioned Cosmos DB accounts
# - Automatically detects existing account type and skips throughput settings for serverless
# - Can create new serverless accounts with -UseServerless parameter
# - Handles throughput configuration gracefully for both account types
#
# Usage Examples:
# .\setup-azure-infrastructure.ps1 -ResourceGroupName "my-rg" -Location "East US"
# .\setup-azure-infrastructure.ps1 -ResourceGroupName "my-rg" -Location "East US" -UseServerless
# .\setup-azure-infrastructure.ps1 -ResourceGroupName "my-rg" -Location "East US" -CosmosAccountName "my-cosmos" -UseServerless

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$Location,
    
    [Parameter(Mandatory=$false)]
    [string]$CosmosAccountName = "cshero-cosmosdb",
    
    [Parameter(Mandatory=$false)]
    [string]$StorageAccountName = "csheroblobstorage",
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseName = "ReportRenderingEngine",
    
    [Parameter(Mandatory=$false)]
    [string]$ContainerName = "Reports",
    
    [Parameter(Mandatory=$false)]
    [switch]$UseServerless = $false
)

Write-Host "Setting up Azure infrastructure for Phase 3 Multi-Storage Architecture..." -ForegroundColor Green

# Check if Azure CLI is installed
if (!(Get-Command "az" -ErrorAction SilentlyContinue)) {
    Write-Error "Azure CLI is not installed. Please install Azure CLI first."
    exit 1
}

# Login check
$loginStatus = az account show --query "user.name" -o tsv 2>$null
if (!$loginStatus) {
    Write-Host "Please login to Azure CLI first..." -ForegroundColor Yellow
    az login
}

Write-Host "Current Azure subscription:" -ForegroundColor Cyan
az account show --query "{name:name, id:id}" -o table

# Create or verify resource group
Write-Host "`nCreating/verifying resource group: $ResourceGroupName" -ForegroundColor Yellow
az group create --name $ResourceGroupName --location $Location

# Create Azure Cosmos DB account (if it doesn't exist)
Write-Host "`nCreating Azure Cosmos DB account: $CosmosAccountName" -ForegroundColor Yellow
$cosmosExists = az cosmosdb show --name $CosmosAccountName --resource-group $ResourceGroupName --query "name" -o tsv 2>$null

if (!$cosmosExists) {
    if ($UseServerless) {
        Write-Host "Creating new serverless Cosmos DB account..." -ForegroundColor Cyan
        az cosmosdb create `
            --name $CosmosAccountName `
            --resource-group $ResourceGroupName `
            --locations regionName=$Location `
            --default-consistency-level "Session" `
            --enable-automatic-failover false `
            --enable-multiple-write-locations false `
            --capabilities EnableServerless
    } else {
        Write-Host "Creating new provisioned Cosmos DB account..." -ForegroundColor Cyan
        az cosmosdb create `
            --name $CosmosAccountName `
            --resource-group $ResourceGroupName `
            --locations regionName=$Location `
            --default-consistency-level "Session" `
            --enable-automatic-failover false `
            --enable-multiple-write-locations false
    }
} else {
    Write-Host "Cosmos DB account already exists: $CosmosAccountName" -ForegroundColor Green
}

# Check if Cosmos DB account is serverless
Write-Host "`nChecking Cosmos DB account configuration..." -ForegroundColor Yellow
try {
    $cosmosCapabilities = az cosmosdb show --name $CosmosAccountName --resource-group $ResourceGroupName --query "capabilities[?name=='EnableServerless'].name" -o tsv 2>$null
    $isServerless = ($cosmosCapabilities -eq "EnableServerless") -or $UseServerless
} catch {
    Write-Warning "Could not determine Cosmos DB account type. Assuming provisioned account unless -UseServerless was specified."
    $isServerless = $UseServerless
}

if ($isServerless) {
    Write-Host "Detected serverless Cosmos DB account - throughput settings will be skipped" -ForegroundColor Cyan
} else {
    Write-Host "Detected provisioned Cosmos DB account - throughput settings will be applied" -ForegroundColor Cyan
}

# Create Cosmos DB database
Write-Host "`nCreating Cosmos DB database: $DatabaseName" -ForegroundColor Yellow
if ($isServerless) {
    # Serverless accounts don't support throughput settings
    az cosmosdb sql database create `
        --account-name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --name $DatabaseName
} else {
    # Provisioned accounts require throughput settings
    az cosmosdb sql database create `
        --account-name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --name $DatabaseName `
        --throughput 400
}

# Create Cosmos DB container with indexing policy
Write-Host "`nCreating Cosmos DB container: $ContainerName" -ForegroundColor Yellow
$indexingPolicyPath = Join-Path $PSScriptRoot "setup-cosmos-container.json"

if (Test-Path $indexingPolicyPath) {
    if ($isServerless) {
        # Serverless accounts don't support throughput settings
        az cosmosdb sql container create `
            --account-name $CosmosAccountName `
            --database-name $DatabaseName `
            --resource-group $ResourceGroupName `
            --name $ContainerName `
            --partition-key-path "/TenantId" `
            --idx @$indexingPolicyPath
    } else {
        # Provisioned accounts require throughput settings
        az cosmosdb sql container create `
            --account-name $CosmosAccountName `
            --database-name $DatabaseName `
            --resource-group $ResourceGroupName `
            --name $ContainerName `
            --partition-key-path "/TenantId" `
            --throughput 400 `
            --idx @$indexingPolicyPath
    }
} else {
    Write-Warning "Indexing policy file not found at $indexingPolicyPath. Creating container with default indexing."
    if ($isServerless) {
        # Serverless accounts don't support throughput settings
        az cosmosdb sql container create `
            --account-name $CosmosAccountName `
            --database-name $DatabaseName `
            --resource-group $ResourceGroupName `
            --name $ContainerName `
            --partition-key-path "/TenantId"
    } else {
        # Provisioned accounts require throughput settings
        az cosmosdb sql container create `
            --account-name $CosmosAccountName `
            --database-name $DatabaseName `
            --resource-group $ResourceGroupName `
            --name $ContainerName `
            --partition-key-path "/TenantId" `
            --throughput 400
    }
}

# Create Azure Storage Account
Write-Host "`nCreating Azure Storage Account: $StorageAccountName" -ForegroundColor Yellow
$storageExists = az storage account show --name $StorageAccountName --resource-group $ResourceGroupName --query "name" -o tsv 2>$null

if (!$storageExists) {
    Write-Host "Creating new Storage Account..." -ForegroundColor Cyan
    az storage account create `
        --name $StorageAccountName `
        --resource-group $ResourceGroupName `
        --location $Location `
        --sku Standard_LRS `
        --kind StorageV2 `
        --access-tier Hot `
        --allow-blob-public-access false
} else {
    Write-Host "Storage Account already exists: $StorageAccountName" -ForegroundColor Green
}

# Create blob containers
Write-Host "`nCreating blob containers..." -ForegroundColor Yellow

# Get storage account key
$storageKey = az storage account keys list --resource-group $ResourceGroupName --account-name $StorageAccountName --query "[0].value" -o tsv

# Create report-components container
az storage container create `
    --name "report-components" `
    --account-name $StorageAccountName `
    --account-key $storageKey `
    --public-access off

# Create report-data container (for backup/export purposes)
az storage container create `
    --name "report-data" `
    --account-name $StorageAccountName `
    --account-key $storageKey `
    --public-access off

# Get connection strings
Write-Host "`nRetrieving connection strings..." -ForegroundColor Yellow

$cosmosConnectionString = az cosmosdb keys list --name $CosmosAccountName --resource-group $ResourceGroupName --type connection-strings --query "connectionStrings[0].connectionString" -o tsv
$blobConnectionString = az storage account show-connection-string --name $StorageAccountName --resource-group $ResourceGroupName --query "connectionString" -o tsv

# Output configuration for appsettings.json
Write-Host "`n" -ForegroundColor Green
Write-Host "=== CONFIGURATION FOR APPSETTINGS.JSON ===" -ForegroundColor Green
Write-Host "`n{" -ForegroundColor White
Write-Host "  `"CosmosDb`": {" -ForegroundColor White
Write-Host "    `"ConnectionString`": `"$cosmosConnectionString`"," -ForegroundColor White
Write-Host "    `"DatabaseName`": `"$DatabaseName`"," -ForegroundColor White
Write-Host "    `"ContainerName`": `"$ContainerName`"," -ForegroundColor White
Write-Host "    `"MaxRetryAttempts`": 3," -ForegroundColor White
Write-Host "    `"RequestTimeoutSeconds`": 30," -ForegroundColor White
Write-Host "    `"MaxConnections`": 50" -ForegroundColor White
Write-Host "  }," -ForegroundColor White
Write-Host "  `"BlobStorage`": {" -ForegroundColor White
Write-Host "    `"ConnectionString`": `"$blobConnectionString`"," -ForegroundColor White
Write-Host "    `"ContainerName`": `"report-components`"," -ForegroundColor White
Write-Host "    `"ReportDataContainer`": `"report-data`"," -ForegroundColor White
Write-Host "    `"MaxRetryAttempts`": 3," -ForegroundColor White
Write-Host "    `"RequestTimeoutSeconds`": 30," -ForegroundColor White
Write-Host "    `"MaxConcurrentOperations`": 10," -ForegroundColor White
Write-Host "    `"EnableEncryption`": true," -ForegroundColor White
Write-Host "    `"DefaultContentType`": `"application/json`"" -ForegroundColor White
Write-Host "  }" -ForegroundColor White
Write-Host "}" -ForegroundColor White

Write-Host "`n=== SETUP COMPLETE ===" -ForegroundColor Green
Write-Host "Azure infrastructure has been successfully set up!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Copy the configuration above to your appsettings.json file" -ForegroundColor White
Write-Host "2. Update your application configuration" -ForegroundColor White
Write-Host "3. Test the multi-storage repositories" -ForegroundColor White
Write-Host "4. Run data migration if needed" -ForegroundColor White

# Save configuration to file
$configPath = Join-Path $PSScriptRoot "azure-config.json"
$config = @{
    CosmosDb = @{
        ConnectionString = $cosmosConnectionString
        DatabaseName = $DatabaseName
        ContainerName = $ContainerName
        MaxRetryAttempts = 3
        RequestTimeoutSeconds = 30
        MaxConnections = 50
    }
    BlobStorage = @{
        ConnectionString = $blobConnectionString
        ContainerName = "report-components"
        ReportDataContainer = "report-data"
        MaxRetryAttempts = 3
        RequestTimeoutSeconds = 30
        MaxConcurrentOperations = 10
        EnableEncryption = $true
        DefaultContentType = "application/json"
    }
}

$config | ConvertTo-Json -Depth 3 | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "`nConfiguration also saved to: $configPath" -ForegroundColor Cyan
