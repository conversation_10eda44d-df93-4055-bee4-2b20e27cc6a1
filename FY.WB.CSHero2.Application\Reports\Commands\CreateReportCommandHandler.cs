using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore; // Required for FirstOrDefaultAsync and AnyAsync
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FY.WB.CSHero2.Application.Reports.Commands
{
    public class CreateReportCommandHandler : IRequestHandler<CreateReportCommand, Guid>
    {
        private readonly IApplicationDbContext _context;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CreateReportCommandHandler> _logger;

        public CreateReportCommandHandler(
            IApplicationDbContext context,
            ICurrentUserService currentUserService,
            ILogger<CreateReportCommandHandler> logger)
        {
            _context = context;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateReportCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating report with data: {RequestData}", JsonSerializer.Serialize(request.Dto));

            if (!_currentUserService.TenantId.HasValue)
            {
                throw new InvalidOperationException("TenantId is required to create a Report.");
            }

            // Use transaction from the underlying DbContext if available
            var efContext = _context as Microsoft.EntityFrameworkCore.DbContext;
            if (efContext == null)
                throw new InvalidOperationException("Underlying context does not support transactions.");

            var strategy = efContext.Database.CreateExecutionStrategy();
            return await strategy.ExecuteAsync(async () =>
            {
                using var transaction = await efContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Step 1: Create and save Report
                    var reportName = !string.IsNullOrEmpty(request.Dto.ReportName) ? request.Dto.ReportName : request.Dto.Name;
                    Guid? clientId = request.Dto.ClientId;
                    var status = !string.IsNullOrEmpty(request.Dto.Status) ? request.Dto.Status : "Draft";
                    var author = !string.IsNullOrEmpty(request.Dto.Author) ? request.Dto.Author : "System";

                    // Validate ClientId if provided
                    if (clientId.HasValue && clientId.Value != Guid.Empty)
                    {
                        if (!await _context.Clients.AnyAsync(c => c.Id == clientId.Value && c.TenantId == _currentUserService.TenantId, cancellationToken))
                        {
                            _logger.LogWarning("Client with ID {ClientId} not found for tenant {TenantId}", clientId, _currentUserService.TenantId);
                            clientId = null;
                        }
                    }
                    else
                    {
                        clientId = null;
                    }

                    var entity = new Report
                    {
                        ReportNumber = !string.IsNullOrEmpty(request.Dto.ReportNumber) ? request.Dto.ReportNumber : $"RPT-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString()[..8]}",
                        ClientId = clientId,
                        ClientName = request.Dto.ClientName ?? string.Empty,
                        Name = reportName,
                        Category = request.Dto.Category ?? "General",
                        SlideCount = request.Dto.SlideCount,
                        Status = status,
                        Author = author,
                        TenantId = _currentUserService.TenantId.Value
                    };

                    _context.Reports.Add(entity);
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Created report {ReportId} with name '{ReportName}'", entity.Id, entity.Name);

                    // Step 2: Create and save ReportSections
                    List<ReportSection> createdSections = new();
                    if (request.Dto.Content?.Sections?.Count > 0)
                    {
                        foreach (var sectionDto in request.Dto.Content.Sections)
                        {
                            var sectionTitle = !string.IsNullOrEmpty(sectionDto.Title) ? sectionDto.Title : sectionDto.SectionTitle;
                            var sectionId = Guid.NewGuid();
                            var sectionType = sectionDto.Type ?? "text";
                            var sectionOrder = sectionDto.Order > 0 ? sectionDto.Order : createdSections.Count;

                            var section = new ReportSection(
                                sectionId,
                                entity.Id,
                                sectionTitle ?? $"Section {createdSections.Count + 1}",
                                sectionType,
                                sectionOrder)
                            {
                                Content = sectionDto.Content != null ? JsonSerializer.Serialize(sectionDto.Content) : string.Empty,
                                TenantId = _currentUserService.TenantId.Value
                            };
                            createdSections.Add(section);
                        }
                        _context.ReportSections.AddRange(createdSections);
                        await _context.SaveChangesAsync(cancellationToken);
                        _logger.LogInformation("Created {SectionCount} sections for report {ReportId}", createdSections.Count, entity.Id);
                    }

                    // Step 3: Create and save ReportVersion (always Version=1, IsCurrent=true)
                    var version = new ReportVersion
                    {
                        ReportId = entity.Id,
                        VersionNumber = 1,
                        Description = "Initial version",
                        IsCurrent = true,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.ReportVersions.Add(version);

                    // Step 4: Conditionally create ReportStorageMetadata and ReportStyle
                    if (request.Dto.Content != null && request.Dto.Content.Template != null)
                    {
                        var metadata = new ReportStorageMetadata(
                            Guid.NewGuid(),
                            entity.Id,
                            "SQL"
                        );
                        _context.ReportStorageMetadata.Add(metadata);
                    }
                    if (request.Dto.Style != null)
                    {
                        var style = new ReportStyle
                        {
                            ReportId = entity.Id,
                            Theme = "modern", // or parse from request.Dto.Style if needed
                            ColorScheme = "blue"
                        };
                        _context.ReportStyles.Add(style);
                    }
                    await _context.SaveChangesAsync(cancellationToken);

                    // Step 5: Create and save ReportSectionFields (after sections exist)
                    if (createdSections.Count > 0)
                    {
                        var sectionIdMap = createdSections
                            .Select((section, idx) => new { section, idx })
                            .ToDictionary(x => request.Dto.Content.Sections[x.idx], x => x.section.Id);

                        List<ReportSectionField> allFields = new();
                        for (int i = 0; i < request.Dto.Content.Sections.Count; i++)
                        {
                            var sectionDto = request.Dto.Content.Sections[i];
                            var sectionId = createdSections[i].Id;
                            if (sectionDto.Fields?.Count > 0)
                            {
                                for (int j = 0; j < sectionDto.Fields.Count; j++)
                                {
                                    var fieldDto = sectionDto.Fields[j];
                                    var fieldId = Guid.NewGuid();
                                    var fieldName = fieldDto.Name ?? $"field_{j}";
                                    var fieldType = fieldDto.Type ?? "text";
                                    var fieldContent = fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty;
                                    var fieldOrder = fieldDto.Order > 0 ? fieldDto.Order : j;

                                    var field = new ReportSectionField(
                                        fieldId,
                                        sectionId,
                                        fieldName,
                                        fieldType,
                                        fieldContent,
                                        fieldOrder)
                                    {
                                        Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {j + 1}",
                                        Value = fieldDto.Value != null ? JsonSerializer.Serialize(fieldDto.Value) : string.Empty,
                                        IsRequired = fieldDto.IsRequired,
                                        Metadata = fieldDto.Metadata != null ? JsonSerializer.Serialize(fieldDto.Metadata) : string.Empty,
                                        TenantId = _currentUserService.TenantId.Value
                                    };
                                    allFields.Add(field);
                                }
                            }
                        }
                        if (allFields.Count > 0)
                        {
                            _context.ReportSectionFields.AddRange(allFields);
                            await _context.SaveChangesAsync(cancellationToken);
                            _logger.LogInformation("Created {FieldCount} fields for report {ReportId}", allFields.Count, entity.Id);
                        }
                    }

                    // Step 6: Update Report with CurrentVersionId and LastSavedAt
                    entity.CurrentVersionId = version.Id;
                    entity.LastSavedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync(cancellationToken);

                    await transaction.CommitAsync(cancellationToken);
                    return entity.Id;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync(cancellationToken);
                    _logger.LogError(ex, "Failed to create report and related entities. Transaction rolled back.");
                    throw;
                }
            });
        }

        private async Task CreateReportSections(Guid reportId, List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionDto> sectionDtos, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating {SectionCount} sections for report {ReportId}", sectionDtos.Count, reportId);

            var sectionsCreated = 0;
            var fieldsCreated = 0;

            for (int i = 0; i < sectionDtos.Count; i++)
            {
                var sectionDto = sectionDtos[i];
                var sectionTitle = !string.IsNullOrEmpty(sectionDto.Title) ? sectionDto.Title : sectionDto.SectionTitle;

                // Generate a new GUID for the section before adding it to the context
                var sectionId = Guid.NewGuid();
                var sectionType = sectionDto.Type ?? "text";
                var sectionOrder = sectionDto.Order > 0 ? sectionDto.Order : i;

                var section = new ReportSection(
                    sectionId,
                    reportId,
                    sectionTitle ?? $"Section {i + 1}",
                    sectionType,
                    sectionOrder)
                {
                    Content = sectionDto.Content != null ? JsonSerializer.Serialize(sectionDto.Content) : string.Empty,
                    TenantId = _currentUserService.TenantId.Value
                };

                _context.ReportSections.Add(section);
                sectionsCreated++;

                _logger.LogDebug("Added section {SectionTitle} (ID: {SectionId}, Type: {SectionType}, Order: {SectionOrder}) to report {ReportId}",
                    section.Title, sectionId, sectionType, sectionOrder, reportId);

                // Create fields for this section using the pre-assigned section ID
                if (sectionDto.Fields?.Count > 0)
                {
                    await CreateSectionFields(sectionId, sectionDto.Fields, cancellationToken);
                    fieldsCreated += sectionDto.Fields.Count;
                }
            }

            // Save all sections and fields together in a single transaction
            try
            {
                var changesSaved = await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Successfully created {SectionsCreated} sections and {FieldsCreated} fields for report {ReportId}. Database changes saved: {ChangesSaved}",
                    sectionsCreated, fieldsCreated, reportId, changesSaved);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save sections and fields for report {ReportId}. Sections prepared: {SectionsCreated}, Fields prepared: {FieldsCreated}. Error: {ErrorMessage}",
                    reportId, sectionsCreated, fieldsCreated, ex.Message);
                throw;
            }
        }

        private async Task CreateSectionFields(Guid sectionId, List<FY.WB.CSHero2.Application.Reports.Dtos.ReportSectionFieldDto> fieldDtos, CancellationToken cancellationToken)
        {
            if (fieldDtos == null || fieldDtos.Count == 0)
            {
                _logger.LogDebug("No fields to create for section {SectionId}", sectionId);
                return;
            }

            _logger.LogInformation("Creating {FieldCount} fields for section {SectionId}", fieldDtos.Count, sectionId);

            // Note: We don't validate section existence here because the section was just added to the context
            // in the same transaction and hasn't been saved yet. The foreign key constraint will ensure
            // referential integrity when SaveChangesAsync is called.

            for (int i = 0; i < fieldDtos.Count; i++)
            {
                var fieldDto = fieldDtos[i];

                var fieldId = Guid.NewGuid();
                var fieldName = fieldDto.Name ?? $"field_{i}";
                var fieldType = fieldDto.Type ?? "text";
                var fieldContent = fieldDto.Content != null ? JsonSerializer.Serialize(fieldDto.Content) : string.Empty;
                var fieldOrder = fieldDto.Order > 0 ? fieldDto.Order : i;

                var field = new ReportSectionField(
                    fieldId,
                    sectionId,
                    fieldName,
                    fieldType,
                    fieldContent,
                    fieldOrder)
                {
                    Label = fieldDto.Label ?? fieldDto.Name ?? $"Field {i + 1}",
                    Value = fieldDto.Value != null ? JsonSerializer.Serialize(fieldDto.Value) : string.Empty,
                    IsRequired = fieldDto.IsRequired,
                    Metadata = fieldDto.Metadata != null ? JsonSerializer.Serialize(fieldDto.Metadata) : string.Empty,
                    TenantId = _currentUserService.TenantId.Value
                };

                _context.ReportSectionFields.Add(field);

                _logger.LogDebug("Added field {FieldName} (Type: {FieldType}, Order: {FieldOrder}) to section {SectionId}",
                    fieldName, fieldType, fieldOrder, sectionId);
            }

            _logger.LogInformation("Prepared {FieldCount} fields for section {SectionId}", fieldDtos.Count, sectionId);
        }
    }
}
