using FY.WB.CSHero2.Domain.Entities;
using MultiStorageReportData = FY.WB.CSHero2.Application.Models.MultiStorage.ReportData;
using MultiStorageReportSection = FY.WB.CSHero2.Application.Models.MultiStorage.ReportSection;
using MultiStorageReportSectionField = FY.WB.CSHero2.Application.Models.MultiStorage.ReportSectionField;
using MultiStorageReportComponent = FY.WB.CSHero2.Application.Models.MultiStorage.ReportComponent;
using MultiStorageComponentsMetadata = FY.WB.CSHero2.Application.Models.MultiStorage.ComponentsMetadata;
using MultiStorageComponentStorageResult = FY.WB.CSHero2.Application.Models.MultiStorage.ComponentStorageResult;

namespace FY.WB.CSHero2.Application.Common.Interfaces
{
    /// <summary>
    /// Repository for SQL database operations (metadata and style selections)
    /// </summary>
    public interface IReportMetadataRepository
    {
        // Report operations
        Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task<Report> CreateReportAsync(Report report, CancellationToken cancellationToken = default);
        Task<Report> UpdateReportAsync(Report report, CancellationToken cancellationToken = default);
        Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task<IEnumerable<Report>> GetReportsAsync(Guid? clientId = null, CancellationToken cancellationToken = default);

        // Report Version operations
        Task<ReportVersion?> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
        Task<ReportVersion?> GetCurrentReportVersionAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task<ReportVersion> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
        Task<ReportVersion> UpdateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
        Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
        Task<IEnumerable<ReportVersion>> GetReportVersionsAsync(Guid reportId, CancellationToken cancellationToken = default);

        // Report Style operations
        Task<ReportStyle?> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task<ReportStyle> CreateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default);
        Task<ReportStyle> UpdateReportStyleAsync(ReportStyle style, CancellationToken cancellationToken = default);
        Task DeleteReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);

        // Bulk operations
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Repository for Cosmos DB operations (report data - sections and fields)
    /// </summary>
    public interface IReportDataRepository
    {
        // Document operations
        Task<MultiStorageReportData?> GetReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default);
        Task<string> CreateReportDataAsync(MultiStorageReportData data, CancellationToken cancellationToken = default);
        Task<string> UpdateReportDataAsync(MultiStorageReportData data, CancellationToken cancellationToken = default);
        Task DeleteReportDataAsync(string documentId, string tenantId, CancellationToken cancellationToken = default);

        // Query operations
        Task<IEnumerable<MultiStorageReportData>> GetReportDataByReportIdAsync(string reportId, string tenantId, CancellationToken cancellationToken = default);
        Task<IEnumerable<MultiStorageReportData>> GetReportDataByTenantAsync(string tenantId, int skip = 0, int take = 100, CancellationToken cancellationToken = default);

        // Section operations
        Task<MultiStorageReportSection?> GetReportSectionAsync(string documentId, string sectionId, string tenantId, CancellationToken cancellationToken = default);
        Task UpdateReportSectionAsync(string documentId, MultiStorageReportSection section, string tenantId, CancellationToken cancellationToken = default);

        // Field operations
        Task<MultiStorageReportSectionField?> GetReportSectionFieldAsync(string documentId, string sectionId, string fieldId, string tenantId, CancellationToken cancellationToken = default);
        Task UpdateReportSectionFieldAsync(string documentId, string sectionId, MultiStorageReportSectionField field, string tenantId, CancellationToken cancellationToken = default);

        // Utility operations
        Task<bool> DocumentExistsAsync(string documentId, string tenantId, CancellationToken cancellationToken = default);
        Task<long> GetDocumentSizeAsync(string documentId, string tenantId, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Repository for Azure Blob Storage operations (rendered components)
    /// </summary>
    public interface IReportComponentsRepository
    {
        // Component storage operations
        Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<MultiStorageReportComponent> components, CancellationToken cancellationToken = default);
        Task<MultiStorageComponentsMetadata?> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
        Task<IEnumerable<MultiStorageReportComponent>> GetComponentsAsync(string blobId, CancellationToken cancellationToken = default);
        Task<MultiStorageReportComponent?> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
        Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);

        // Individual component operations
        Task<string> SaveComponentAsync(Guid reportId, Guid versionId, MultiStorageReportComponent component, CancellationToken cancellationToken = default);
        Task<string> GetComponentCodeAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
        Task UpdateComponentAsync(string blobId, MultiStorageReportComponent component, CancellationToken cancellationToken = default);

        // Utility operations
        Task<bool> ComponentsExistAsync(string blobId, CancellationToken cancellationToken = default);
        Task<long> GetComponentsSizeAsync(string blobId, CancellationToken cancellationToken = default);
        Task<MultiStorageComponentStorageResult> GetStorageResultAsync(string blobId, CancellationToken cancellationToken = default);

        // Bulk operations
        Task<IEnumerable<string>> ListComponentBlobsAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task DeleteAllComponentsForReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    }
}
