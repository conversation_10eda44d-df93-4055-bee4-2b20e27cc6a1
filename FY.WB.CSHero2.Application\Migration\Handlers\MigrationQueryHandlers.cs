using MediatR;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Application.Migration.Queries;
using FY.WB.CSHero2.Application.Models.Migration;
using FY.WB.CSHero2.Application.Services.Migration;

namespace FY.WB.CSHero2.Application.Migration.Handlers
{
    /// <summary>
    /// Handler for getting migration status query
    /// </summary>
    public class GetMigrationStatusQueryHandler : IRequestHandler<GetMigrationStatusQuery, MigrationStatus>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<GetMigrationStatusQueryHandler> _logger;

        public GetMigrationStatusQueryHandler(
            IReportDataMigrationService migrationService,
            ILogger<GetMigrationStatusQueryHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationStatus> Handle(GetMigrationStatusQuery request, CancellationToken cancellationToken)
        {
            _logger.LogDebug("Getting migration status. IncludeDetails: {IncludeDetails}, IncludeHistory: {IncludeHistory}",
                request.IncludeDetails, request.IncludeHistory);

            try
            {
                var result = await _migrationService.GetMigrationStatusAsync(cancellationToken);
                
                _logger.LogDebug("Retrieved migration status. IsRunning: {IsRunning}", result.IsRunning);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status");
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for getting migration statistics query
    /// </summary>
    public class GetMigrationStatisticsQueryHandler : IRequestHandler<GetMigrationStatisticsQuery, MigrationStatistics>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<GetMigrationStatisticsQueryHandler> _logger;

        public GetMigrationStatisticsQueryHandler(
            IReportDataMigrationService migrationService,
            ILogger<GetMigrationStatisticsQueryHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationStatistics> Handle(GetMigrationStatisticsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogDebug("Getting migration statistics. TenantId: {TenantId}, StartDate: {StartDate}, EndDate: {EndDate}",
                request.TenantId, request.StartDate, request.EndDate);

            try
            {
                var result = await _migrationService.GetMigrationStatisticsAsync(cancellationToken);
                
                _logger.LogDebug("Retrieved migration statistics. TotalReports: {TotalReports}, MigratedReports: {MigratedReports}",
                    result.TotalReports, result.MigratedReports);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration statistics");
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for getting migration progress query
    /// </summary>
    public class GetMigrationProgressQueryHandler : IRequestHandler<GetMigrationProgressQuery, MigrationProgress>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<GetMigrationProgressQueryHandler> _logger;

        public GetMigrationProgressQueryHandler(
            IReportDataMigrationService migrationService,
            ILogger<GetMigrationProgressQueryHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<MigrationProgress> Handle(GetMigrationProgressQuery request, CancellationToken cancellationToken)
        {
            _logger.LogDebug("Getting migration progress for operation {OperationId}", request.OperationId);

            try
            {
                var result = await _migrationService.GetMigrationProgressAsync(request.OperationId, cancellationToken);
                
                _logger.LogDebug("Retrieved migration progress for operation {OperationId}. PercentComplete: {PercentComplete}",
                    request.OperationId, result?.PercentComplete);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration progress for operation {OperationId}", request.OperationId);
                throw;
            }
        }
    }

    /// <summary>
    /// Handler for getting reports requiring migration query
    /// </summary>
    public class GetReportsRequiringMigrationQueryHandler : IRequestHandler<GetReportsRequiringMigrationQuery, IEnumerable<ReportMigrationInfo>>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<GetReportsRequiringMigrationQueryHandler> _logger;

        public GetReportsRequiringMigrationQueryHandler(
            IReportDataMigrationService migrationService,
            ILogger<GetReportsRequiringMigrationQueryHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<IEnumerable<ReportMigrationInfo>> Handle(GetReportsRequiringMigrationQuery request, CancellationToken cancellationToken)
        {
            _logger.LogDebug("Getting reports requiring migration. TenantId: {TenantId}, MaxResults: {MaxResults}, Skip: {Skip}, SortOrder: {SortOrder}",
                request.TenantId, request.MaxResults, request.Skip, request.SortOrder);

            try
            {
                var result = await _migrationService.GetReportsRequiringMigrationAsync(request.TenantId, cancellationToken);
                
                // Apply pagination and sorting
                var sortedResult = ApplySorting(result, request.SortOrder);
                var paginatedResult = sortedResult.Skip(request.Skip).Take(request.MaxResults);
                
                _logger.LogDebug("Retrieved {Count} reports requiring migration", paginatedResult.Count());

                return paginatedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports requiring migration");
                throw;
            }
        }

        private static IEnumerable<ReportMigrationInfo> ApplySorting(IEnumerable<ReportMigrationInfo> reports, MigrationSortOrder sortOrder)
        {
            return sortOrder switch
            {
                MigrationSortOrder.Priority => reports.OrderByDescending(r => r.Priority),
                MigrationSortOrder.LastModified => reports.OrderByDescending(r => r.LastModified),
                MigrationSortOrder.ReportName => reports.OrderBy(r => r.ReportName),
                MigrationSortOrder.DataSize => reports.OrderByDescending(r => r.EstimatedDataSize),
                MigrationSortOrder.Complexity => reports.OrderByDescending(r => r.Complexity),
                MigrationSortOrder.TenantId => reports.OrderBy(r => r.TenantId),
                _ => reports.OrderByDescending(r => r.Priority)
            };
        }
    }

    /// <summary>
    /// Handler for getting migration history query
    /// </summary>
    public class GetMigrationHistoryQueryHandler : IRequestHandler<GetMigrationHistoryQuery, IEnumerable<MigrationHistoryEntry>>
    {
        private readonly IReportDataMigrationService _migrationService;
        private readonly ILogger<GetMigrationHistoryQueryHandler> _logger;

        public GetMigrationHistoryQueryHandler(
            IReportDataMigrationService migrationService,
            ILogger<GetMigrationHistoryQueryHandler> logger)
        {
            _migrationService = migrationService;
            _logger = logger;
        }

        public async Task<IEnumerable<MigrationHistoryEntry>> Handle(GetMigrationHistoryQuery request, CancellationToken cancellationToken)
        {
            _logger.LogDebug("Getting migration history for report {ReportId}. MaxResults: {MaxResults}, Skip: {Skip}",
                request.ReportId, request.MaxResults, request.Skip);

            try
            {
                var result = await _migrationService.GetMigrationHistoryAsync(request.ReportId, cancellationToken);
                
                // Apply pagination
                var paginatedResult = result
                    .OrderByDescending(h => h.Timestamp)
                    .Skip(request.Skip)
                    .Take(request.MaxResults);
                
                _logger.LogDebug("Retrieved {Count} migration history entries for report {ReportId}", 
                    paginatedResult.Count(), request.ReportId);

                return paginatedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration history for report {ReportId}", request.ReportId);
                throw;
            }
        }
    }
}