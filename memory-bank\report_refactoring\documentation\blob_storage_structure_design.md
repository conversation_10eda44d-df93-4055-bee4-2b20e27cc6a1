# Blob Storage Structure Design for Multi-Storage Architecture

## Overview

This document outlines the comprehensive design for blob storage structure to support versioned styles, components, and template inheritance in the multi-storage architecture.

## Blob Storage Architecture

### Storage Hierarchy Design

```
/report-components/
├── /tenants/
│   ├── /{tenant-id}/
│   │   ├── /reports/
│   │   │   ├── /{report-id}/
│   │   │   │   ├── /versions/
│   │   │   │   │   ├── /v{version-number}/
│   │   │   │   │   │   ├── components.json
│   │   │   │   │   │   ├── styles.json
│   │   │   │   │   │   └── metadata.json
│   │   │   │   │   └── /v{version-number}/...
│   │   │   │   └── /drafts/
│   │   │   │       ├── /{draft-id}/
│   │   │   │       │   ├── components.json
│   │   │   │       │   ├── styles.json
│   │   │   │       │   └── metadata.json
│   │   │   │       └── /current/
│   │   │   │           ├── components.json
│   │   │   │           ├── styles.json
│   │   │   │           └── metadata.json
│   │   │   └── /{report-id}/...
│   │   └── /templates/
│   │       ├── /{template-id}/
│   │       │   ├── /versions/
│   │       │   │   ├── /v{version-number}/
│   │       │   │   │   ├── components.json
│   │       │   │   │   ├── styles.json
│   │       │   │   │   └── metadata.json
│   │       │   │   └── /v{version-number}/...
│   │       │   └── base-template.json
│   │       └── /{template-id}/...
│   └── /{tenant-id}/...
└── /shared/
    ├── /system-templates/
    │   ├── /{template-id}/
    │   │   ├── components.json
    │   │   ├── styles.json
    │   │   └── metadata.json
    │   └── /{template-id}/...
    └── /default-styles/
        ├── themes/
        │   ├── corporate.json
        │   ├── modern.json
        │   ├── healthcare.json
        │   └── financial.json
        └── components/
            ├── charts/
            ├── tables/
            ├── text/
            └── layouts/
```

## Blob Storage Models

### 1. Component Data Structure

```json
{
  "id": "components-{report-id}-v{version-id}",
  "reportId": "550e8400-e29b-41d4-a716-446655440000",
  "versionId": "v1000000-0000-0000-0000-000000000001",
  "versionNumber": 1,
  "tenantId": "00000000-0000-0000-0003-000000000001",
  "createdAt": "2024-03-07T08:30:00Z",
  "updatedAt": "2024-03-07T08:30:00Z",
  "templateSourceId": "00000000-0000-0000-0005-000000000001",
  "isModifiedFromTemplate": false,
  "components": {
    "layout": {
      "type": "standard",
      "framework": "NextJS",
      "typescript": true,
      "styleFramework": "TailwindCSS"
    },
    "sections": [
      {
        "id": "11111111-1111-1111-1111-111111111111",
        "type": "text",
        "component": {
          "name": "ExecutiveSummarySection",
          "props": {
            "heading": "Executive Summary",
            "content": "This quarterly financial analysis...",
            "className": "mb-6 p-4 bg-white rounded-lg shadow"
          },
          "jsx": "<div className=\"mb-6 p-4 bg-white rounded-lg shadow\">...</div>",
          "dependencies": ["react", "@types/react"]
        },
        "templateSourceId": "template-section-001",
        "isModifiedFromTemplate": false
      },
      {
        "id": "22222222-2222-2222-2222-222222222222",
        "type": "chart",
        "component": {
          "name": "FinancialAnalysisChart",
          "props": {
            "chartType": "bar",
            "data": {
              "labels": ["Q1 2024", "Q2 2024", "Q3 2024", "Q4 2024"],
              "values": [2100000, 2350000, 2600000, 2850000],
              "currency": "USD"
            },
            "className": "mb-6 p-4 bg-white rounded-lg shadow"
          },
          "jsx": "<div className=\"mb-6 p-4 bg-white rounded-lg shadow\">...</div>",
          "dependencies": ["react", "recharts", "@types/react"]
        },
        "templateSourceId": null,
        "isModifiedFromTemplate": true
      }
    ],
    "metadata": {
      "totalComponents": 2,
      "framework": "NextJS",
      "dependencies": ["react", "recharts", "@types/react"],
      "buildVersion": "1.0.0",
      "lastBuilt": "2024-03-07T08:30:00Z"
    }
  }
}
```

### 2. Styles Data Structure

```json
{
  "id": "styles-{report-id}-v{version-id}",
  "reportId": "550e8400-e29b-41d4-a716-446655440000",
  "versionId": "v1000000-0000-0000-0000-000000000001",
  "versionNumber": 1,
  "tenantId": "00000000-0000-0000-0003-000000000001",
  "createdAt": "2024-03-07T08:30:00Z",
  "updatedAt": "2024-03-07T08:30:00Z",
  "templateSourceId": "00000000-0000-0000-0005-000000000001",
  "isModifiedFromTemplate": false,
  "styles": {
    "theme": {
      "name": "Financial Biotech",
      "description": "Professional financial styling for biotech sector",
      "baseTheme": "corporate"
    },
    "layout": {
      "pageSize": "A4",
      "orientation": "portrait",
      "margins": {
        "top": 20,
        "bottom": 20,
        "left": 20,
        "right": 20
      },
      "headerHeight": 60,
      "footerHeight": 40,
      "sectionSpacing": 24,
      "columnGap": 16
    },
    "typography": {
      "primaryFont": "Calibri",
      "secondaryFont": "Arial",
      "headingSize": 16,
      "bodySize": 11,
      "lineHeight": 1.5,
      "fontWeights": {
        "normal": 400,
        "medium": 500,
        "bold": 700
      }
    },
    "colors": {
      "primary": "#2E8B57",
      "secondary": "#4169E1",
      "accent": "#FF4500",
      "background": "#FFFFFF",
      "border": "#708090",
      "text": {
        "primary": "#333333",
        "secondary": "#666666",
        "muted": "#999999"
      }
    },
    "components": {
      "section": {
        "marginBottom": "1.5rem",
        "padding": "1rem",
        "backgroundColor": "white",
        "borderRadius": "0.5rem",
        "boxShadow": "0 1px 3px rgba(0,0,0,0.1)"
      },
      "heading": {
        "fontSize": "1.25rem",
        "fontWeight": "600",
        "marginBottom": "0.75rem",
        "color": "#2E8B57"
      },
      "chart": {
        "height": "300px",
        "backgroundColor": "white",
        "border": "1px solid #e5e7eb"
      },
      "table": {
        "width": "100%",
        "borderCollapse": "collapse",
        "fontSize": "0.875rem"
      }
    },
    "responsive": {
      "breakpoints": {
        "sm": "640px",
        "md": "768px",
        "lg": "1024px",
        "xl": "1280px"
      },
      "rules": {
        "@media (max-width: 768px)": {
          "layout.margins": { "left": 10, "right": 10 },
          "typography.bodySize": 10
        }
      }
    },
    "export": {
      "pdf": {
        "pageBreaks": "auto",
        "headerFooter": true,
        "printBackground": true
      },
      "word": {
        "compatibility": "Office365",
        "preserveFormatting": true
      }
    }
  }
}
```

### 3. Template Base Structure

```json
{
  "id": "template-{template-id}",
  "templateId": "00000000-0000-0000-0005-000000000001",
  "name": "Customer Satisfaction Report",
  "description": "Standard template for quarterly customer satisfaction analysis",
  "category": "Customer Analysis",
  "version": "1.0.0",
  "isSystemTemplate": false,
  "tenantId": "00000000-0000-0000-0003-000000000001",
  "createdAt": "2024-03-01T00:00:00Z",
  "updatedAt": "2024-03-01T00:00:00Z",
  "inheritance": {
    "allowModification": true,
    "lockSections": [],
    "requiredSections": ["exec-summary", "metrics"],
    "inheritanceRules": {
      "styles": "inherit", // inherit, override, merge
      "components": "merge",
      "layout": "inherit"
    }
  },
  "sections": [
    {
      "id": "exec-summary",
      "title": "Executive Summary",
      "type": "text",
      "order": 0,
      "isRequired": true,
      "isLocked": false,
      "defaultContent": {
        "heading": "Executive Summary",
        "content": "This report provides an overview of..."
      }
    },
    {
      "id": "metrics",
      "title": "Key Metrics",
      "type": "chart",
      "order": 1,
      "isRequired": true,
      "isLocked": false,
      "defaultContent": {
        "chartType": "bar",
        "dataStructure": {
          "labels": ["string[]"],
          "values": ["number[]"],
          "categories": ["string[]"]
        }
      }
    }
  ],
  "fields": [
    {
      "id": "period",
      "name": "Reporting Period",
      "type": "String",
      "sectionId": "exec-summary",
      "isRequired": true,
      "defaultValue": "",
      "validation": {
        "pattern": "^Q[1-4] \\d{4}$",
        "message": "Format: Q1 2024"
      }
    }
  ]
}
```

## BlobSeeder Implementation

### 1. Enhanced BlobSeeder Class

```csharp
public class BlobSeeder : IBlobSeeder
{
    private readonly IBlobStorageService? _blobStorageService;
    private readonly ILogger<BlobSeeder> _logger;
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Blob Storage seeding...");

        if (_blobStorageService == null)
        {
            _logger.LogWarning("Blob Storage service not available, skipping Blob Storage seeding");
            return;
        }

        try
        {
            // Phase 1: Seed system templates and default styles
            await SeedSystemTemplatesAsync(cancellationToken);
            await SeedDefaultStylesAsync(cancellationToken);
            
            // Phase 2: Seed tenant-specific templates
            await SeedTenantTemplatesAsync(context, cancellationToken);
            
            // Phase 3: Seed report version components and styles
            await SeedReportVersionBlobsAsync(context, cancellationToken);
            
            _logger.LogInformation("Blob Storage seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Blob Storage seeding");
            throw;
        }
    }

    private async Task SeedSystemTemplatesAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Seeding system templates to blob storage...");
        
        var systemTemplates = new[]
        {
            CreateSystemTemplate("corporate-standard", "Corporate Standard", "Standard corporate report template"),
            CreateSystemTemplate("healthcare-analysis", "Healthcare Analysis", "Healthcare industry analysis template"),
            CreateSystemTemplate("financial-quarterly", "Financial Quarterly", "Quarterly financial report template"),
            CreateSystemTemplate("customer-satisfaction", "Customer Satisfaction", "Customer feedback analysis template")
        };

        foreach (var template in systemTemplates)
        {
            var blobPath = $"shared/system-templates/{template.TemplateId}/base-template.json";
            var content = JsonSerializer.Serialize(template, JsonOptions);
            
            await _blobStorageService.UploadAsync(blobPath, content, "application/json", cancellationToken);
            _logger.LogDebug("Uploaded system template: {TemplateName}", template.Name);
        }
    }

    private async Task SeedDefaultStylesAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Seeding default styles to blob storage...");
        
        var defaultThemes = new[]
        {
            CreateDefaultTheme("corporate", "Corporate", "Professional corporate styling"),
            CreateDefaultTheme("modern", "Modern", "Contemporary modern styling"),
            CreateDefaultTheme("healthcare", "Healthcare", "Healthcare industry styling"),
            CreateDefaultTheme("financial", "Financial", "Financial services styling")
        };

        foreach (var theme in defaultThemes)
        {
            var blobPath = $"shared/default-styles/themes/{theme.Name.ToLower()}.json";
            var content = JsonSerializer.Serialize(theme, JsonOptions);
            
            await _blobStorageService.UploadAsync(blobPath, content, "application/json", cancellationToken);
            _logger.LogDebug("Uploaded default theme: {ThemeName}", theme.Name);
        }
    }

    private async Task SeedTenantTemplatesAsync(ApplicationDbContext context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Seeding tenant templates to blob storage...");
        
        var templates = await context.Templates
            .IgnoreQueryFilters()
            .ToListAsync(cancellationToken);

        foreach (var template in templates)
        {
            var tenantId = template.TenantId?.ToString() ?? "default";
            var templateBlob = CreateTemplateBlob(template);
            
            var blobPath = $"tenants/{tenantId}/templates/{template.Id}/base-template.json";
            var content = JsonSerializer.Serialize(templateBlob, JsonOptions);
            
            await _blobStorageService.UploadAsync(blobPath, content, "application/json", cancellationToken);
            _logger.LogDebug("Uploaded tenant template: {TemplateName} for tenant {TenantId}", template.Name, tenantId);
        }
    }

    private async Task SeedReportVersionBlobsAsync(ApplicationDbContext context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Seeding report version blobs to storage...");
        
        var reportVersions = await context.ReportVersions
            .IgnoreQueryFilters()
            .Include(rv => rv.Report)
            .Where(rv => rv.IsCurrent)
            .ToListAsync(cancellationToken);

        var createdCount = 0;
        var errorCount = 0;

        foreach (var version in reportVersions)
        {
            try
            {
                var tenantId = version.Report.TenantId?.ToString() ?? "default";
                
                // Create components blob
                var componentsBlob = await CreateComponentsBlobAsync(context, version, cancellationToken);
                var componentsBlobId = $"components-{version.ReportId}-v{version.Id}";
                var componentsBlobPath = $"tenants/{tenantId}/reports/{version.ReportId}/versions/v{version.VersionNumber}/components.json";
                
                await _blobStorageService.UploadAsync(componentsBlobPath, 
                    JsonSerializer.Serialize(componentsBlob, JsonOptions), 
                    "application/json", cancellationToken);
                
                // Create styles blob
                var stylesBlob = await CreateStylesBlobAsync(context, version, cancellationToken);
                var stylesBlobId = $"styles-{version.ReportId}-v{version.Id}";
                var stylesBlobPath = $"tenants/{tenantId}/reports/{version.ReportId}/versions/v{version.VersionNumber}/styles.json";
                
                await _blobStorageService.UploadAsync(stylesBlobPath, 
                    JsonSerializer.Serialize(stylesBlob, JsonOptions), 
                    "application/json", cancellationToken);
                
                // Update SQL with blob references
                await UpdateReportVersionWithBlobIdsAsync(context, version.Id, componentsBlobId, stylesBlobId, cancellationToken);
                
                createdCount++;
                _logger.LogDebug("Created blobs for report {ReportId} version {VersionNumber}", 
                    version.ReportId, version.VersionNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating blobs for report {ReportId} version {VersionNumber}", 
                    version.ReportId, version.VersionNumber);
                errorCount++;
            }
        }

        _logger.LogInformation("Report version blob seeding completed: {Created} created, {Errors} errors", 
            createdCount, errorCount);
    }

    private async Task<ComponentsBlob> CreateComponentsBlobAsync(ApplicationDbContext context, ReportVersion version, CancellationToken cancellationToken)
    {
        var sections = await context.ReportSections
            .IgnoreQueryFilters()
            .Where(rs => rs.ReportId == version.ReportId)
            .Include(rs => rs.Fields)
            .OrderBy(rs => rs.Order)
            .ToListAsync(cancellationToken);

        var components = sections.Select(section => new ComponentSection
        {
            Id = section.Id,
            Type = section.Type,
            Component = new ComponentDefinition
            {
                Name = $"{section.Title.Replace(" ", "")}Section",
                Props = CreateComponentProps(section),
                Jsx = GenerateComponentJsx(section),
                Dependencies = GetComponentDependencies(section.Type)
            },
            TemplateSourceId = section.TemplateSourceSectionId?.ToString(),
            IsModifiedFromTemplate = section.IsModifiedFromTemplate
        }).ToList();

        return new ComponentsBlob
        {
            Id = $"components-{version.ReportId}-v{version.Id}",
            ReportId = version.ReportId,
            VersionId = version.Id,
            VersionNumber = version.VersionNumber,
            TenantId = version.Report.TenantId?.ToString() ?? "default",
            CreatedAt = version.CreationTime,
            UpdatedAt = version.LastModificationTime ?? version.CreationTime,
            Components = new ComponentsData
            {
                Layout = new LayoutConfig
                {
                    Type = "standard",
                    Framework = "NextJS",
                    TypeScript = true,
                    StyleFramework = "TailwindCSS"
                },
                Sections = components,
                Metadata = new ComponentMetadata
                {
                    TotalComponents = components.Count,
                    Framework = "NextJS",
                    Dependencies = components.SelectMany(c => c.Component.Dependencies).Distinct().ToList(),
                    BuildVersion = "1.0.0",
                    LastBuilt = DateTime.UtcNow
                }
            }
        };
    }

    private async Task<StylesBlob> CreateStylesBlobAsync(ApplicationDbContext context, ReportVersion version, CancellationToken cancellationToken)
    {
        // Get report style if exists
        var reportStyle = await context.ReportStyles
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(rs => rs.ReportId == version.ReportId, cancellationToken);

        var stylesData = reportStyle != null ? 
            CreateStylesFromReportStyle(reportStyle) : 
            CreateDefaultStyles();

        return new StylesBlob
        {
            Id = $"styles-{version.ReportId}-v{version.Id}",
            ReportId = version.ReportId,
            VersionId = version.Id,
            VersionNumber = version.VersionNumber,
            TenantId = version.Report.TenantId?.ToString() ?? "default",
            CreatedAt = version.CreationTime,
            UpdatedAt = version.LastModificationTime ?? version.CreationTime,
            Styles = stylesData
        };
    }

    private async Task UpdateReportVersionWithBlobIdsAsync(ApplicationDbContext context, Guid versionId, 
        string componentsBlobId, string stylesBlobId, CancellationToken cancellationToken)
    {
        var version = await context.ReportVersions
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(v => v.Id == versionId, cancellationToken);

        if (version != null)
        {
            version.ComponentsBlobId = componentsBlobId;
            version.StylesBlobId = stylesBlobId;
            version.StorageStrategy = "MultiStorage"; // SQL + Cosmos DB + Blob Storage
            
            // Calculate sizes (placeholder - would be actual blob sizes)
            version.ComponentsSize = 2048; // Placeholder
            version.StylesSize = 1024; // Placeholder
            
            await context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_blobStorageService == null)
            {
                return new List<string>();
            }

            // List all blobs in the container
            var blobs = await _blobStorageService.ListBlobsAsync("", cancellationToken);
            return blobs.Select(b => Path.GetFileNameWithoutExtension(b)).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting existing blob IDs, returning empty list");
            return new List<string>();
        }
    }

    // Helper methods for creating blob data structures
    private SystemTemplate CreateSystemTemplate(string id, string name, string description)
    {
        return new SystemTemplate
        {
            Id = $"system-template-{id}",
            TemplateId = Guid.NewGuid(),
            Name = name,
            Description = description,
            Category = "System",
            Version = "1.0.0",
            IsSystemTemplate = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Inheritance = new TemplateInheritance
            {
                AllowModification = true,
                LockSections = new List<string>(),
                RequiredSections = new List<string> { "exec-summary" },
                InheritanceRules = new Dictionary<string, string>
                {
                    ["styles"] = "inherit",
                    ["components"] = "merge",
                    ["layout"] = "inherit"
                }
            },
            Sections = new List<TemplateSection>
            {
                new TemplateSection
                {
                    Id = "exec-summary",
                    Title = "Executive Summary",
                    Type = "text",
                    Order = 0,
                    IsRequired = true,
                    IsLocked = false,
                    DefaultContent = new Dictionary<string, object>
                    {
                        ["heading"] = "Executive Summary",
                        ["content"] = "This report provides an overview of..."
                    }
                }
            },
            Fields = new List<TemplateField>()
        };
    }

    private DefaultTheme CreateDefaultTheme(string id, string name, string description)
    {
        return new DefaultTheme
        {
            Id = id,
            Name = name,
            Description = description,
            Colors = new ThemeColors
            {
                Primary = "#2E86AB",
                Secondary = "#A23B72",
                Accent = "#F18F01",
                Background = "#FFFFFF",
                Border = "#E0E0E0",
                Text = new TextColors
                {
                    Primary = "#333333",
                    Secondary = "#666666",
                    Muted = "#999999"
                }
            },
            Typography = new ThemeTypography
            {
                PrimaryFont = "Arial",
                SecondaryFont = "Helvetica",
                HeadingSize = 18,
                BodySize = 12,
                LineHeight = 1.5
            },
            Layout = new ThemeLayout
            {
                PageSize = "A4",
                Orientation = "portrait",
                Margins = new Margins { Top = 20, Bottom = 20, Left = 15, Right = 15 }
            }
        };
    }

    // Additional helper methods would be implemented here...
}
```

## Blob Data Models

### Component Models

```csharp
public class ComponentsBlob
{
    public string Id { get; set; } = string.Empty;
    public Guid ReportId { get; set; }
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public string TenantId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? TemplateSourceId { get; set; }
    public bool IsModifiedFromTemplate { get; set; }
    public ComponentsData Components { get; set; } = new();
}

public class ComponentsData
{
    public LayoutConfig Layout { get; set; } = new();
    public List<ComponentSection> Sections { get; set; } = new();
    public ComponentMetadata Metadata { get; set; } = new();
}

public class ComponentSection
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public ComponentDefinition Component { get; set; } = new();
    public string? TemplateSourceId { get; set; }
    public bool IsModifiedFromTemplate { get; set; }
}

public class ComponentDefinition
{
    public string Name { get; set; } = string.Empty;
    public Dictionary<string, object> Props { get; set; } = new();
    public string Jsx { get; set; } = string.Empty;
    public List<string> Dependencies { get; set; } = new();
}
```

### Style Models

```csharp
public class StylesBlob
{
    public string Id { get; set; } = string.Empty;
    public Guid ReportId { get; set; }
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public string TenantId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? TemplateSourceId { get; set; }
    public bool IsModifiedFromTemplate { get; set; }
    public StylesData Styles { get; set; } = new();
}

public class StylesData
{
    public ThemeInfo Theme { get; set; } = new();
    public LayoutConfig Layout { get; set; } = new();
    public Typography Typography { get; set; } = new();
    public Colors Colors { get; set; } = new();
    public Dictionary<string, ComponentStyle> Components { get; set; } = new();
    public ResponsiveConfig Responsive { get; set; } = new();
    public ExportConfig Export { get; set; } = new();
}
```

## Implementation Benefits

### 1. Version Control
- **Immutable Versions**: Each report version has its own blob storage
- **Rollback Capability**: Easy to revert to previous versions
- **Change Tracking**: Clear audit trail of component and style changes

### 2. Template Inheritance
- **Source Tracking**: Track which template components/styles came from
- **Modification Detection**: Know what has been customized vs inherited
- **Update Propagation**: Ability to update templates and propagate changes

### 3. Multi-Tenant Isolation
- **Tenant Separation**: Clear tenant boundaries in blob storage
- **Shared Resources**: System templates and default styles shared across tenants
- **Custom Templates**: Tenant-specific template storage

### 4. Performance Optimization
- **CDN Ready**: Blob storage can be served via CDN
- **Caching**: Components and styles can be cached effectively
- **Lazy Loading**: Load only needed components/styles

### 5. Development Workflow
- **Draft Support**: Separate storage for draft vs published versions
- **Component Reuse**: Shared component library across reports
- **Style Consistency**: Centralized style management

This blob storage structure provides a robust foundation for the multi-storage architecture while supporting the draft-based editing workflow and template inheritance model.
